/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This is a barrel export file for all models and their related types.
 *
 * 🟢 You can import this file directly.
 */
export type * from './models/Class.js'
export type * from './models/Section.js'
export type * from './models/Subject.js'
export type * from './models/SubjectClass.js'
export type * from './models/SubjectTeacher.js'
export type * from './models/LoginLog.js'
export type * from './models/ActivityLog.js'
export type * from './models/Role.js'
export type * from './models/Permission.js'
export type * from './models/RolePermission.js'
export type * from './models/SchoolType.js'
export type * from './models/School.js'
export type * from './models/SchoolUser.js'
export type * from './models/Department.js'
export type * from './models/DepartmentStaff.js'
export type * from './models/Session.js'
export type * from './models/Token.js'
export type * from './models/User.js'
export type * from './models/Admin.js'
export type * from './models/Teacher.js'
export type * from './models/Staff.js'
export type * from './models/Student.js'
export type * from './models/Parent.js'
export type * from './commonInputTypes.js'
