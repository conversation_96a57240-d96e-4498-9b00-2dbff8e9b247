/* !!! This is code generated by <PERSON>risma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * All exports from this file are wrapped under a `Prisma` namespace object in the client.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 *
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective
 * model files in the `model` directory!
 */

import * as runtime from '@prisma/client/runtime/library'
import type * as Prisma from '../models.js'
import { type PrismaClient } from './class.js'

export type * from '../models.js'

export type DMMF = typeof runtime.DMMF

export type PrismaPromise<T> = runtime.Types.Public.PrismaPromise<T>

/**
 * Validator
 */
export const validator = runtime.Public.validator

/**
 * Prisma Errors
 */

export const PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
export type PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError

export const PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
export type PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError

export const PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
export type PrismaClientRustPanicError = runtime.PrismaClientRustPanicError

export const PrismaClientInitializationError = runtime.PrismaClientInitializationError
export type PrismaClientInitializationError = runtime.PrismaClientInitializationError

export const PrismaClientValidationError = runtime.PrismaClientValidationError
export type PrismaClientValidationError = runtime.PrismaClientValidationError

/**
 * Re-export of sql-template-tag
 */
export const sql = runtime.sqltag
export const empty = runtime.empty
export const join = runtime.join
export const raw = runtime.raw
export const Sql = runtime.Sql
export type Sql = runtime.Sql

/**
 * Decimal.js
 */
export const Decimal = runtime.Decimal
export type Decimal = runtime.Decimal

export type DecimalJsLike = runtime.DecimalJsLike

/**
 * Metrics
 */
export type Metrics = runtime.Metrics
export type Metric<T> = runtime.Metric<T>
export type MetricHistogram = runtime.MetricHistogram
export type MetricHistogramBucket = runtime.MetricHistogramBucket

/**
 * Extensions
 */
export type Extension = runtime.Types.Extensions.UserArgs
export const getExtensionContext = runtime.Extensions.getExtensionContext
export type Args<T, F extends runtime.Operation> = runtime.Types.Public.Args<T, F>
export type Payload<T, F extends runtime.Operation = never> = runtime.Types.Public.Payload<T, F>
export type Result<T, A, F extends runtime.Operation> = runtime.Types.Public.Result<T, A, F>
export type Exact<A, W> = runtime.Types.Public.Exact<A, W>

export type PrismaVersion = {
  client: string
  engine: string
}

/**
 * Prisma Client JS version: 6.14.0
 * Query Engine version: 717184b7b35ea05dfa71a3236b7af656013e1e49
 */
export const prismaVersion: PrismaVersion = {
  client: '6.14.0',
  engine: '717184b7b35ea05dfa71a3236b7af656013e1e49',
}

/**
 * Utility Types
 */

export type JsonObject = runtime.JsonObject
export type JsonArray = runtime.JsonArray
export type JsonValue = runtime.JsonValue
export type InputJsonObject = runtime.InputJsonObject
export type InputJsonArray = runtime.InputJsonArray
export type InputJsonValue = runtime.InputJsonValue

export const NullTypes = {
  DbNull: runtime.objectEnumValues.classes.DbNull as new (
    secret: never
  ) => typeof runtime.objectEnumValues.instances.DbNull,
  JsonNull: runtime.objectEnumValues.classes.JsonNull as new (
    secret: never
  ) => typeof runtime.objectEnumValues.instances.JsonNull,
  AnyNull: runtime.objectEnumValues.classes.AnyNull as new (
    secret: never
  ) => typeof runtime.objectEnumValues.instances.AnyNull,
}

/**
 * Helper for filtering JSON entries that have `null` on the database (empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const DbNull = runtime.objectEnumValues.instances.DbNull

/**
 * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const JsonNull = runtime.objectEnumValues.instances.JsonNull

/**
 * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const AnyNull = runtime.objectEnumValues.instances.AnyNull

type SelectAndInclude = {
  select: any
  include: any
}

type SelectAndOmit = {
  select: any
  omit: any
}

/**
 * From T, pick a set of properties whose keys are in the union K
 */
type Prisma__Pick<T, K extends keyof T> = {
  [P in K]: T[P]
}

export type Enumerable<T> = T | Array<T>

/**
 * Subset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
 */
export type Subset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
}

/**
 * SelectSubset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
 * Additionally, it validates, if both select and include are present. If the case, it errors.
 */
export type SelectSubset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} & (T extends SelectAndInclude
  ? 'Please either choose `select` or `include`.'
  : T extends SelectAndOmit
    ? 'Please either choose `select` or `omit`.'
    : {})

/**
 * Subset + Intersection
 * @desc From `T` pick properties that exist in `U` and intersect `K`
 */
export type SubsetIntersection<T, U, K> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} & K

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never }

/**
 * XOR is needed to have a real mutually exclusive union type
 * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
 */
export type XOR<T, U> = T extends object
  ? U extends object
    ? (Without<T, U> & U) | (Without<U, T> & T)
    : U
  : T

/**
 * Is T a Record?
 */
type IsObject<T extends any> =
  T extends Array<any>
    ? False
    : T extends Date
      ? False
      : T extends Uint8Array
        ? False
        : T extends BigInt
          ? False
          : T extends object
            ? True
            : False

/**
 * If it's T[], return T
 */
export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

/**
 * From ts-toolbelt
 */

type __Either<O extends object, K extends Key> = Omit<O, K> &
  {
    // Merge all but K
    [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
  }[K]

type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

type _Either<O extends object, K extends Key, strict extends Boolean> = {
  1: EitherStrict<O, K>
  0: EitherLoose<O, K>
}[strict]

export type Either<O extends object, K extends Key, strict extends Boolean = 1> = O extends unknown
  ? _Either<O, K, strict>
  : never

export type Union = any

export type PatchUndefined<O extends object, O1 extends object> = {
  [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
} & {}

/** Helper Types for "Merge" **/
export type IntersectOf<U extends Union> = (U extends unknown ? (k: U) => void : never) extends (
  k: infer I
) => void
  ? I
  : never

export type Overwrite<O extends object, O1 extends object> = {
  [K in keyof O]: K extends keyof O1 ? O1[K] : O[K]
} & {}

type _Merge<U extends object> = IntersectOf<
  Overwrite<
    U,
    {
      [K in keyof U]-?: At<U, K>
    }
  >
>

type Key = string | number | symbol
type AtStrict<O extends object, K extends Key> = O[K & keyof O]
type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never
export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
  1: AtStrict<O, K>
  0: AtLoose<O, K>
}[strict]

export type ComputeRaw<A extends any> = A extends Function
  ? A
  : {
      [K in keyof A]: A[K]
    } & {}

export type OptionalFlat<O> = {
  [K in keyof O]?: O[K]
} & {}

type _Record<K extends keyof any, T> = {
  [P in K]: T
}

// cause typescript not to expand types and preserve names
type NoExpand<T> = T extends unknown ? T : never

// this type assumes the passed object is entirely optional
export type AtLeast<O extends object, K extends string> = NoExpand<
  O extends unknown
    ?
        | (K extends keyof O ? { [P in K]: O[P] } & O : O)
        | ({ [P in keyof O as P extends K ? P : never]-?: O[P] } & O)
    : never
>

type _Strict<U, _U = U> = U extends unknown
  ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>>
  : never

export type Strict<U extends object> = ComputeRaw<_Strict<U>>
/** End Helper Types for "Merge" **/

export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>

export type Boolean = True | False

export type True = 1

export type False = 0

export type Not<B extends Boolean> = {
  0: 1
  1: 0
}[B]

export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
  ? 0 // anything `never` is false
  : A1 extends A2
    ? 1
    : 0

export type Has<U extends Union, U1 extends Union> = Not<Extends<Exclude<U1, U>, U1>>

export type Or<B1 extends Boolean, B2 extends Boolean> = {
  0: {
    0: 0
    1: 1
  }
  1: {
    0: 1
    1: 1
  }
}[B1][B2]

export type Keys<U extends Union> = U extends unknown ? keyof U : never

export type GetScalarType<T, O> = O extends object
  ? {
      [P in keyof T]: P extends keyof O ? O[P] : never
    }
  : never

type FieldPaths<T, U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>> =
  IsObject<T> extends True ? U : T

export type GetHavingFields<T> = {
  [K in keyof T]: Or<Or<Extends<'OR', K>, Extends<'AND', K>>, Extends<'NOT', K>> extends True
    ? // infer is only needed to not hit TS limit
      // based on the brilliant idea of Pierre-Antoine Mills
      // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
      T[K] extends infer TK
      ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
      : never
    : {} extends FieldPaths<T[K]>
      ? never
      : K
}[keyof T]

/**
 * Convert tuple to union
 */
type _TupleToUnion<T> = T extends (infer E)[] ? E : never
type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
export type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

/**
 * Like `Pick`, but additionally can also accept an array of keys
 */
export type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<
  T,
  MaybeTupleToUnion<K>
>

/**
 * Exclude all keys with underscores
 */
export type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T

export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>

export const ModelName = {
  Class: 'Class',
  Section: 'Section',
  Subject: 'Subject',
  SubjectClass: 'SubjectClass',
  SubjectTeacher: 'SubjectTeacher',
  LoginLog: 'LoginLog',
  ActivityLog: 'ActivityLog',
  Role: 'Role',
  Permission: 'Permission',
  RolePermission: 'RolePermission',
  SchoolType: 'SchoolType',
  School: 'School',
  SchoolUser: 'SchoolUser',
  Department: 'Department',
  DepartmentStaff: 'DepartmentStaff',
  Session: 'Session',
  Token: 'Token',
  User: 'User',
  Admin: 'Admin',
  Teacher: 'Teacher',
  Staff: 'Staff',
  Student: 'Student',
  Parent: 'Parent',
} as const

export type ModelName = (typeof ModelName)[keyof typeof ModelName]

export interface TypeMapCb<GlobalOmitOptions = {}>
  extends runtime.Types.Utils.Fn<
    { extArgs: runtime.Types.Extensions.InternalArgs },
    runtime.Types.Utils.Record<string, any>
  > {
  returns: TypeMap<this['params']['extArgs'], GlobalOmitOptions>
}

export type TypeMap<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> = {
  globalOmitOptions: {
    omit: GlobalOmitOptions
  }
  meta: {
    modelProps:
      | 'class'
      | 'section'
      | 'subject'
      | 'subjectClass'
      | 'subjectTeacher'
      | 'loginLog'
      | 'activityLog'
      | 'role'
      | 'permission'
      | 'rolePermission'
      | 'schoolType'
      | 'school'
      | 'schoolUser'
      | 'department'
      | 'departmentStaff'
      | 'session'
      | 'token'
      | 'user'
      | 'admin'
      | 'teacher'
      | 'staff'
      | 'student'
      | 'parent'
    txIsolationLevel: TransactionIsolationLevel
  }
  model: {
    Class: {
      payload: Prisma.$ClassPayload<ExtArgs>
      fields: Prisma.ClassFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ClassFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ClassFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>
        }
        findFirst: {
          args: Prisma.ClassFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ClassFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>
        }
        findMany: {
          args: Prisma.ClassFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>[]
        }
        create: {
          args: Prisma.ClassCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>
        }
        createMany: {
          args: Prisma.ClassCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ClassCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>[]
        }
        delete: {
          args: Prisma.ClassDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>
        }
        update: {
          args: Prisma.ClassUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>
        }
        deleteMany: {
          args: Prisma.ClassDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ClassUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ClassUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>[]
        }
        upsert: {
          args: Prisma.ClassUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClassPayload>
        }
        aggregate: {
          args: Prisma.ClassAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateClass>
        }
        groupBy: {
          args: Prisma.ClassGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ClassGroupByOutputType>[]
        }
        count: {
          args: Prisma.ClassCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ClassCountAggregateOutputType> | number
        }
      }
    }
    Section: {
      payload: Prisma.$SectionPayload<ExtArgs>
      fields: Prisma.SectionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SectionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SectionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>
        }
        findFirst: {
          args: Prisma.SectionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SectionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>
        }
        findMany: {
          args: Prisma.SectionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>[]
        }
        create: {
          args: Prisma.SectionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>
        }
        createMany: {
          args: Prisma.SectionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SectionCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>[]
        }
        delete: {
          args: Prisma.SectionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>
        }
        update: {
          args: Prisma.SectionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>
        }
        deleteMany: {
          args: Prisma.SectionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SectionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SectionUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>[]
        }
        upsert: {
          args: Prisma.SectionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SectionPayload>
        }
        aggregate: {
          args: Prisma.SectionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSection>
        }
        groupBy: {
          args: Prisma.SectionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SectionGroupByOutputType>[]
        }
        count: {
          args: Prisma.SectionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SectionCountAggregateOutputType> | number
        }
      }
    }
    Subject: {
      payload: Prisma.$SubjectPayload<ExtArgs>
      fields: Prisma.SubjectFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SubjectFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SubjectFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>
        }
        findFirst: {
          args: Prisma.SubjectFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SubjectFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>
        }
        findMany: {
          args: Prisma.SubjectFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>[]
        }
        create: {
          args: Prisma.SubjectCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>
        }
        createMany: {
          args: Prisma.SubjectCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SubjectCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>[]
        }
        delete: {
          args: Prisma.SubjectDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>
        }
        update: {
          args: Prisma.SubjectUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>
        }
        deleteMany: {
          args: Prisma.SubjectDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SubjectUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SubjectUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>[]
        }
        upsert: {
          args: Prisma.SubjectUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectPayload>
        }
        aggregate: {
          args: Prisma.SubjectAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSubject>
        }
        groupBy: {
          args: Prisma.SubjectGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SubjectGroupByOutputType>[]
        }
        count: {
          args: Prisma.SubjectCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SubjectCountAggregateOutputType> | number
        }
      }
    }
    SubjectClass: {
      payload: Prisma.$SubjectClassPayload<ExtArgs>
      fields: Prisma.SubjectClassFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SubjectClassFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SubjectClassFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>
        }
        findFirst: {
          args: Prisma.SubjectClassFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SubjectClassFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>
        }
        findMany: {
          args: Prisma.SubjectClassFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>[]
        }
        create: {
          args: Prisma.SubjectClassCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>
        }
        createMany: {
          args: Prisma.SubjectClassCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SubjectClassCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>[]
        }
        delete: {
          args: Prisma.SubjectClassDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>
        }
        update: {
          args: Prisma.SubjectClassUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>
        }
        deleteMany: {
          args: Prisma.SubjectClassDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SubjectClassUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SubjectClassUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>[]
        }
        upsert: {
          args: Prisma.SubjectClassUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectClassPayload>
        }
        aggregate: {
          args: Prisma.SubjectClassAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSubjectClass>
        }
        groupBy: {
          args: Prisma.SubjectClassGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SubjectClassGroupByOutputType>[]
        }
        count: {
          args: Prisma.SubjectClassCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SubjectClassCountAggregateOutputType> | number
        }
      }
    }
    SubjectTeacher: {
      payload: Prisma.$SubjectTeacherPayload<ExtArgs>
      fields: Prisma.SubjectTeacherFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SubjectTeacherFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SubjectTeacherFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>
        }
        findFirst: {
          args: Prisma.SubjectTeacherFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SubjectTeacherFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>
        }
        findMany: {
          args: Prisma.SubjectTeacherFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>[]
        }
        create: {
          args: Prisma.SubjectTeacherCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>
        }
        createMany: {
          args: Prisma.SubjectTeacherCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SubjectTeacherCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>[]
        }
        delete: {
          args: Prisma.SubjectTeacherDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>
        }
        update: {
          args: Prisma.SubjectTeacherUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>
        }
        deleteMany: {
          args: Prisma.SubjectTeacherDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SubjectTeacherUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SubjectTeacherUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>[]
        }
        upsert: {
          args: Prisma.SubjectTeacherUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SubjectTeacherPayload>
        }
        aggregate: {
          args: Prisma.SubjectTeacherAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSubjectTeacher>
        }
        groupBy: {
          args: Prisma.SubjectTeacherGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SubjectTeacherGroupByOutputType>[]
        }
        count: {
          args: Prisma.SubjectTeacherCountArgs<ExtArgs>
          result:
            | runtime.Types.Utils.Optional<Prisma.SubjectTeacherCountAggregateOutputType>
            | number
        }
      }
    }
    LoginLog: {
      payload: Prisma.$LoginLogPayload<ExtArgs>
      fields: Prisma.LoginLogFieldRefs
      operations: {
        findUnique: {
          args: Prisma.LoginLogFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.LoginLogFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>
        }
        findFirst: {
          args: Prisma.LoginLogFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.LoginLogFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>
        }
        findMany: {
          args: Prisma.LoginLogFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>[]
        }
        create: {
          args: Prisma.LoginLogCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>
        }
        createMany: {
          args: Prisma.LoginLogCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.LoginLogCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>[]
        }
        delete: {
          args: Prisma.LoginLogDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>
        }
        update: {
          args: Prisma.LoginLogUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>
        }
        deleteMany: {
          args: Prisma.LoginLogDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.LoginLogUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.LoginLogUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>[]
        }
        upsert: {
          args: Prisma.LoginLogUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LoginLogPayload>
        }
        aggregate: {
          args: Prisma.LoginLogAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateLoginLog>
        }
        groupBy: {
          args: Prisma.LoginLogGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LoginLogGroupByOutputType>[]
        }
        count: {
          args: Prisma.LoginLogCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LoginLogCountAggregateOutputType> | number
        }
      }
    }
    ActivityLog: {
      payload: Prisma.$ActivityLogPayload<ExtArgs>
      fields: Prisma.ActivityLogFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ActivityLogFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ActivityLogFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>
        }
        findFirst: {
          args: Prisma.ActivityLogFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ActivityLogFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>
        }
        findMany: {
          args: Prisma.ActivityLogFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>[]
        }
        create: {
          args: Prisma.ActivityLogCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>
        }
        createMany: {
          args: Prisma.ActivityLogCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ActivityLogCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>[]
        }
        delete: {
          args: Prisma.ActivityLogDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>
        }
        update: {
          args: Prisma.ActivityLogUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>
        }
        deleteMany: {
          args: Prisma.ActivityLogDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ActivityLogUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ActivityLogUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>[]
        }
        upsert: {
          args: Prisma.ActivityLogUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ActivityLogPayload>
        }
        aggregate: {
          args: Prisma.ActivityLogAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateActivityLog>
        }
        groupBy: {
          args: Prisma.ActivityLogGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ActivityLogGroupByOutputType>[]
        }
        count: {
          args: Prisma.ActivityLogCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ActivityLogCountAggregateOutputType> | number
        }
      }
    }
    Role: {
      payload: Prisma.$RolePayload<ExtArgs>
      fields: Prisma.RoleFieldRefs
      operations: {
        findUnique: {
          args: Prisma.RoleFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.RoleFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        findFirst: {
          args: Prisma.RoleFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.RoleFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        findMany: {
          args: Prisma.RoleFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>[]
        }
        create: {
          args: Prisma.RoleCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        createMany: {
          args: Prisma.RoleCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.RoleCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>[]
        }
        delete: {
          args: Prisma.RoleDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        update: {
          args: Prisma.RoleUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        deleteMany: {
          args: Prisma.RoleDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.RoleUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.RoleUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>[]
        }
        upsert: {
          args: Prisma.RoleUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        aggregate: {
          args: Prisma.RoleAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRole>
        }
        groupBy: {
          args: Prisma.RoleGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RoleGroupByOutputType>[]
        }
        count: {
          args: Prisma.RoleCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RoleCountAggregateOutputType> | number
        }
      }
    }
    Permission: {
      payload: Prisma.$PermissionPayload<ExtArgs>
      fields: Prisma.PermissionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.PermissionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.PermissionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>
        }
        findFirst: {
          args: Prisma.PermissionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.PermissionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>
        }
        findMany: {
          args: Prisma.PermissionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>[]
        }
        create: {
          args: Prisma.PermissionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>
        }
        createMany: {
          args: Prisma.PermissionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.PermissionCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>[]
        }
        delete: {
          args: Prisma.PermissionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>
        }
        update: {
          args: Prisma.PermissionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>
        }
        deleteMany: {
          args: Prisma.PermissionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.PermissionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.PermissionUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>[]
        }
        upsert: {
          args: Prisma.PermissionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PermissionPayload>
        }
        aggregate: {
          args: Prisma.PermissionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePermission>
        }
        groupBy: {
          args: Prisma.PermissionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PermissionGroupByOutputType>[]
        }
        count: {
          args: Prisma.PermissionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PermissionCountAggregateOutputType> | number
        }
      }
    }
    RolePermission: {
      payload: Prisma.$RolePermissionPayload<ExtArgs>
      fields: Prisma.RolePermissionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.RolePermissionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.RolePermissionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>
        }
        findFirst: {
          args: Prisma.RolePermissionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.RolePermissionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>
        }
        findMany: {
          args: Prisma.RolePermissionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>[]
        }
        create: {
          args: Prisma.RolePermissionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>
        }
        createMany: {
          args: Prisma.RolePermissionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.RolePermissionCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>[]
        }
        delete: {
          args: Prisma.RolePermissionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>
        }
        update: {
          args: Prisma.RolePermissionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>
        }
        deleteMany: {
          args: Prisma.RolePermissionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.RolePermissionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.RolePermissionUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>[]
        }
        upsert: {
          args: Prisma.RolePermissionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePermissionPayload>
        }
        aggregate: {
          args: Prisma.RolePermissionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRolePermission>
        }
        groupBy: {
          args: Prisma.RolePermissionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RolePermissionGroupByOutputType>[]
        }
        count: {
          args: Prisma.RolePermissionCountArgs<ExtArgs>
          result:
            | runtime.Types.Utils.Optional<Prisma.RolePermissionCountAggregateOutputType>
            | number
        }
      }
    }
    SchoolType: {
      payload: Prisma.$SchoolTypePayload<ExtArgs>
      fields: Prisma.SchoolTypeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SchoolTypeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SchoolTypeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>
        }
        findFirst: {
          args: Prisma.SchoolTypeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SchoolTypeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>
        }
        findMany: {
          args: Prisma.SchoolTypeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>[]
        }
        create: {
          args: Prisma.SchoolTypeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>
        }
        createMany: {
          args: Prisma.SchoolTypeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SchoolTypeCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>[]
        }
        delete: {
          args: Prisma.SchoolTypeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>
        }
        update: {
          args: Prisma.SchoolTypeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>
        }
        deleteMany: {
          args: Prisma.SchoolTypeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SchoolTypeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SchoolTypeUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>[]
        }
        upsert: {
          args: Prisma.SchoolTypeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolTypePayload>
        }
        aggregate: {
          args: Prisma.SchoolTypeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSchoolType>
        }
        groupBy: {
          args: Prisma.SchoolTypeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SchoolTypeGroupByOutputType>[]
        }
        count: {
          args: Prisma.SchoolTypeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SchoolTypeCountAggregateOutputType> | number
        }
      }
    }
    School: {
      payload: Prisma.$SchoolPayload<ExtArgs>
      fields: Prisma.SchoolFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SchoolFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SchoolFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>
        }
        findFirst: {
          args: Prisma.SchoolFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SchoolFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>
        }
        findMany: {
          args: Prisma.SchoolFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>[]
        }
        create: {
          args: Prisma.SchoolCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>
        }
        createMany: {
          args: Prisma.SchoolCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SchoolCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>[]
        }
        delete: {
          args: Prisma.SchoolDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>
        }
        update: {
          args: Prisma.SchoolUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>
        }
        deleteMany: {
          args: Prisma.SchoolDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SchoolUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SchoolUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>[]
        }
        upsert: {
          args: Prisma.SchoolUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolPayload>
        }
        aggregate: {
          args: Prisma.SchoolAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSchool>
        }
        groupBy: {
          args: Prisma.SchoolGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SchoolGroupByOutputType>[]
        }
        count: {
          args: Prisma.SchoolCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SchoolCountAggregateOutputType> | number
        }
      }
    }
    SchoolUser: {
      payload: Prisma.$SchoolUserPayload<ExtArgs>
      fields: Prisma.SchoolUserFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SchoolUserFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SchoolUserFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>
        }
        findFirst: {
          args: Prisma.SchoolUserFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SchoolUserFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>
        }
        findMany: {
          args: Prisma.SchoolUserFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>[]
        }
        create: {
          args: Prisma.SchoolUserCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>
        }
        createMany: {
          args: Prisma.SchoolUserCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SchoolUserCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>[]
        }
        delete: {
          args: Prisma.SchoolUserDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>
        }
        update: {
          args: Prisma.SchoolUserUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>
        }
        deleteMany: {
          args: Prisma.SchoolUserDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SchoolUserUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SchoolUserUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>[]
        }
        upsert: {
          args: Prisma.SchoolUserUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SchoolUserPayload>
        }
        aggregate: {
          args: Prisma.SchoolUserAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSchoolUser>
        }
        groupBy: {
          args: Prisma.SchoolUserGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SchoolUserGroupByOutputType>[]
        }
        count: {
          args: Prisma.SchoolUserCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SchoolUserCountAggregateOutputType> | number
        }
      }
    }
    Department: {
      payload: Prisma.$DepartmentPayload<ExtArgs>
      fields: Prisma.DepartmentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.DepartmentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.DepartmentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>
        }
        findFirst: {
          args: Prisma.DepartmentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.DepartmentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>
        }
        findMany: {
          args: Prisma.DepartmentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>[]
        }
        create: {
          args: Prisma.DepartmentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>
        }
        createMany: {
          args: Prisma.DepartmentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.DepartmentCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>[]
        }
        delete: {
          args: Prisma.DepartmentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>
        }
        update: {
          args: Prisma.DepartmentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>
        }
        deleteMany: {
          args: Prisma.DepartmentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.DepartmentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.DepartmentUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>[]
        }
        upsert: {
          args: Prisma.DepartmentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentPayload>
        }
        aggregate: {
          args: Prisma.DepartmentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateDepartment>
        }
        groupBy: {
          args: Prisma.DepartmentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.DepartmentGroupByOutputType>[]
        }
        count: {
          args: Prisma.DepartmentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.DepartmentCountAggregateOutputType> | number
        }
      }
    }
    DepartmentStaff: {
      payload: Prisma.$DepartmentStaffPayload<ExtArgs>
      fields: Prisma.DepartmentStaffFieldRefs
      operations: {
        findUnique: {
          args: Prisma.DepartmentStaffFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.DepartmentStaffFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>
        }
        findFirst: {
          args: Prisma.DepartmentStaffFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.DepartmentStaffFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>
        }
        findMany: {
          args: Prisma.DepartmentStaffFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>[]
        }
        create: {
          args: Prisma.DepartmentStaffCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>
        }
        createMany: {
          args: Prisma.DepartmentStaffCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.DepartmentStaffCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>[]
        }
        delete: {
          args: Prisma.DepartmentStaffDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>
        }
        update: {
          args: Prisma.DepartmentStaffUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>
        }
        deleteMany: {
          args: Prisma.DepartmentStaffDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.DepartmentStaffUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.DepartmentStaffUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>[]
        }
        upsert: {
          args: Prisma.DepartmentStaffUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DepartmentStaffPayload>
        }
        aggregate: {
          args: Prisma.DepartmentStaffAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateDepartmentStaff>
        }
        groupBy: {
          args: Prisma.DepartmentStaffGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.DepartmentStaffGroupByOutputType>[]
        }
        count: {
          args: Prisma.DepartmentStaffCountArgs<ExtArgs>
          result:
            | runtime.Types.Utils.Optional<Prisma.DepartmentStaffCountAggregateOutputType>
            | number
        }
      }
    }
    Session: {
      payload: Prisma.$SessionPayload<ExtArgs>
      fields: Prisma.SessionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SessionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SessionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        findFirst: {
          args: Prisma.SessionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SessionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        findMany: {
          args: Prisma.SessionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>[]
        }
        create: {
          args: Prisma.SessionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        createMany: {
          args: Prisma.SessionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.SessionCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>[]
        }
        delete: {
          args: Prisma.SessionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        update: {
          args: Prisma.SessionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        deleteMany: {
          args: Prisma.SessionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SessionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.SessionUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>[]
        }
        upsert: {
          args: Prisma.SessionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        aggregate: {
          args: Prisma.SessionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSession>
        }
        groupBy: {
          args: Prisma.SessionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SessionGroupByOutputType>[]
        }
        count: {
          args: Prisma.SessionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SessionCountAggregateOutputType> | number
        }
      }
    }
    Token: {
      payload: Prisma.$TokenPayload<ExtArgs>
      fields: Prisma.TokenFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TokenFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TokenFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>
        }
        findFirst: {
          args: Prisma.TokenFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TokenFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>
        }
        findMany: {
          args: Prisma.TokenFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>[]
        }
        create: {
          args: Prisma.TokenCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>
        }
        createMany: {
          args: Prisma.TokenCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TokenCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>[]
        }
        delete: {
          args: Prisma.TokenDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>
        }
        update: {
          args: Prisma.TokenUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>
        }
        deleteMany: {
          args: Prisma.TokenDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TokenUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TokenUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>[]
        }
        upsert: {
          args: Prisma.TokenUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TokenPayload>
        }
        aggregate: {
          args: Prisma.TokenAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateToken>
        }
        groupBy: {
          args: Prisma.TokenGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TokenGroupByOutputType>[]
        }
        count: {
          args: Prisma.TokenCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TokenCountAggregateOutputType> | number
        }
      }
    }
    User: {
      payload: Prisma.$UserPayload<ExtArgs>
      fields: Prisma.UserFieldRefs
      operations: {
        findUnique: {
          args: Prisma.UserFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        findFirst: {
          args: Prisma.UserFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        findMany: {
          args: Prisma.UserFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>[]
        }
        create: {
          args: Prisma.UserCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        createMany: {
          args: Prisma.UserCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>[]
        }
        delete: {
          args: Prisma.UserDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        update: {
          args: Prisma.UserUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        deleteMany: {
          args: Prisma.UserDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.UserUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>[]
        }
        upsert: {
          args: Prisma.UserUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        aggregate: {
          args: Prisma.UserAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser>
        }
        groupBy: {
          args: Prisma.UserGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UserGroupByOutputType>[]
        }
        count: {
          args: Prisma.UserCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UserCountAggregateOutputType> | number
        }
      }
    }
    Admin: {
      payload: Prisma.$AdminPayload<ExtArgs>
      fields: Prisma.AdminFieldRefs
      operations: {
        findUnique: {
          args: Prisma.AdminFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.AdminFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>
        }
        findFirst: {
          args: Prisma.AdminFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.AdminFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>
        }
        findMany: {
          args: Prisma.AdminFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>[]
        }
        create: {
          args: Prisma.AdminCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>
        }
        createMany: {
          args: Prisma.AdminCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.AdminCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>[]
        }
        delete: {
          args: Prisma.AdminDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>
        }
        update: {
          args: Prisma.AdminUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>
        }
        deleteMany: {
          args: Prisma.AdminDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.AdminUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.AdminUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>[]
        }
        upsert: {
          args: Prisma.AdminUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AdminPayload>
        }
        aggregate: {
          args: Prisma.AdminAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAdmin>
        }
        groupBy: {
          args: Prisma.AdminGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AdminGroupByOutputType>[]
        }
        count: {
          args: Prisma.AdminCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AdminCountAggregateOutputType> | number
        }
      }
    }
    Teacher: {
      payload: Prisma.$TeacherPayload<ExtArgs>
      fields: Prisma.TeacherFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TeacherFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TeacherFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>
        }
        findFirst: {
          args: Prisma.TeacherFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TeacherFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>
        }
        findMany: {
          args: Prisma.TeacherFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>[]
        }
        create: {
          args: Prisma.TeacherCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>
        }
        createMany: {
          args: Prisma.TeacherCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.TeacherCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>[]
        }
        delete: {
          args: Prisma.TeacherDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>
        }
        update: {
          args: Prisma.TeacherUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>
        }
        deleteMany: {
          args: Prisma.TeacherDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TeacherUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.TeacherUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>[]
        }
        upsert: {
          args: Prisma.TeacherUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TeacherPayload>
        }
        aggregate: {
          args: Prisma.TeacherAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTeacher>
        }
        groupBy: {
          args: Prisma.TeacherGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TeacherGroupByOutputType>[]
        }
        count: {
          args: Prisma.TeacherCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TeacherCountAggregateOutputType> | number
        }
      }
    }
    Staff: {
      payload: Prisma.$StaffPayload<ExtArgs>
      fields: Prisma.StaffFieldRefs
      operations: {
        findUnique: {
          args: Prisma.StaffFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.StaffFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>
        }
        findFirst: {
          args: Prisma.StaffFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.StaffFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>
        }
        findMany: {
          args: Prisma.StaffFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>[]
        }
        create: {
          args: Prisma.StaffCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>
        }
        createMany: {
          args: Prisma.StaffCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.StaffCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>[]
        }
        delete: {
          args: Prisma.StaffDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>
        }
        update: {
          args: Prisma.StaffUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>
        }
        deleteMany: {
          args: Prisma.StaffDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.StaffUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.StaffUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>[]
        }
        upsert: {
          args: Prisma.StaffUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StaffPayload>
        }
        aggregate: {
          args: Prisma.StaffAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateStaff>
        }
        groupBy: {
          args: Prisma.StaffGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.StaffGroupByOutputType>[]
        }
        count: {
          args: Prisma.StaffCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.StaffCountAggregateOutputType> | number
        }
      }
    }
    Student: {
      payload: Prisma.$StudentPayload<ExtArgs>
      fields: Prisma.StudentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.StudentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.StudentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>
        }
        findFirst: {
          args: Prisma.StudentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.StudentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>
        }
        findMany: {
          args: Prisma.StudentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>[]
        }
        create: {
          args: Prisma.StudentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>
        }
        createMany: {
          args: Prisma.StudentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.StudentCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>[]
        }
        delete: {
          args: Prisma.StudentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>
        }
        update: {
          args: Prisma.StudentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>
        }
        deleteMany: {
          args: Prisma.StudentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.StudentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.StudentUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>[]
        }
        upsert: {
          args: Prisma.StudentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$StudentPayload>
        }
        aggregate: {
          args: Prisma.StudentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateStudent>
        }
        groupBy: {
          args: Prisma.StudentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.StudentGroupByOutputType>[]
        }
        count: {
          args: Prisma.StudentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.StudentCountAggregateOutputType> | number
        }
      }
    }
    Parent: {
      payload: Prisma.$ParentPayload<ExtArgs>
      fields: Prisma.ParentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ParentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ParentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>
        }
        findFirst: {
          args: Prisma.ParentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ParentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>
        }
        findMany: {
          args: Prisma.ParentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>[]
        }
        create: {
          args: Prisma.ParentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>
        }
        createMany: {
          args: Prisma.ParentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        createManyAndReturn: {
          args: Prisma.ParentCreateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>[]
        }
        delete: {
          args: Prisma.ParentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>
        }
        update: {
          args: Prisma.ParentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>
        }
        deleteMany: {
          args: Prisma.ParentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ParentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateManyAndReturn: {
          args: Prisma.ParentUpdateManyAndReturnArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>[]
        }
        upsert: {
          args: Prisma.ParentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ParentPayload>
        }
        aggregate: {
          args: Prisma.ParentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateParent>
        }
        groupBy: {
          args: Prisma.ParentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ParentGroupByOutputType>[]
        }
        count: {
          args: Prisma.ParentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ParentCountAggregateOutputType> | number
        }
      }
    }
  }
} & {
  other: {
    payload: any
    operations: {
      $executeRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]]
        result: any
      }
      $executeRawUnsafe: {
        args: [query: string, ...values: any[]]
        result: any
      }
      $queryRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]]
        result: any
      }
      $queryRawUnsafe: {
        args: [query: string, ...values: any[]]
        result: any
      }
    }
  }
}

/**
 * Enums
 */

export const TransactionIsolationLevel = runtime.makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable',
} as const)

export type TransactionIsolationLevel =
  (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]

export const ClassScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  schoolId: 'schoolId',
} as const

export type ClassScalarFieldEnum = (typeof ClassScalarFieldEnum)[keyof typeof ClassScalarFieldEnum]

export const SectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  classId: 'classId',
} as const

export type SectionScalarFieldEnum =
  (typeof SectionScalarFieldEnum)[keyof typeof SectionScalarFieldEnum]

export const SubjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  code: 'code',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  schoolId: 'schoolId',
} as const

export type SubjectScalarFieldEnum =
  (typeof SubjectScalarFieldEnum)[keyof typeof SubjectScalarFieldEnum]

export const SubjectClassScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  subjectId: 'subjectId',
  classId: 'classId',
} as const

export type SubjectClassScalarFieldEnum =
  (typeof SubjectClassScalarFieldEnum)[keyof typeof SubjectClassScalarFieldEnum]

export const SubjectTeacherScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  subjectId: 'subjectId',
  teacherId: 'teacherId',
} as const

export type SubjectTeacherScalarFieldEnum =
  (typeof SubjectTeacherScalarFieldEnum)[keyof typeof SubjectTeacherScalarFieldEnum]

export const LoginLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  loginAt: 'loginAt',
  logoutAt: 'logoutAt',
} as const

export type LoginLogScalarFieldEnum =
  (typeof LoginLogScalarFieldEnum)[keyof typeof LoginLogScalarFieldEnum]

export const ActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  resource: 'resource',
  details: 'details',
  createdAt: 'createdAt',
} as const

export type ActivityLogScalarFieldEnum =
  (typeof ActivityLogScalarFieldEnum)[keyof typeof ActivityLogScalarFieldEnum]

export const RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
} as const

export type RoleScalarFieldEnum = (typeof RoleScalarFieldEnum)[keyof typeof RoleScalarFieldEnum]

export const PermissionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  resource: 'resource',
  action: 'action',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
} as const

export type PermissionScalarFieldEnum =
  (typeof PermissionScalarFieldEnum)[keyof typeof PermissionScalarFieldEnum]

export const RolePermissionScalarFieldEnum = {
  id: 'id',
  roleId: 'roleId',
  permissionId: 'permissionId',
} as const

export type RolePermissionScalarFieldEnum =
  (typeof RolePermissionScalarFieldEnum)[keyof typeof RolePermissionScalarFieldEnum]

export const SchoolTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  level: 'level',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
} as const

export type SchoolTypeScalarFieldEnum =
  (typeof SchoolTypeScalarFieldEnum)[keyof typeof SchoolTypeScalarFieldEnum]

export const SchoolScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  phone: 'phone',
  email: 'email',
  website: 'website',
  motto: 'motto',
  description: 'description',
  logo: 'logo',
  certification: 'certification',
  isActive: 'isActive',
  verified: 'verified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  typeId: 'typeId',
} as const

export type SchoolScalarFieldEnum =
  (typeof SchoolScalarFieldEnum)[keyof typeof SchoolScalarFieldEnum]

export const SchoolUserScalarFieldEnum = {
  id: 'id',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  schoolId: 'schoolId',
  userId: 'userId',
} as const

export type SchoolUserScalarFieldEnum =
  (typeof SchoolUserScalarFieldEnum)[keyof typeof SchoolUserScalarFieldEnum]

export const DepartmentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  schoolId: 'schoolId',
} as const

export type DepartmentScalarFieldEnum =
  (typeof DepartmentScalarFieldEnum)[keyof typeof DepartmentScalarFieldEnum]

export const DepartmentStaffScalarFieldEnum = {
  id: 'id',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  departmentId: 'departmentId',
  userId: 'userId',
} as const

export type DepartmentStaffScalarFieldEnum =
  (typeof DepartmentStaffScalarFieldEnum)[keyof typeof DepartmentStaffScalarFieldEnum]

export const SessionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  schoolId: 'schoolId',
} as const

export type SessionScalarFieldEnum =
  (typeof SessionScalarFieldEnum)[keyof typeof SessionScalarFieldEnum]

export const TokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  type: 'type',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
} as const

export type TokenScalarFieldEnum = (typeof TokenScalarFieldEnum)[keyof typeof TokenScalarFieldEnum]

export const UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  firstName: 'firstName',
  middleName: 'middleName',
  lastName: 'lastName',
  otherName: 'otherName',
  gender: 'gender',
  dateOfBirth: 'dateOfBirth',
  phone: 'phone',
  otherPhone: 'otherPhone',
  nationality: 'nationality',
  avatar: 'avatar',
  city: 'city',
  barangay: 'barangay',
  cra: 'cra',
  pobProvince: 'pobProvince',
  pobCity: 'pobCity',
  religiousAffiliation: 'religiousAffiliation',
  isVerified: 'isVerified',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  password: 'password',
  lastLogin: 'lastLogin',
  roleId: 'roleId',
} as const

export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]

export const AdminScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  adminLevel: 'adminLevel',
} as const

export type AdminScalarFieldEnum = (typeof AdminScalarFieldEnum)[keyof typeof AdminScalarFieldEnum]

export const TeacherScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  employeeId: 'employeeId',
  qualification: 'qualification',
  experience: 'experience',
  joiningDate: 'joiningDate',
} as const

export type TeacherScalarFieldEnum =
  (typeof TeacherScalarFieldEnum)[keyof typeof TeacherScalarFieldEnum]

export const StaffScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  employeeId: 'employeeId',
  qualification: 'qualification',
  experience: 'experience',
  joiningDate: 'joiningDate',
} as const

export type StaffScalarFieldEnum = (typeof StaffScalarFieldEnum)[keyof typeof StaffScalarFieldEnum]

export const StudentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  studentId: 'studentId',
  emergencyContact: 'emergencyContact',
  enrollmentDate: 'enrollmentDate',
  schoolId: 'schoolId',
} as const

export type StudentScalarFieldEnum =
  (typeof StudentScalarFieldEnum)[keyof typeof StudentScalarFieldEnum]

export const ParentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  occupation: 'occupation',
  relationship: 'relationship',
} as const

export type ParentScalarFieldEnum =
  (typeof ParentScalarFieldEnum)[keyof typeof ParentScalarFieldEnum]

export const SortOrder = {
  asc: 'asc',
  desc: 'desc',
} as const

export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]

export const NullableJsonNullValueInput = {
  DbNull: DbNull,
  JsonNull: JsonNull,
} as const

export type NullableJsonNullValueInput =
  (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]

export const QueryMode = {
  default: 'default',
  insensitive: 'insensitive',
} as const

export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]

export const NullsOrder = {
  first: 'first',
  last: 'last',
} as const

export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]

export const JsonNullValueFilter = {
  DbNull: DbNull,
  JsonNull: JsonNull,
  AnyNull: AnyNull,
} as const

export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]

/**
 * Field references
 */

/**
 * Reference to a field of type 'String'
 */
export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>

/**
 * Reference to a field of type 'String[]'
 */
export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>

/**
 * Reference to a field of type 'DateTime'
 */
export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>

/**
 * Reference to a field of type 'DateTime[]'
 */
export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>

/**
 * Reference to a field of type 'Json'
 */
export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>

/**
 * Reference to a field of type 'QueryMode'
 */
export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>

/**
 * Reference to a field of type 'Boolean'
 */
export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>

/**
 * Reference to a field of type 'Int'
 */
export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>

/**
 * Reference to a field of type 'Int[]'
 */
export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>

/**
 * Reference to a field of type 'SchoolLevel'
 */
export type EnumSchoolLevelFieldRefInput<$PrismaModel> = FieldRefInputType<
  $PrismaModel,
  'SchoolLevel'
>

/**
 * Reference to a field of type 'SchoolLevel[]'
 */
export type ListEnumSchoolLevelFieldRefInput<$PrismaModel> = FieldRefInputType<
  $PrismaModel,
  'SchoolLevel[]'
>

/**
 * Reference to a field of type 'TokenTypes'
 */
export type EnumTokenTypesFieldRefInput<$PrismaModel> = FieldRefInputType<
  $PrismaModel,
  'TokenTypes'
>

/**
 * Reference to a field of type 'TokenTypes[]'
 */
export type ListEnumTokenTypesFieldRefInput<$PrismaModel> = FieldRefInputType<
  $PrismaModel,
  'TokenTypes[]'
>

/**
 * Reference to a field of type 'Gender'
 */
export type EnumGenderFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Gender'>

/**
 * Reference to a field of type 'Gender[]'
 */
export type ListEnumGenderFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Gender[]'>

/**
 * Reference to a field of type 'Float'
 */
export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>

/**
 * Reference to a field of type 'Float[]'
 */
export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>

/**
 * Batch Payload for updateMany & deleteMany & createMany
 */
export type BatchPayload = {
  count: number
}

export type Datasource = {
  url?: string
}
export type Datasources = {
  db?: Datasource
}

export const defineExtension = runtime.Extensions
  .defineExtension as unknown as runtime.Types.Extensions.ExtendsHook<
  'define',
  TypeMapCb,
  runtime.Types.Extensions.DefaultArgs
>
export type DefaultPrismaClient = PrismaClient
export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
export interface PrismaClientOptions {
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasources?: Datasources
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasourceUrl?: string
  /**
   * @default "colorless"
   */
  errorFormat?: ErrorFormat
  /**
   * @example
   * ```
   * // Shorthand for `emit: 'stdout'`
   * log: ['query', 'info', 'warn', 'error']
   *
   * // Emit as events only
   * log: [
   *   { emit: 'event', level: 'query' },
   *   { emit: 'event', level: 'info' },
   *   { emit: 'event', level: 'warn' }
   *   { emit: 'event', level: 'error' }
   * ]
   *
   * / Emit as events and log to stdout
   * og: [
   *  { emit: 'stdout', level: 'query' },
   *  { emit: 'stdout', level: 'info' },
   *  { emit: 'stdout', level: 'warn' }
   *  { emit: 'stdout', level: 'error' }
   *
   * ```
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
   */
  log?: (LogLevel | LogDefinition)[]
  /**
   * The default values for transactionOptions
   * maxWait ?= 2000
   * timeout ?= 5000
   */
  transactionOptions?: {
    maxWait?: number
    timeout?: number
    isolationLevel?: TransactionIsolationLevel
  }
  /**
   * Global configuration for omitting model fields by default.
   *
   * @example
   * ```
   * const prisma = new PrismaClient({
   *   omit: {
   *     user: {
   *       password: true
   *     }
   *   }
   * })
   * ```
   */
  omit?: GlobalOmitConfig
}
export type GlobalOmitConfig = {
  class?: Prisma.ClassOmit
  section?: Prisma.SectionOmit
  subject?: Prisma.SubjectOmit
  subjectClass?: Prisma.SubjectClassOmit
  subjectTeacher?: Prisma.SubjectTeacherOmit
  loginLog?: Prisma.LoginLogOmit
  activityLog?: Prisma.ActivityLogOmit
  role?: Prisma.RoleOmit
  permission?: Prisma.PermissionOmit
  rolePermission?: Prisma.RolePermissionOmit
  schoolType?: Prisma.SchoolTypeOmit
  school?: Prisma.SchoolOmit
  schoolUser?: Prisma.SchoolUserOmit
  department?: Prisma.DepartmentOmit
  departmentStaff?: Prisma.DepartmentStaffOmit
  session?: Prisma.SessionOmit
  token?: Prisma.TokenOmit
  user?: Prisma.UserOmit
  admin?: Prisma.AdminOmit
  teacher?: Prisma.TeacherOmit
  staff?: Prisma.StaffOmit
  student?: Prisma.StudentOmit
  parent?: Prisma.ParentOmit
}

/* Types for Logging */
export type LogLevel = 'info' | 'query' | 'warn' | 'error'
export type LogDefinition = {
  level: LogLevel
  emit: 'stdout' | 'event'
}

export type CheckIsLogLevel<T> = T extends LogLevel ? T : never

export type GetLogType<T> = CheckIsLogLevel<T extends LogDefinition ? T['level'] : T>

export type GetEvents<T extends any[]> =
  T extends Array<LogLevel | LogDefinition> ? GetLogType<T[number]> : never

export type QueryEvent = {
  timestamp: Date
  query: string
  params: string
  duration: number
  target: string
}

export type LogEvent = {
  timestamp: Date
  message: string
  target: string
}
/* End Types for Logging */

export type PrismaAction =
  | 'findUnique'
  | 'findUniqueOrThrow'
  | 'findMany'
  | 'findFirst'
  | 'findFirstOrThrow'
  | 'create'
  | 'createMany'
  | 'createManyAndReturn'
  | 'update'
  | 'updateMany'
  | 'updateManyAndReturn'
  | 'upsert'
  | 'delete'
  | 'deleteMany'
  | 'executeRaw'
  | 'queryRaw'
  | 'aggregate'
  | 'count'
  | 'runCommandRaw'
  | 'findRaw'
  | 'groupBy'

/**
 * `PrismaClient` proxy available in interactive transactions.
 */
export type TransactionClient = Omit<DefaultPrismaClient, runtime.ITXClientDenyList>
