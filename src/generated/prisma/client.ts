/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from '@prisma/client/runtime/library'
import * as $Enums from './enums.js'
import * as $Class from './internal/class.js'
import * as Prisma from './internal/prismaNamespace.js'

export * as $Enums from './enums.js'
/**
 * ## Prisma Client
 *
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Classes
 * const classes = await prisma.class.findMany()
 * ```
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<
  LogOpts extends Prisma.LogLevel = never,
  OmitOpts extends Prisma.PrismaClientOptions['omit'] = Prisma.PrismaClientOptions['omit'],
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = $Class.PrismaClient<LogOpts, OmitOpts, ExtArgs>
export { Prisma }

// file annotations for bundling tools to include these files
path.join(__dirname, 'libquery_engine-debian-openssl-3.0.x.so.node')
path.join(process.cwd(), 'src/generated/prisma/libquery_engine-debian-openssl-3.0.x.so.node')

/**
 * Model Class
 *
 */
export type Class = Prisma.ClassModel
/**
 * Model Section
 *
 */
export type Section = Prisma.SectionModel
/**
 * Model Subject
 *
 */
export type Subject = Prisma.SubjectModel
/**
 * Model SubjectClass
 *
 */
export type SubjectClass = Prisma.SubjectClassModel
/**
 * Model SubjectTeacher
 *
 */
export type SubjectTeacher = Prisma.SubjectTeacherModel
/**
 * Model LoginLog
 *
 */
export type LoginLog = Prisma.LoginLogModel
/**
 * Model ActivityLog
 *
 */
export type ActivityLog = Prisma.ActivityLogModel
/**
 * Model Role
 *
 */
export type Role = Prisma.RoleModel
/**
 * Model Permission
 *
 */
export type Permission = Prisma.PermissionModel
/**
 * Model RolePermission
 *
 */
export type RolePermission = Prisma.RolePermissionModel
/**
 * Model SchoolType
 *
 */
export type SchoolType = Prisma.SchoolTypeModel
/**
 * Model School
 *
 */
export type School = Prisma.SchoolModel
/**
 * Model SchoolUser
 *
 */
export type SchoolUser = Prisma.SchoolUserModel
/**
 * Model Department
 *
 */
export type Department = Prisma.DepartmentModel
/**
 * Model DepartmentStaff
 *
 */
export type DepartmentStaff = Prisma.DepartmentStaffModel
/**
 * Model Session
 *
 */
export type Session = Prisma.SessionModel
/**
 * Model Token
 *
 */
export type Token = Prisma.TokenModel
/**
 * Model User
 *
 */
export type User = Prisma.UserModel
/**
 * Model Admin
 *
 */
export type Admin = Prisma.AdminModel
/**
 * Model Teacher
 *
 */
export type Teacher = Prisma.TeacherModel
/**
 * Model Staff
 *
 */
export type Staff = Prisma.StaffModel
/**
 * Model Student
 *
 */
export type Student = Prisma.StudentModel
/**
 * Model Parent
 *
 */
export type Parent = Prisma.ParentModel

export type SchoolLevel = $Enums.SchoolLevel
export const SchoolLevel = $Enums.SchoolLevel

export type TokenTypes = $Enums.TokenTypes
export const TokenTypes = $Enums.TokenTypes

export type Gender = $Enums.Gender
export const Gender = $Enums.Gender
