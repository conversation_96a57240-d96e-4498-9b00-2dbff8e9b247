/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `SubjectClass` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model SubjectClass
 *
 */
export type SubjectClassModel = runtime.Types.Result.DefaultSelection<Prisma.$SubjectClassPayload>

export type AggregateSubjectClass = {
  _count: SubjectClassCountAggregateOutputType | null
  _min: SubjectClassMinAggregateOutputType | null
  _max: SubjectClassMaxAggregateOutputType | null
}

export type SubjectClassMinAggregateOutputType = {
  id: string | null
  createdAt: Date | null
  updatedAt: Date | null
  subjectId: string | null
  classId: string | null
}

export type SubjectClassMaxAggregateOutputType = {
  id: string | null
  createdAt: Date | null
  updatedAt: Date | null
  subjectId: string | null
  classId: string | null
}

export type SubjectClassCountAggregateOutputType = {
  id: number
  createdAt: number
  updatedAt: number
  subjectId: number
  classId: number
  _all: number
}

export type SubjectClassMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  subjectId?: true
  classId?: true
}

export type SubjectClassMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  subjectId?: true
  classId?: true
}

export type SubjectClassCountAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  subjectId?: true
  classId?: true
  _all?: true
}

export type SubjectClassAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SubjectClass to aggregate.
   */
  where?: Prisma.SubjectClassWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectClasses to fetch.
   */
  orderBy?:
    | Prisma.SubjectClassOrderByWithRelationInput
    | Prisma.SubjectClassOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.SubjectClassWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectClasses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectClasses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned SubjectClasses
   **/
  _count?: true | SubjectClassCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: SubjectClassMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: SubjectClassMaxAggregateInputType
}

export type GetSubjectClassAggregateType<T extends SubjectClassAggregateArgs> = {
  [P in keyof T & keyof AggregateSubjectClass]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSubjectClass[P]>
    : Prisma.GetScalarType<T[P], AggregateSubjectClass[P]>
}

export type SubjectClassGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SubjectClassWhereInput
  orderBy?:
    | Prisma.SubjectClassOrderByWithAggregationInput
    | Prisma.SubjectClassOrderByWithAggregationInput[]
  by: Prisma.SubjectClassScalarFieldEnum[] | Prisma.SubjectClassScalarFieldEnum
  having?: Prisma.SubjectClassScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SubjectClassCountAggregateInputType | true
  _min?: SubjectClassMinAggregateInputType
  _max?: SubjectClassMaxAggregateInputType
}

export type SubjectClassGroupByOutputType = {
  id: string
  createdAt: Date
  updatedAt: Date
  subjectId: string
  classId: string
  _count: SubjectClassCountAggregateOutputType | null
  _min: SubjectClassMinAggregateOutputType | null
  _max: SubjectClassMaxAggregateOutputType | null
}

type GetSubjectClassGroupByPayload<T extends SubjectClassGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SubjectClassGroupByOutputType, T['by']> & {
      [P in keyof T & keyof SubjectClassGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], SubjectClassGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], SubjectClassGroupByOutputType[P]>
    }
  >
>

export type SubjectClassWhereInput = {
  AND?: Prisma.SubjectClassWhereInput | Prisma.SubjectClassWhereInput[]
  OR?: Prisma.SubjectClassWhereInput[]
  NOT?: Prisma.SubjectClassWhereInput | Prisma.SubjectClassWhereInput[]
  id?: Prisma.StringFilter<'SubjectClass'> | string
  createdAt?: Prisma.DateTimeFilter<'SubjectClass'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'SubjectClass'> | Date | string
  subjectId?: Prisma.StringFilter<'SubjectClass'> | string
  classId?: Prisma.StringFilter<'SubjectClass'> | string
  subject?: Prisma.XOR<Prisma.SubjectScalarRelationFilter, Prisma.SubjectWhereInput>
  class?: Prisma.XOR<Prisma.ClassScalarRelationFilter, Prisma.ClassWhereInput>
}

export type SubjectClassOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  classId?: Prisma.SortOrder
  subject?: Prisma.SubjectOrderByWithRelationInput
  class?: Prisma.ClassOrderByWithRelationInput
}

export type SubjectClassWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    subjectId_classId?: Prisma.SubjectClassSubjectIdClassIdCompoundUniqueInput
    AND?: Prisma.SubjectClassWhereInput | Prisma.SubjectClassWhereInput[]
    OR?: Prisma.SubjectClassWhereInput[]
    NOT?: Prisma.SubjectClassWhereInput | Prisma.SubjectClassWhereInput[]
    createdAt?: Prisma.DateTimeFilter<'SubjectClass'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'SubjectClass'> | Date | string
    subjectId?: Prisma.StringFilter<'SubjectClass'> | string
    classId?: Prisma.StringFilter<'SubjectClass'> | string
    subject?: Prisma.XOR<Prisma.SubjectScalarRelationFilter, Prisma.SubjectWhereInput>
    class?: Prisma.XOR<Prisma.ClassScalarRelationFilter, Prisma.ClassWhereInput>
  },
  'id' | 'subjectId_classId'
>

export type SubjectClassOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  classId?: Prisma.SortOrder
  _count?: Prisma.SubjectClassCountOrderByAggregateInput
  _max?: Prisma.SubjectClassMaxOrderByAggregateInput
  _min?: Prisma.SubjectClassMinOrderByAggregateInput
}

export type SubjectClassScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.SubjectClassScalarWhereWithAggregatesInput
    | Prisma.SubjectClassScalarWhereWithAggregatesInput[]
  OR?: Prisma.SubjectClassScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.SubjectClassScalarWhereWithAggregatesInput
    | Prisma.SubjectClassScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'SubjectClass'> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'SubjectClass'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'SubjectClass'> | Date | string
  subjectId?: Prisma.StringWithAggregatesFilter<'SubjectClass'> | string
  classId?: Prisma.StringWithAggregatesFilter<'SubjectClass'> | string
}

export type SubjectClassCreateInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subject: Prisma.SubjectCreateNestedOneWithoutClassesInput
  class: Prisma.ClassCreateNestedOneWithoutSubjectsInput
}

export type SubjectClassUncheckedCreateInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
  classId: string
}

export type SubjectClassUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subject?: Prisma.SubjectUpdateOneRequiredWithoutClassesNestedInput
  class?: Prisma.ClassUpdateOneRequiredWithoutSubjectsNestedInput
}

export type SubjectClassUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
  classId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectClassCreateManyInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
  classId: string
}

export type SubjectClassUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SubjectClassUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
  classId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectClassListRelationFilter = {
  every?: Prisma.SubjectClassWhereInput
  some?: Prisma.SubjectClassWhereInput
  none?: Prisma.SubjectClassWhereInput
}

export type SubjectClassOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SubjectClassSubjectIdClassIdCompoundUniqueInput = {
  subjectId: string
  classId: string
}

export type SubjectClassCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  classId?: Prisma.SortOrder
}

export type SubjectClassMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  classId?: Prisma.SortOrder
}

export type SubjectClassMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  classId?: Prisma.SortOrder
}

export type SubjectClassCreateNestedManyWithoutClassInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutClassInput,
        Prisma.SubjectClassUncheckedCreateWithoutClassInput
      >
    | Prisma.SubjectClassCreateWithoutClassInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput[]
  createMany?: Prisma.SubjectClassCreateManyClassInputEnvelope
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
}

export type SubjectClassUncheckedCreateNestedManyWithoutClassInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutClassInput,
        Prisma.SubjectClassUncheckedCreateWithoutClassInput
      >
    | Prisma.SubjectClassCreateWithoutClassInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput[]
  createMany?: Prisma.SubjectClassCreateManyClassInputEnvelope
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
}

export type SubjectClassUpdateManyWithoutClassNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutClassInput,
        Prisma.SubjectClassUncheckedCreateWithoutClassInput
      >
    | Prisma.SubjectClassCreateWithoutClassInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput[]
  upsert?:
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutClassInput
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutClassInput[]
  createMany?: Prisma.SubjectClassCreateManyClassInputEnvelope
  set?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  disconnect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  delete?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  update?:
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutClassInput
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutClassInput[]
  updateMany?:
    | Prisma.SubjectClassUpdateManyWithWhereWithoutClassInput
    | Prisma.SubjectClassUpdateManyWithWhereWithoutClassInput[]
  deleteMany?: Prisma.SubjectClassScalarWhereInput | Prisma.SubjectClassScalarWhereInput[]
}

export type SubjectClassUncheckedUpdateManyWithoutClassNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutClassInput,
        Prisma.SubjectClassUncheckedCreateWithoutClassInput
      >
    | Prisma.SubjectClassCreateWithoutClassInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput
    | Prisma.SubjectClassCreateOrConnectWithoutClassInput[]
  upsert?:
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutClassInput
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutClassInput[]
  createMany?: Prisma.SubjectClassCreateManyClassInputEnvelope
  set?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  disconnect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  delete?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  update?:
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutClassInput
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutClassInput[]
  updateMany?:
    | Prisma.SubjectClassUpdateManyWithWhereWithoutClassInput
    | Prisma.SubjectClassUpdateManyWithWhereWithoutClassInput[]
  deleteMany?: Prisma.SubjectClassScalarWhereInput | Prisma.SubjectClassScalarWhereInput[]
}

export type SubjectClassCreateNestedManyWithoutSubjectInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutSubjectInput,
        Prisma.SubjectClassUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectClassCreateWithoutSubjectInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput[]
  createMany?: Prisma.SubjectClassCreateManySubjectInputEnvelope
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
}

export type SubjectClassUncheckedCreateNestedManyWithoutSubjectInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutSubjectInput,
        Prisma.SubjectClassUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectClassCreateWithoutSubjectInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput[]
  createMany?: Prisma.SubjectClassCreateManySubjectInputEnvelope
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
}

export type SubjectClassUpdateManyWithoutSubjectNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutSubjectInput,
        Prisma.SubjectClassUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectClassCreateWithoutSubjectInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput[]
  upsert?:
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutSubjectInput[]
  createMany?: Prisma.SubjectClassCreateManySubjectInputEnvelope
  set?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  disconnect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  delete?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  update?:
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutSubjectInput[]
  updateMany?:
    | Prisma.SubjectClassUpdateManyWithWhereWithoutSubjectInput
    | Prisma.SubjectClassUpdateManyWithWhereWithoutSubjectInput[]
  deleteMany?: Prisma.SubjectClassScalarWhereInput | Prisma.SubjectClassScalarWhereInput[]
}

export type SubjectClassUncheckedUpdateManyWithoutSubjectNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectClassCreateWithoutSubjectInput,
        Prisma.SubjectClassUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectClassCreateWithoutSubjectInput[]
    | Prisma.SubjectClassUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectClassCreateOrConnectWithoutSubjectInput[]
  upsert?:
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectClassUpsertWithWhereUniqueWithoutSubjectInput[]
  createMany?: Prisma.SubjectClassCreateManySubjectInputEnvelope
  set?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  disconnect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  delete?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  connect?: Prisma.SubjectClassWhereUniqueInput | Prisma.SubjectClassWhereUniqueInput[]
  update?:
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectClassUpdateWithWhereUniqueWithoutSubjectInput[]
  updateMany?:
    | Prisma.SubjectClassUpdateManyWithWhereWithoutSubjectInput
    | Prisma.SubjectClassUpdateManyWithWhereWithoutSubjectInput[]
  deleteMany?: Prisma.SubjectClassScalarWhereInput | Prisma.SubjectClassScalarWhereInput[]
}

export type SubjectClassCreateWithoutClassInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subject: Prisma.SubjectCreateNestedOneWithoutClassesInput
}

export type SubjectClassUncheckedCreateWithoutClassInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
}

export type SubjectClassCreateOrConnectWithoutClassInput = {
  where: Prisma.SubjectClassWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SubjectClassCreateWithoutClassInput,
    Prisma.SubjectClassUncheckedCreateWithoutClassInput
  >
}

export type SubjectClassCreateManyClassInputEnvelope = {
  data: Prisma.SubjectClassCreateManyClassInput | Prisma.SubjectClassCreateManyClassInput[]
  skipDuplicates?: boolean
}

export type SubjectClassUpsertWithWhereUniqueWithoutClassInput = {
  where: Prisma.SubjectClassWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SubjectClassUpdateWithoutClassInput,
    Prisma.SubjectClassUncheckedUpdateWithoutClassInput
  >
  create: Prisma.XOR<
    Prisma.SubjectClassCreateWithoutClassInput,
    Prisma.SubjectClassUncheckedCreateWithoutClassInput
  >
}

export type SubjectClassUpdateWithWhereUniqueWithoutClassInput = {
  where: Prisma.SubjectClassWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SubjectClassUpdateWithoutClassInput,
    Prisma.SubjectClassUncheckedUpdateWithoutClassInput
  >
}

export type SubjectClassUpdateManyWithWhereWithoutClassInput = {
  where: Prisma.SubjectClassScalarWhereInput
  data: Prisma.XOR<
    Prisma.SubjectClassUpdateManyMutationInput,
    Prisma.SubjectClassUncheckedUpdateManyWithoutClassInput
  >
}

export type SubjectClassScalarWhereInput = {
  AND?: Prisma.SubjectClassScalarWhereInput | Prisma.SubjectClassScalarWhereInput[]
  OR?: Prisma.SubjectClassScalarWhereInput[]
  NOT?: Prisma.SubjectClassScalarWhereInput | Prisma.SubjectClassScalarWhereInput[]
  id?: Prisma.StringFilter<'SubjectClass'> | string
  createdAt?: Prisma.DateTimeFilter<'SubjectClass'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'SubjectClass'> | Date | string
  subjectId?: Prisma.StringFilter<'SubjectClass'> | string
  classId?: Prisma.StringFilter<'SubjectClass'> | string
}

export type SubjectClassCreateWithoutSubjectInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  class: Prisma.ClassCreateNestedOneWithoutSubjectsInput
}

export type SubjectClassUncheckedCreateWithoutSubjectInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  classId: string
}

export type SubjectClassCreateOrConnectWithoutSubjectInput = {
  where: Prisma.SubjectClassWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SubjectClassCreateWithoutSubjectInput,
    Prisma.SubjectClassUncheckedCreateWithoutSubjectInput
  >
}

export type SubjectClassCreateManySubjectInputEnvelope = {
  data: Prisma.SubjectClassCreateManySubjectInput | Prisma.SubjectClassCreateManySubjectInput[]
  skipDuplicates?: boolean
}

export type SubjectClassUpsertWithWhereUniqueWithoutSubjectInput = {
  where: Prisma.SubjectClassWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SubjectClassUpdateWithoutSubjectInput,
    Prisma.SubjectClassUncheckedUpdateWithoutSubjectInput
  >
  create: Prisma.XOR<
    Prisma.SubjectClassCreateWithoutSubjectInput,
    Prisma.SubjectClassUncheckedCreateWithoutSubjectInput
  >
}

export type SubjectClassUpdateWithWhereUniqueWithoutSubjectInput = {
  where: Prisma.SubjectClassWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SubjectClassUpdateWithoutSubjectInput,
    Prisma.SubjectClassUncheckedUpdateWithoutSubjectInput
  >
}

export type SubjectClassUpdateManyWithWhereWithoutSubjectInput = {
  where: Prisma.SubjectClassScalarWhereInput
  data: Prisma.XOR<
    Prisma.SubjectClassUpdateManyMutationInput,
    Prisma.SubjectClassUncheckedUpdateManyWithoutSubjectInput
  >
}

export type SubjectClassCreateManyClassInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
}

export type SubjectClassUpdateWithoutClassInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subject?: Prisma.SubjectUpdateOneRequiredWithoutClassesNestedInput
}

export type SubjectClassUncheckedUpdateWithoutClassInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectClassUncheckedUpdateManyWithoutClassInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectClassCreateManySubjectInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  classId: string
}

export type SubjectClassUpdateWithoutSubjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  class?: Prisma.ClassUpdateOneRequiredWithoutSubjectsNestedInput
}

export type SubjectClassUncheckedUpdateWithoutSubjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  classId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectClassUncheckedUpdateManyWithoutSubjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  classId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectClassSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    subjectId?: boolean
    classId?: boolean
    subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
    class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subjectClass']
>

export type SubjectClassSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    subjectId?: boolean
    classId?: boolean
    subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
    class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subjectClass']
>

export type SubjectClassSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    subjectId?: boolean
    classId?: boolean
    subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
    class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subjectClass']
>

export type SubjectClassSelectScalar = {
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  subjectId?: boolean
  classId?: boolean
}

export type SubjectClassOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'createdAt' | 'updatedAt' | 'subjectId' | 'classId',
  ExtArgs['result']['subjectClass']
>
export type SubjectClassInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
  class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
}
export type SubjectClassIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
  class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
}
export type SubjectClassIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
  class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
}

export type $SubjectClassPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'SubjectClass'
  objects: {
    subject: Prisma.$SubjectPayload<ExtArgs>
    class: Prisma.$ClassPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      createdAt: Date
      updatedAt: Date
      subjectId: string
      classId: string
    },
    ExtArgs['result']['subjectClass']
  >
  composites: {}
}

export type SubjectClassGetPayload<S extends boolean | null | undefined | SubjectClassDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$SubjectClassPayload, S>

export type SubjectClassCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<SubjectClassFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: SubjectClassCountAggregateInputType | true
}

export interface SubjectClassDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['SubjectClass']
    meta: { name: 'SubjectClass' }
  }
  /**
   * Find zero or one SubjectClass that matches the filter.
   * @param {SubjectClassFindUniqueArgs} args - Arguments to find a SubjectClass
   * @example
   * // Get one SubjectClass
   * const subjectClass = await prisma.subjectClass.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SubjectClassFindUniqueArgs>(
    args: Prisma.SelectSubset<T, SubjectClassFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one SubjectClass that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SubjectClassFindUniqueOrThrowArgs} args - Arguments to find a SubjectClass
   * @example
   * // Get one SubjectClass
   * const subjectClass = await prisma.subjectClass.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SubjectClassFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, SubjectClassFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SubjectClass that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectClassFindFirstArgs} args - Arguments to find a SubjectClass
   * @example
   * // Get one SubjectClass
   * const subjectClass = await prisma.subjectClass.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SubjectClassFindFirstArgs>(
    args?: Prisma.SelectSubset<T, SubjectClassFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SubjectClass that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectClassFindFirstOrThrowArgs} args - Arguments to find a SubjectClass
   * @example
   * // Get one SubjectClass
   * const subjectClass = await prisma.subjectClass.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SubjectClassFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, SubjectClassFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more SubjectClasses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectClassFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all SubjectClasses
   * const subjectClasses = await prisma.subjectClass.findMany()
   *
   * // Get first 10 SubjectClasses
   * const subjectClasses = await prisma.subjectClass.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const subjectClassWithIdOnly = await prisma.subjectClass.findMany({ select: { id: true } })
   *
   */
  findMany<T extends SubjectClassFindManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectClassFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a SubjectClass.
   * @param {SubjectClassCreateArgs} args - Arguments to create a SubjectClass.
   * @example
   * // Create one SubjectClass
   * const SubjectClass = await prisma.subjectClass.create({
   *   data: {
   *     // ... data to create a SubjectClass
   *   }
   * })
   *
   */
  create<T extends SubjectClassCreateArgs>(
    args: Prisma.SelectSubset<T, SubjectClassCreateArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many SubjectClasses.
   * @param {SubjectClassCreateManyArgs} args - Arguments to create many SubjectClasses.
   * @example
   * // Create many SubjectClasses
   * const subjectClass = await prisma.subjectClass.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends SubjectClassCreateManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectClassCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many SubjectClasses and returns the data saved in the database.
   * @param {SubjectClassCreateManyAndReturnArgs} args - Arguments to create many SubjectClasses.
   * @example
   * // Create many SubjectClasses
   * const subjectClass = await prisma.subjectClass.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many SubjectClasses and only return the `id`
   * const subjectClassWithIdOnly = await prisma.subjectClass.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends SubjectClassCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, SubjectClassCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a SubjectClass.
   * @param {SubjectClassDeleteArgs} args - Arguments to delete one SubjectClass.
   * @example
   * // Delete one SubjectClass
   * const SubjectClass = await prisma.subjectClass.delete({
   *   where: {
   *     // ... filter to delete one SubjectClass
   *   }
   * })
   *
   */
  delete<T extends SubjectClassDeleteArgs>(
    args: Prisma.SelectSubset<T, SubjectClassDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one SubjectClass.
   * @param {SubjectClassUpdateArgs} args - Arguments to update one SubjectClass.
   * @example
   * // Update one SubjectClass
   * const subjectClass = await prisma.subjectClass.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends SubjectClassUpdateArgs>(
    args: Prisma.SelectSubset<T, SubjectClassUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more SubjectClasses.
   * @param {SubjectClassDeleteManyArgs} args - Arguments to filter SubjectClasses to delete.
   * @example
   * // Delete a few SubjectClasses
   * const { count } = await prisma.subjectClass.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends SubjectClassDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectClassDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SubjectClasses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectClassUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many SubjectClasses
   * const subjectClass = await prisma.subjectClass.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends SubjectClassUpdateManyArgs>(
    args: Prisma.SelectSubset<T, SubjectClassUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SubjectClasses and returns the data updated in the database.
   * @param {SubjectClassUpdateManyAndReturnArgs} args - Arguments to update many SubjectClasses.
   * @example
   * // Update many SubjectClasses
   * const subjectClass = await prisma.subjectClass.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more SubjectClasses and only return the `id`
   * const subjectClassWithIdOnly = await prisma.subjectClass.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends SubjectClassUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, SubjectClassUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one SubjectClass.
   * @param {SubjectClassUpsertArgs} args - Arguments to update or create a SubjectClass.
   * @example
   * // Update or create a SubjectClass
   * const subjectClass = await prisma.subjectClass.upsert({
   *   create: {
   *     // ... data to create a SubjectClass
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the SubjectClass we want to update
   *   }
   * })
   */
  upsert<T extends SubjectClassUpsertArgs>(
    args: Prisma.SelectSubset<T, SubjectClassUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClassClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectClassPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of SubjectClasses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectClassCountArgs} args - Arguments to filter SubjectClasses to count.
   * @example
   * // Count the number of SubjectClasses
   * const count = await prisma.subjectClass.count({
   *   where: {
   *     // ... the filter for the SubjectClasses we want to count
   *   }
   * })
   **/
  count<T extends SubjectClassCountArgs>(
    args?: Prisma.Subset<T, SubjectClassCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SubjectClassCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a SubjectClass.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectClassAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends SubjectClassAggregateArgs>(
    args: Prisma.Subset<T, SubjectClassAggregateArgs>
  ): Prisma.PrismaPromise<GetSubjectClassAggregateType<T>>

  /**
   * Group by SubjectClass.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectClassGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends SubjectClassGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SubjectClassGroupByArgs['orderBy'] }
      : { orderBy?: SubjectClassGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, SubjectClassGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetSubjectClassGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SubjectClass model
   */
  readonly fields: SubjectClassFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for SubjectClass.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SubjectClassClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  subject<T extends Prisma.SubjectDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.SubjectDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    | runtime.Types.Result.GetResult<
        Prisma.$SubjectPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  class<T extends Prisma.ClassDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.ClassDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__ClassClient<
    | runtime.Types.Result.GetResult<
        Prisma.$ClassPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the SubjectClass model
 */
export interface SubjectClassFieldRefs {
  readonly id: Prisma.FieldRef<'SubjectClass', 'String'>
  readonly createdAt: Prisma.FieldRef<'SubjectClass', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'SubjectClass', 'DateTime'>
  readonly subjectId: Prisma.FieldRef<'SubjectClass', 'String'>
  readonly classId: Prisma.FieldRef<'SubjectClass', 'String'>
}

// Custom InputTypes
/**
 * SubjectClass findUnique
 */
export type SubjectClassFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * Filter, which SubjectClass to fetch.
   */
  where: Prisma.SubjectClassWhereUniqueInput
}

/**
 * SubjectClass findUniqueOrThrow
 */
export type SubjectClassFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * Filter, which SubjectClass to fetch.
   */
  where: Prisma.SubjectClassWhereUniqueInput
}

/**
 * SubjectClass findFirst
 */
export type SubjectClassFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * Filter, which SubjectClass to fetch.
   */
  where?: Prisma.SubjectClassWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectClasses to fetch.
   */
  orderBy?:
    | Prisma.SubjectClassOrderByWithRelationInput
    | Prisma.SubjectClassOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SubjectClasses.
   */
  cursor?: Prisma.SubjectClassWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectClasses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectClasses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SubjectClasses.
   */
  distinct?: Prisma.SubjectClassScalarFieldEnum | Prisma.SubjectClassScalarFieldEnum[]
}

/**
 * SubjectClass findFirstOrThrow
 */
export type SubjectClassFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * Filter, which SubjectClass to fetch.
   */
  where?: Prisma.SubjectClassWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectClasses to fetch.
   */
  orderBy?:
    | Prisma.SubjectClassOrderByWithRelationInput
    | Prisma.SubjectClassOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SubjectClasses.
   */
  cursor?: Prisma.SubjectClassWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectClasses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectClasses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SubjectClasses.
   */
  distinct?: Prisma.SubjectClassScalarFieldEnum | Prisma.SubjectClassScalarFieldEnum[]
}

/**
 * SubjectClass findMany
 */
export type SubjectClassFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * Filter, which SubjectClasses to fetch.
   */
  where?: Prisma.SubjectClassWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectClasses to fetch.
   */
  orderBy?:
    | Prisma.SubjectClassOrderByWithRelationInput
    | Prisma.SubjectClassOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing SubjectClasses.
   */
  cursor?: Prisma.SubjectClassWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectClasses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectClasses.
   */
  skip?: number
  distinct?: Prisma.SubjectClassScalarFieldEnum | Prisma.SubjectClassScalarFieldEnum[]
}

/**
 * SubjectClass create
 */
export type SubjectClassCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * The data needed to create a SubjectClass.
   */
  data: Prisma.XOR<Prisma.SubjectClassCreateInput, Prisma.SubjectClassUncheckedCreateInput>
}

/**
 * SubjectClass createMany
 */
export type SubjectClassCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many SubjectClasses.
   */
  data: Prisma.SubjectClassCreateManyInput | Prisma.SubjectClassCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * SubjectClass createManyAndReturn
 */
export type SubjectClassCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * The data used to create many SubjectClasses.
   */
  data: Prisma.SubjectClassCreateManyInput | Prisma.SubjectClassCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * SubjectClass update
 */
export type SubjectClassUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * The data needed to update a SubjectClass.
   */
  data: Prisma.XOR<Prisma.SubjectClassUpdateInput, Prisma.SubjectClassUncheckedUpdateInput>
  /**
   * Choose, which SubjectClass to update.
   */
  where: Prisma.SubjectClassWhereUniqueInput
}

/**
 * SubjectClass updateMany
 */
export type SubjectClassUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update SubjectClasses.
   */
  data: Prisma.XOR<
    Prisma.SubjectClassUpdateManyMutationInput,
    Prisma.SubjectClassUncheckedUpdateManyInput
  >
  /**
   * Filter which SubjectClasses to update
   */
  where?: Prisma.SubjectClassWhereInput
  /**
   * Limit how many SubjectClasses to update.
   */
  limit?: number
}

/**
 * SubjectClass updateManyAndReturn
 */
export type SubjectClassUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * The data used to update SubjectClasses.
   */
  data: Prisma.XOR<
    Prisma.SubjectClassUpdateManyMutationInput,
    Prisma.SubjectClassUncheckedUpdateManyInput
  >
  /**
   * Filter which SubjectClasses to update
   */
  where?: Prisma.SubjectClassWhereInput
  /**
   * Limit how many SubjectClasses to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * SubjectClass upsert
 */
export type SubjectClassUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * The filter to search for the SubjectClass to update in case it exists.
   */
  where: Prisma.SubjectClassWhereUniqueInput
  /**
   * In case the SubjectClass found by the `where` argument doesn't exist, create a new SubjectClass with this data.
   */
  create: Prisma.XOR<Prisma.SubjectClassCreateInput, Prisma.SubjectClassUncheckedCreateInput>
  /**
   * In case the SubjectClass was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SubjectClassUpdateInput, Prisma.SubjectClassUncheckedUpdateInput>
}

/**
 * SubjectClass delete
 */
export type SubjectClassDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  /**
   * Filter which SubjectClass to delete.
   */
  where: Prisma.SubjectClassWhereUniqueInput
}

/**
 * SubjectClass deleteMany
 */
export type SubjectClassDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SubjectClasses to delete
   */
  where?: Prisma.SubjectClassWhereInput
  /**
   * Limit how many SubjectClasses to delete.
   */
  limit?: number
}

/**
 * SubjectClass without action
 */
export type SubjectClassDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
}
