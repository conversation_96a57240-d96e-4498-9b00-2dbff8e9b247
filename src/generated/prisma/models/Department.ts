/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Department` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Department
 *
 */
export type DepartmentModel = runtime.Types.Result.DefaultSelection<Prisma.$DepartmentPayload>

export type AggregateDepartment = {
  _count: DepartmentCountAggregateOutputType | null
  _min: DepartmentMinAggregateOutputType | null
  _max: DepartmentMaxAggregateOutputType | null
}

export type DepartmentMinAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  createdAt: Date | null
  updatedAt: Date | null
  schoolId: string | null
}

export type DepartmentMaxAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  createdAt: Date | null
  updatedAt: Date | null
  schoolId: string | null
}

export type DepartmentCountAggregateOutputType = {
  id: number
  name: number
  description: number
  createdAt: number
  updatedAt: number
  schoolId: number
  _all: number
}

export type DepartmentMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
}

export type DepartmentMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
}

export type DepartmentCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
  _all?: true
}

export type DepartmentAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Department to aggregate.
   */
  where?: Prisma.DepartmentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Departments to fetch.
   */
  orderBy?: Prisma.DepartmentOrderByWithRelationInput | Prisma.DepartmentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.DepartmentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Departments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Departments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Departments
   **/
  _count?: true | DepartmentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: DepartmentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: DepartmentMaxAggregateInputType
}

export type GetDepartmentAggregateType<T extends DepartmentAggregateArgs> = {
  [P in keyof T & keyof AggregateDepartment]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDepartment[P]>
    : Prisma.GetScalarType<T[P], AggregateDepartment[P]>
}

export type DepartmentGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.DepartmentWhereInput
  orderBy?:
    | Prisma.DepartmentOrderByWithAggregationInput
    | Prisma.DepartmentOrderByWithAggregationInput[]
  by: Prisma.DepartmentScalarFieldEnum[] | Prisma.DepartmentScalarFieldEnum
  having?: Prisma.DepartmentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: DepartmentCountAggregateInputType | true
  _min?: DepartmentMinAggregateInputType
  _max?: DepartmentMaxAggregateInputType
}

export type DepartmentGroupByOutputType = {
  id: string
  name: string
  description: string | null
  createdAt: Date
  updatedAt: Date
  schoolId: string
  _count: DepartmentCountAggregateOutputType | null
  _min: DepartmentMinAggregateOutputType | null
  _max: DepartmentMaxAggregateOutputType | null
}

type GetDepartmentGroupByPayload<T extends DepartmentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<DepartmentGroupByOutputType, T['by']> & {
      [P in keyof T & keyof DepartmentGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], DepartmentGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], DepartmentGroupByOutputType[P]>
    }
  >
>

export type DepartmentWhereInput = {
  AND?: Prisma.DepartmentWhereInput | Prisma.DepartmentWhereInput[]
  OR?: Prisma.DepartmentWhereInput[]
  NOT?: Prisma.DepartmentWhereInput | Prisma.DepartmentWhereInput[]
  id?: Prisma.StringFilter<'Department'> | string
  name?: Prisma.StringFilter<'Department'> | string
  description?: Prisma.StringNullableFilter<'Department'> | string | null
  createdAt?: Prisma.DateTimeFilter<'Department'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Department'> | Date | string
  schoolId?: Prisma.StringFilter<'Department'> | string
  school?: Prisma.XOR<Prisma.SchoolScalarRelationFilter, Prisma.SchoolWhereInput>
  staffs?: Prisma.DepartmentStaffListRelationFilter
}

export type DepartmentOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  school?: Prisma.SchoolOrderByWithRelationInput
  staffs?: Prisma.DepartmentStaffOrderByRelationAggregateInput
}

export type DepartmentWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    name_schoolId?: Prisma.DepartmentNameSchoolIdCompoundUniqueInput
    AND?: Prisma.DepartmentWhereInput | Prisma.DepartmentWhereInput[]
    OR?: Prisma.DepartmentWhereInput[]
    NOT?: Prisma.DepartmentWhereInput | Prisma.DepartmentWhereInput[]
    name?: Prisma.StringFilter<'Department'> | string
    description?: Prisma.StringNullableFilter<'Department'> | string | null
    createdAt?: Prisma.DateTimeFilter<'Department'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'Department'> | Date | string
    schoolId?: Prisma.StringFilter<'Department'> | string
    school?: Prisma.XOR<Prisma.SchoolScalarRelationFilter, Prisma.SchoolWhereInput>
    staffs?: Prisma.DepartmentStaffListRelationFilter
  },
  'id' | 'name_schoolId'
>

export type DepartmentOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  _count?: Prisma.DepartmentCountOrderByAggregateInput
  _max?: Prisma.DepartmentMaxOrderByAggregateInput
  _min?: Prisma.DepartmentMinOrderByAggregateInput
}

export type DepartmentScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.DepartmentScalarWhereWithAggregatesInput
    | Prisma.DepartmentScalarWhereWithAggregatesInput[]
  OR?: Prisma.DepartmentScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.DepartmentScalarWhereWithAggregatesInput
    | Prisma.DepartmentScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Department'> | string
  name?: Prisma.StringWithAggregatesFilter<'Department'> | string
  description?: Prisma.StringNullableWithAggregatesFilter<'Department'> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'Department'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'Department'> | Date | string
  schoolId?: Prisma.StringWithAggregatesFilter<'Department'> | string
}

export type DepartmentCreateInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  school: Prisma.SchoolCreateNestedOneWithoutDepartmentsInput
  staffs?: Prisma.DepartmentStaffCreateNestedManyWithoutDepartmentInput
}

export type DepartmentUncheckedCreateInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
  staffs?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutDepartmentInput
}

export type DepartmentUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneRequiredWithoutDepartmentsNestedInput
  staffs?: Prisma.DepartmentStaffUpdateManyWithoutDepartmentNestedInput
}

export type DepartmentUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
  staffs?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutDepartmentNestedInput
}

export type DepartmentCreateManyInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
}

export type DepartmentUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DepartmentUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentListRelationFilter = {
  every?: Prisma.DepartmentWhereInput
  some?: Prisma.DepartmentWhereInput
  none?: Prisma.DepartmentWhereInput
}

export type DepartmentOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type DepartmentNameSchoolIdCompoundUniqueInput = {
  name: string
  schoolId: string
}

export type DepartmentCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type DepartmentMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type DepartmentMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type DepartmentScalarRelationFilter = {
  is?: Prisma.DepartmentWhereInput
  isNot?: Prisma.DepartmentWhereInput
}

export type DepartmentCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentCreateWithoutSchoolInput,
        Prisma.DepartmentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.DepartmentCreateWithoutSchoolInput[]
    | Prisma.DepartmentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.DepartmentCreateManySchoolInputEnvelope
  connect?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
}

export type DepartmentUncheckedCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentCreateWithoutSchoolInput,
        Prisma.DepartmentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.DepartmentCreateWithoutSchoolInput[]
    | Prisma.DepartmentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.DepartmentCreateManySchoolInputEnvelope
  connect?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
}

export type DepartmentUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentCreateWithoutSchoolInput,
        Prisma.DepartmentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.DepartmentCreateWithoutSchoolInput[]
    | Prisma.DepartmentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.DepartmentUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.DepartmentUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.DepartmentCreateManySchoolInputEnvelope
  set?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  disconnect?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  delete?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  connect?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  update?:
    | Prisma.DepartmentUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.DepartmentUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.DepartmentUpdateManyWithWhereWithoutSchoolInput
    | Prisma.DepartmentUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.DepartmentScalarWhereInput | Prisma.DepartmentScalarWhereInput[]
}

export type DepartmentUncheckedUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentCreateWithoutSchoolInput,
        Prisma.DepartmentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.DepartmentCreateWithoutSchoolInput[]
    | Prisma.DepartmentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput
    | Prisma.DepartmentCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.DepartmentUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.DepartmentUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.DepartmentCreateManySchoolInputEnvelope
  set?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  disconnect?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  delete?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  connect?: Prisma.DepartmentWhereUniqueInput | Prisma.DepartmentWhereUniqueInput[]
  update?:
    | Prisma.DepartmentUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.DepartmentUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.DepartmentUpdateManyWithWhereWithoutSchoolInput
    | Prisma.DepartmentUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.DepartmentScalarWhereInput | Prisma.DepartmentScalarWhereInput[]
}

export type DepartmentCreateNestedOneWithoutStaffsInput = {
  create?: Prisma.XOR<
    Prisma.DepartmentCreateWithoutStaffsInput,
    Prisma.DepartmentUncheckedCreateWithoutStaffsInput
  >
  connectOrCreate?: Prisma.DepartmentCreateOrConnectWithoutStaffsInput
  connect?: Prisma.DepartmentWhereUniqueInput
}

export type DepartmentUpdateOneRequiredWithoutStaffsNestedInput = {
  create?: Prisma.XOR<
    Prisma.DepartmentCreateWithoutStaffsInput,
    Prisma.DepartmentUncheckedCreateWithoutStaffsInput
  >
  connectOrCreate?: Prisma.DepartmentCreateOrConnectWithoutStaffsInput
  upsert?: Prisma.DepartmentUpsertWithoutStaffsInput
  connect?: Prisma.DepartmentWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.DepartmentUpdateToOneWithWhereWithoutStaffsInput,
      Prisma.DepartmentUpdateWithoutStaffsInput
    >,
    Prisma.DepartmentUncheckedUpdateWithoutStaffsInput
  >
}

export type DepartmentCreateWithoutSchoolInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  staffs?: Prisma.DepartmentStaffCreateNestedManyWithoutDepartmentInput
}

export type DepartmentUncheckedCreateWithoutSchoolInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  staffs?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutDepartmentInput
}

export type DepartmentCreateOrConnectWithoutSchoolInput = {
  where: Prisma.DepartmentWhereUniqueInput
  create: Prisma.XOR<
    Prisma.DepartmentCreateWithoutSchoolInput,
    Prisma.DepartmentUncheckedCreateWithoutSchoolInput
  >
}

export type DepartmentCreateManySchoolInputEnvelope = {
  data: Prisma.DepartmentCreateManySchoolInput | Prisma.DepartmentCreateManySchoolInput[]
  skipDuplicates?: boolean
}

export type DepartmentUpsertWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.DepartmentWhereUniqueInput
  update: Prisma.XOR<
    Prisma.DepartmentUpdateWithoutSchoolInput,
    Prisma.DepartmentUncheckedUpdateWithoutSchoolInput
  >
  create: Prisma.XOR<
    Prisma.DepartmentCreateWithoutSchoolInput,
    Prisma.DepartmentUncheckedCreateWithoutSchoolInput
  >
}

export type DepartmentUpdateWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.DepartmentWhereUniqueInput
  data: Prisma.XOR<
    Prisma.DepartmentUpdateWithoutSchoolInput,
    Prisma.DepartmentUncheckedUpdateWithoutSchoolInput
  >
}

export type DepartmentUpdateManyWithWhereWithoutSchoolInput = {
  where: Prisma.DepartmentScalarWhereInput
  data: Prisma.XOR<
    Prisma.DepartmentUpdateManyMutationInput,
    Prisma.DepartmentUncheckedUpdateManyWithoutSchoolInput
  >
}

export type DepartmentScalarWhereInput = {
  AND?: Prisma.DepartmentScalarWhereInput | Prisma.DepartmentScalarWhereInput[]
  OR?: Prisma.DepartmentScalarWhereInput[]
  NOT?: Prisma.DepartmentScalarWhereInput | Prisma.DepartmentScalarWhereInput[]
  id?: Prisma.StringFilter<'Department'> | string
  name?: Prisma.StringFilter<'Department'> | string
  description?: Prisma.StringNullableFilter<'Department'> | string | null
  createdAt?: Prisma.DateTimeFilter<'Department'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Department'> | Date | string
  schoolId?: Prisma.StringFilter<'Department'> | string
}

export type DepartmentCreateWithoutStaffsInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  school: Prisma.SchoolCreateNestedOneWithoutDepartmentsInput
}

export type DepartmentUncheckedCreateWithoutStaffsInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
}

export type DepartmentCreateOrConnectWithoutStaffsInput = {
  where: Prisma.DepartmentWhereUniqueInput
  create: Prisma.XOR<
    Prisma.DepartmentCreateWithoutStaffsInput,
    Prisma.DepartmentUncheckedCreateWithoutStaffsInput
  >
}

export type DepartmentUpsertWithoutStaffsInput = {
  update: Prisma.XOR<
    Prisma.DepartmentUpdateWithoutStaffsInput,
    Prisma.DepartmentUncheckedUpdateWithoutStaffsInput
  >
  create: Prisma.XOR<
    Prisma.DepartmentCreateWithoutStaffsInput,
    Prisma.DepartmentUncheckedCreateWithoutStaffsInput
  >
  where?: Prisma.DepartmentWhereInput
}

export type DepartmentUpdateToOneWithWhereWithoutStaffsInput = {
  where?: Prisma.DepartmentWhereInput
  data: Prisma.XOR<
    Prisma.DepartmentUpdateWithoutStaffsInput,
    Prisma.DepartmentUncheckedUpdateWithoutStaffsInput
  >
}

export type DepartmentUpdateWithoutStaffsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneRequiredWithoutDepartmentsNestedInput
}

export type DepartmentUncheckedUpdateWithoutStaffsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentCreateManySchoolInput = {
  id?: string
  name: string
  description?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DepartmentUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  staffs?: Prisma.DepartmentStaffUpdateManyWithoutDepartmentNestedInput
}

export type DepartmentUncheckedUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  staffs?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutDepartmentNestedInput
}

export type DepartmentUncheckedUpdateManyWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

/**
 * Count Type DepartmentCountOutputType
 */

export type DepartmentCountOutputType = {
  staffs: number
}

export type DepartmentCountOutputTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  staffs?: boolean | DepartmentCountOutputTypeCountStaffsArgs
}

/**
 * DepartmentCountOutputType without action
 */
export type DepartmentCountOutputTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentCountOutputType
   */
  select?: Prisma.DepartmentCountOutputTypeSelect<ExtArgs> | null
}

/**
 * DepartmentCountOutputType without action
 */
export type DepartmentCountOutputTypeCountStaffsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.DepartmentStaffWhereInput
}

export type DepartmentSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
    staffs?: boolean | Prisma.Department$staffsArgs<ExtArgs>
    _count?: boolean | Prisma.DepartmentCountOutputTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['department']
>

export type DepartmentSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['department']
>

export type DepartmentSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['department']
>

export type DepartmentSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  schoolId?: boolean
}

export type DepartmentOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'name' | 'description' | 'createdAt' | 'updatedAt' | 'schoolId',
  ExtArgs['result']['department']
>
export type DepartmentInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  staffs?: boolean | Prisma.Department$staffsArgs<ExtArgs>
  _count?: boolean | Prisma.DepartmentCountOutputTypeDefaultArgs<ExtArgs>
}
export type DepartmentIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
}
export type DepartmentIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
}

export type $DepartmentPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Department'
  objects: {
    school: Prisma.$SchoolPayload<ExtArgs>
    staffs: Prisma.$DepartmentStaffPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      name: string
      description: string | null
      createdAt: Date
      updatedAt: Date
      schoolId: string
    },
    ExtArgs['result']['department']
  >
  composites: {}
}

export type DepartmentGetPayload<S extends boolean | null | undefined | DepartmentDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$DepartmentPayload, S>

export type DepartmentCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<DepartmentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: DepartmentCountAggregateInputType | true
}

export interface DepartmentDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['Department']
    meta: { name: 'Department' }
  }
  /**
   * Find zero or one Department that matches the filter.
   * @param {DepartmentFindUniqueArgs} args - Arguments to find a Department
   * @example
   * // Get one Department
   * const department = await prisma.department.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends DepartmentFindUniqueArgs>(
    args: Prisma.SelectSubset<T, DepartmentFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Department that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {DepartmentFindUniqueOrThrowArgs} args - Arguments to find a Department
   * @example
   * // Get one Department
   * const department = await prisma.department.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends DepartmentFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, DepartmentFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Department that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentFindFirstArgs} args - Arguments to find a Department
   * @example
   * // Get one Department
   * const department = await prisma.department.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends DepartmentFindFirstArgs>(
    args?: Prisma.SelectSubset<T, DepartmentFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Department that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentFindFirstOrThrowArgs} args - Arguments to find a Department
   * @example
   * // Get one Department
   * const department = await prisma.department.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends DepartmentFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, DepartmentFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Departments that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Departments
   * const departments = await prisma.department.findMany()
   *
   * // Get first 10 Departments
   * const departments = await prisma.department.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const departmentWithIdOnly = await prisma.department.findMany({ select: { id: true } })
   *
   */
  findMany<T extends DepartmentFindManyArgs>(
    args?: Prisma.SelectSubset<T, DepartmentFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a Department.
   * @param {DepartmentCreateArgs} args - Arguments to create a Department.
   * @example
   * // Create one Department
   * const Department = await prisma.department.create({
   *   data: {
   *     // ... data to create a Department
   *   }
   * })
   *
   */
  create<T extends DepartmentCreateArgs>(
    args: Prisma.SelectSubset<T, DepartmentCreateArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Departments.
   * @param {DepartmentCreateManyArgs} args - Arguments to create many Departments.
   * @example
   * // Create many Departments
   * const department = await prisma.department.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends DepartmentCreateManyArgs>(
    args?: Prisma.SelectSubset<T, DepartmentCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Departments and returns the data saved in the database.
   * @param {DepartmentCreateManyAndReturnArgs} args - Arguments to create many Departments.
   * @example
   * // Create many Departments
   * const department = await prisma.department.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Departments and only return the `id`
   * const departmentWithIdOnly = await prisma.department.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends DepartmentCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, DepartmentCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Department.
   * @param {DepartmentDeleteArgs} args - Arguments to delete one Department.
   * @example
   * // Delete one Department
   * const Department = await prisma.department.delete({
   *   where: {
   *     // ... filter to delete one Department
   *   }
   * })
   *
   */
  delete<T extends DepartmentDeleteArgs>(
    args: Prisma.SelectSubset<T, DepartmentDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Department.
   * @param {DepartmentUpdateArgs} args - Arguments to update one Department.
   * @example
   * // Update one Department
   * const department = await prisma.department.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends DepartmentUpdateArgs>(
    args: Prisma.SelectSubset<T, DepartmentUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Departments.
   * @param {DepartmentDeleteManyArgs} args - Arguments to filter Departments to delete.
   * @example
   * // Delete a few Departments
   * const { count } = await prisma.department.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends DepartmentDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, DepartmentDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Departments.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Departments
   * const department = await prisma.department.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends DepartmentUpdateManyArgs>(
    args: Prisma.SelectSubset<T, DepartmentUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Departments and returns the data updated in the database.
   * @param {DepartmentUpdateManyAndReturnArgs} args - Arguments to update many Departments.
   * @example
   * // Update many Departments
   * const department = await prisma.department.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Departments and only return the `id`
   * const departmentWithIdOnly = await prisma.department.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends DepartmentUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, DepartmentUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Department.
   * @param {DepartmentUpsertArgs} args - Arguments to update or create a Department.
   * @example
   * // Update or create a Department
   * const department = await prisma.department.upsert({
   *   create: {
   *     // ... data to create a Department
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Department we want to update
   *   }
   * })
   */
  upsert<T extends DepartmentUpsertArgs>(
    args: Prisma.SelectSubset<T, DepartmentUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Departments.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentCountArgs} args - Arguments to filter Departments to count.
   * @example
   * // Count the number of Departments
   * const count = await prisma.department.count({
   *   where: {
   *     // ... the filter for the Departments we want to count
   *   }
   * })
   **/
  count<T extends DepartmentCountArgs>(
    args?: Prisma.Subset<T, DepartmentCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], DepartmentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Department.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends DepartmentAggregateArgs>(
    args: Prisma.Subset<T, DepartmentAggregateArgs>
  ): Prisma.PrismaPromise<GetDepartmentAggregateType<T>>

  /**
   * Group by Department.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends DepartmentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: DepartmentGroupByArgs['orderBy'] }
      : { orderBy?: DepartmentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, DepartmentGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetDepartmentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Department model
   */
  readonly fields: DepartmentFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Department.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__DepartmentClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  school<T extends Prisma.SchoolDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.SchoolDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    | runtime.Types.Result.GetResult<
        Prisma.$SchoolPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  staffs<T extends Prisma.Department$staffsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.Department$staffsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$DepartmentStaffPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Department model
 */
export interface DepartmentFieldRefs {
  readonly id: Prisma.FieldRef<'Department', 'String'>
  readonly name: Prisma.FieldRef<'Department', 'String'>
  readonly description: Prisma.FieldRef<'Department', 'String'>
  readonly createdAt: Prisma.FieldRef<'Department', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'Department', 'DateTime'>
  readonly schoolId: Prisma.FieldRef<'Department', 'String'>
}

// Custom InputTypes
/**
 * Department findUnique
 */
export type DepartmentFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * Filter, which Department to fetch.
   */
  where: Prisma.DepartmentWhereUniqueInput
}

/**
 * Department findUniqueOrThrow
 */
export type DepartmentFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * Filter, which Department to fetch.
   */
  where: Prisma.DepartmentWhereUniqueInput
}

/**
 * Department findFirst
 */
export type DepartmentFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * Filter, which Department to fetch.
   */
  where?: Prisma.DepartmentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Departments to fetch.
   */
  orderBy?: Prisma.DepartmentOrderByWithRelationInput | Prisma.DepartmentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Departments.
   */
  cursor?: Prisma.DepartmentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Departments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Departments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Departments.
   */
  distinct?: Prisma.DepartmentScalarFieldEnum | Prisma.DepartmentScalarFieldEnum[]
}

/**
 * Department findFirstOrThrow
 */
export type DepartmentFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * Filter, which Department to fetch.
   */
  where?: Prisma.DepartmentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Departments to fetch.
   */
  orderBy?: Prisma.DepartmentOrderByWithRelationInput | Prisma.DepartmentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Departments.
   */
  cursor?: Prisma.DepartmentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Departments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Departments.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Departments.
   */
  distinct?: Prisma.DepartmentScalarFieldEnum | Prisma.DepartmentScalarFieldEnum[]
}

/**
 * Department findMany
 */
export type DepartmentFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * Filter, which Departments to fetch.
   */
  where?: Prisma.DepartmentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Departments to fetch.
   */
  orderBy?: Prisma.DepartmentOrderByWithRelationInput | Prisma.DepartmentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Departments.
   */
  cursor?: Prisma.DepartmentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Departments from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Departments.
   */
  skip?: number
  distinct?: Prisma.DepartmentScalarFieldEnum | Prisma.DepartmentScalarFieldEnum[]
}

/**
 * Department create
 */
export type DepartmentCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * The data needed to create a Department.
   */
  data: Prisma.XOR<Prisma.DepartmentCreateInput, Prisma.DepartmentUncheckedCreateInput>
}

/**
 * Department createMany
 */
export type DepartmentCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Departments.
   */
  data: Prisma.DepartmentCreateManyInput | Prisma.DepartmentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Department createManyAndReturn
 */
export type DepartmentCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * The data used to create many Departments.
   */
  data: Prisma.DepartmentCreateManyInput | Prisma.DepartmentCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Department update
 */
export type DepartmentUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * The data needed to update a Department.
   */
  data: Prisma.XOR<Prisma.DepartmentUpdateInput, Prisma.DepartmentUncheckedUpdateInput>
  /**
   * Choose, which Department to update.
   */
  where: Prisma.DepartmentWhereUniqueInput
}

/**
 * Department updateMany
 */
export type DepartmentUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Departments.
   */
  data: Prisma.XOR<
    Prisma.DepartmentUpdateManyMutationInput,
    Prisma.DepartmentUncheckedUpdateManyInput
  >
  /**
   * Filter which Departments to update
   */
  where?: Prisma.DepartmentWhereInput
  /**
   * Limit how many Departments to update.
   */
  limit?: number
}

/**
 * Department updateManyAndReturn
 */
export type DepartmentUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * The data used to update Departments.
   */
  data: Prisma.XOR<
    Prisma.DepartmentUpdateManyMutationInput,
    Prisma.DepartmentUncheckedUpdateManyInput
  >
  /**
   * Filter which Departments to update
   */
  where?: Prisma.DepartmentWhereInput
  /**
   * Limit how many Departments to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Department upsert
 */
export type DepartmentUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * The filter to search for the Department to update in case it exists.
   */
  where: Prisma.DepartmentWhereUniqueInput
  /**
   * In case the Department found by the `where` argument doesn't exist, create a new Department with this data.
   */
  create: Prisma.XOR<Prisma.DepartmentCreateInput, Prisma.DepartmentUncheckedCreateInput>
  /**
   * In case the Department was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.DepartmentUpdateInput, Prisma.DepartmentUncheckedUpdateInput>
}

/**
 * Department delete
 */
export type DepartmentDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  /**
   * Filter which Department to delete.
   */
  where: Prisma.DepartmentWhereUniqueInput
}

/**
 * Department deleteMany
 */
export type DepartmentDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Departments to delete
   */
  where?: Prisma.DepartmentWhereInput
  /**
   * Limit how many Departments to delete.
   */
  limit?: number
}

/**
 * Department.staffs
 */
export type Department$staffsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  where?: Prisma.DepartmentStaffWhereInput
  orderBy?:
    | Prisma.DepartmentStaffOrderByWithRelationInput
    | Prisma.DepartmentStaffOrderByWithRelationInput[]
  cursor?: Prisma.DepartmentStaffWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DepartmentStaffScalarFieldEnum | Prisma.DepartmentStaffScalarFieldEnum[]
}

/**
 * Department without action
 */
export type DepartmentDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
}
