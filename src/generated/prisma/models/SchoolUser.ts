/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `SchoolUser` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model SchoolUser
 *
 */
export type SchoolUserModel = runtime.Types.Result.DefaultSelection<Prisma.$SchoolUserPayload>

export type AggregateSchoolUser = {
  _count: SchoolUserCountAggregateOutputType | null
  _min: SchoolUserMinAggregateOutputType | null
  _max: SchoolUserMaxAggregateOutputType | null
}

export type SchoolUserMinAggregateOutputType = {
  id: string | null
  role: string | null
  createdAt: Date | null
  updatedAt: Date | null
  schoolId: string | null
  userId: string | null
}

export type SchoolUserMaxAggregateOutputType = {
  id: string | null
  role: string | null
  createdAt: Date | null
  updatedAt: Date | null
  schoolId: string | null
  userId: string | null
}

export type SchoolUserCountAggregateOutputType = {
  id: number
  role: number
  createdAt: number
  updatedAt: number
  schoolId: number
  userId: number
  _all: number
}

export type SchoolUserMinAggregateInputType = {
  id?: true
  role?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
  userId?: true
}

export type SchoolUserMaxAggregateInputType = {
  id?: true
  role?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
  userId?: true
}

export type SchoolUserCountAggregateInputType = {
  id?: true
  role?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
  userId?: true
  _all?: true
}

export type SchoolUserAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SchoolUser to aggregate.
   */
  where?: Prisma.SchoolUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolUsers to fetch.
   */
  orderBy?: Prisma.SchoolUserOrderByWithRelationInput | Prisma.SchoolUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.SchoolUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolUsers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned SchoolUsers
   **/
  _count?: true | SchoolUserCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: SchoolUserMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: SchoolUserMaxAggregateInputType
}

export type GetSchoolUserAggregateType<T extends SchoolUserAggregateArgs> = {
  [P in keyof T & keyof AggregateSchoolUser]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSchoolUser[P]>
    : Prisma.GetScalarType<T[P], AggregateSchoolUser[P]>
}

export type SchoolUserGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SchoolUserWhereInput
  orderBy?:
    | Prisma.SchoolUserOrderByWithAggregationInput
    | Prisma.SchoolUserOrderByWithAggregationInput[]
  by: Prisma.SchoolUserScalarFieldEnum[] | Prisma.SchoolUserScalarFieldEnum
  having?: Prisma.SchoolUserScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SchoolUserCountAggregateInputType | true
  _min?: SchoolUserMinAggregateInputType
  _max?: SchoolUserMaxAggregateInputType
}

export type SchoolUserGroupByOutputType = {
  id: string
  role: string
  createdAt: Date
  updatedAt: Date
  schoolId: string
  userId: string
  _count: SchoolUserCountAggregateOutputType | null
  _min: SchoolUserMinAggregateOutputType | null
  _max: SchoolUserMaxAggregateOutputType | null
}

type GetSchoolUserGroupByPayload<T extends SchoolUserGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SchoolUserGroupByOutputType, T['by']> & {
      [P in keyof T & keyof SchoolUserGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], SchoolUserGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], SchoolUserGroupByOutputType[P]>
    }
  >
>

export type SchoolUserWhereInput = {
  AND?: Prisma.SchoolUserWhereInput | Prisma.SchoolUserWhereInput[]
  OR?: Prisma.SchoolUserWhereInput[]
  NOT?: Prisma.SchoolUserWhereInput | Prisma.SchoolUserWhereInput[]
  id?: Prisma.StringFilter<'SchoolUser'> | string
  role?: Prisma.StringFilter<'SchoolUser'> | string
  createdAt?: Prisma.DateTimeFilter<'SchoolUser'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'SchoolUser'> | Date | string
  schoolId?: Prisma.StringFilter<'SchoolUser'> | string
  userId?: Prisma.StringFilter<'SchoolUser'> | string
  school?: Prisma.XOR<Prisma.SchoolScalarRelationFilter, Prisma.SchoolWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type SchoolUserOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  school?: Prisma.SchoolOrderByWithRelationInput
  user?: Prisma.UserOrderByWithRelationInput
}

export type SchoolUserWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    AND?: Prisma.SchoolUserWhereInput | Prisma.SchoolUserWhereInput[]
    OR?: Prisma.SchoolUserWhereInput[]
    NOT?: Prisma.SchoolUserWhereInput | Prisma.SchoolUserWhereInput[]
    role?: Prisma.StringFilter<'SchoolUser'> | string
    createdAt?: Prisma.DateTimeFilter<'SchoolUser'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'SchoolUser'> | Date | string
    schoolId?: Prisma.StringFilter<'SchoolUser'> | string
    userId?: Prisma.StringFilter<'SchoolUser'> | string
    school?: Prisma.XOR<Prisma.SchoolScalarRelationFilter, Prisma.SchoolWhereInput>
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id'
>

export type SchoolUserOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.SchoolUserCountOrderByAggregateInput
  _max?: Prisma.SchoolUserMaxOrderByAggregateInput
  _min?: Prisma.SchoolUserMinOrderByAggregateInput
}

export type SchoolUserScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.SchoolUserScalarWhereWithAggregatesInput
    | Prisma.SchoolUserScalarWhereWithAggregatesInput[]
  OR?: Prisma.SchoolUserScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.SchoolUserScalarWhereWithAggregatesInput
    | Prisma.SchoolUserScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'SchoolUser'> | string
  role?: Prisma.StringWithAggregatesFilter<'SchoolUser'> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'SchoolUser'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'SchoolUser'> | Date | string
  schoolId?: Prisma.StringWithAggregatesFilter<'SchoolUser'> | string
  userId?: Prisma.StringWithAggregatesFilter<'SchoolUser'> | string
}

export type SchoolUserCreateInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  school: Prisma.SchoolCreateNestedOneWithoutSchoolAdminsInput
  user: Prisma.UserCreateNestedOneWithoutSchoolsInput
}

export type SchoolUserUncheckedCreateInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
  userId: string
}

export type SchoolUserUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneRequiredWithoutSchoolAdminsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutSchoolsNestedInput
}

export type SchoolUserUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SchoolUserCreateManyInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
  userId: string
}

export type SchoolUserUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SchoolUserUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SchoolUserListRelationFilter = {
  every?: Prisma.SchoolUserWhereInput
  some?: Prisma.SchoolUserWhereInput
  none?: Prisma.SchoolUserWhereInput
}

export type SchoolUserOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SchoolUserCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type SchoolUserMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type SchoolUserMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type SchoolUserCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutSchoolInput,
        Prisma.SchoolUserUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SchoolUserCreateWithoutSchoolInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.SchoolUserCreateManySchoolInputEnvelope
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
}

export type SchoolUserUncheckedCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutSchoolInput,
        Prisma.SchoolUserUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SchoolUserCreateWithoutSchoolInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.SchoolUserCreateManySchoolInputEnvelope
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
}

export type SchoolUserUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutSchoolInput,
        Prisma.SchoolUserUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SchoolUserCreateWithoutSchoolInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.SchoolUserCreateManySchoolInputEnvelope
  set?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  disconnect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  delete?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  update?:
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.SchoolUserUpdateManyWithWhereWithoutSchoolInput
    | Prisma.SchoolUserUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.SchoolUserScalarWhereInput | Prisma.SchoolUserScalarWhereInput[]
}

export type SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutSchoolInput,
        Prisma.SchoolUserUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SchoolUserCreateWithoutSchoolInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput
    | Prisma.SchoolUserCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.SchoolUserCreateManySchoolInputEnvelope
  set?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  disconnect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  delete?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  update?:
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.SchoolUserUpdateManyWithWhereWithoutSchoolInput
    | Prisma.SchoolUserUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.SchoolUserScalarWhereInput | Prisma.SchoolUserScalarWhereInput[]
}

export type SchoolUserCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutUserInput,
        Prisma.SchoolUserUncheckedCreateWithoutUserInput
      >
    | Prisma.SchoolUserCreateWithoutUserInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.SchoolUserCreateManyUserInputEnvelope
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
}

export type SchoolUserUncheckedCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutUserInput,
        Prisma.SchoolUserUncheckedCreateWithoutUserInput
      >
    | Prisma.SchoolUserCreateWithoutUserInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.SchoolUserCreateManyUserInputEnvelope
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
}

export type SchoolUserUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutUserInput,
        Prisma.SchoolUserUncheckedCreateWithoutUserInput
      >
    | Prisma.SchoolUserCreateWithoutUserInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutUserInput
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.SchoolUserCreateManyUserInputEnvelope
  set?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  disconnect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  delete?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  update?:
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutUserInput
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.SchoolUserUpdateManyWithWhereWithoutUserInput
    | Prisma.SchoolUserUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.SchoolUserScalarWhereInput | Prisma.SchoolUserScalarWhereInput[]
}

export type SchoolUserUncheckedUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SchoolUserCreateWithoutUserInput,
        Prisma.SchoolUserUncheckedCreateWithoutUserInput
      >
    | Prisma.SchoolUserCreateWithoutUserInput[]
    | Prisma.SchoolUserUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput
    | Prisma.SchoolUserCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutUserInput
    | Prisma.SchoolUserUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.SchoolUserCreateManyUserInputEnvelope
  set?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  disconnect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  delete?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  connect?: Prisma.SchoolUserWhereUniqueInput | Prisma.SchoolUserWhereUniqueInput[]
  update?:
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutUserInput
    | Prisma.SchoolUserUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.SchoolUserUpdateManyWithWhereWithoutUserInput
    | Prisma.SchoolUserUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.SchoolUserScalarWhereInput | Prisma.SchoolUserScalarWhereInput[]
}

export type SchoolUserCreateWithoutSchoolInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutSchoolsInput
}

export type SchoolUserUncheckedCreateWithoutSchoolInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: string
}

export type SchoolUserCreateOrConnectWithoutSchoolInput = {
  where: Prisma.SchoolUserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolUserCreateWithoutSchoolInput,
    Prisma.SchoolUserUncheckedCreateWithoutSchoolInput
  >
}

export type SchoolUserCreateManySchoolInputEnvelope = {
  data: Prisma.SchoolUserCreateManySchoolInput | Prisma.SchoolUserCreateManySchoolInput[]
  skipDuplicates?: boolean
}

export type SchoolUserUpsertWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.SchoolUserWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SchoolUserUpdateWithoutSchoolInput,
    Prisma.SchoolUserUncheckedUpdateWithoutSchoolInput
  >
  create: Prisma.XOR<
    Prisma.SchoolUserCreateWithoutSchoolInput,
    Prisma.SchoolUserUncheckedCreateWithoutSchoolInput
  >
}

export type SchoolUserUpdateWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.SchoolUserWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SchoolUserUpdateWithoutSchoolInput,
    Prisma.SchoolUserUncheckedUpdateWithoutSchoolInput
  >
}

export type SchoolUserUpdateManyWithWhereWithoutSchoolInput = {
  where: Prisma.SchoolUserScalarWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUserUpdateManyMutationInput,
    Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolInput
  >
}

export type SchoolUserScalarWhereInput = {
  AND?: Prisma.SchoolUserScalarWhereInput | Prisma.SchoolUserScalarWhereInput[]
  OR?: Prisma.SchoolUserScalarWhereInput[]
  NOT?: Prisma.SchoolUserScalarWhereInput | Prisma.SchoolUserScalarWhereInput[]
  id?: Prisma.StringFilter<'SchoolUser'> | string
  role?: Prisma.StringFilter<'SchoolUser'> | string
  createdAt?: Prisma.DateTimeFilter<'SchoolUser'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'SchoolUser'> | Date | string
  schoolId?: Prisma.StringFilter<'SchoolUser'> | string
  userId?: Prisma.StringFilter<'SchoolUser'> | string
}

export type SchoolUserCreateWithoutUserInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  school: Prisma.SchoolCreateNestedOneWithoutSchoolAdminsInput
}

export type SchoolUserUncheckedCreateWithoutUserInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
}

export type SchoolUserCreateOrConnectWithoutUserInput = {
  where: Prisma.SchoolUserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolUserCreateWithoutUserInput,
    Prisma.SchoolUserUncheckedCreateWithoutUserInput
  >
}

export type SchoolUserCreateManyUserInputEnvelope = {
  data: Prisma.SchoolUserCreateManyUserInput | Prisma.SchoolUserCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type SchoolUserUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.SchoolUserWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SchoolUserUpdateWithoutUserInput,
    Prisma.SchoolUserUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.SchoolUserCreateWithoutUserInput,
    Prisma.SchoolUserUncheckedCreateWithoutUserInput
  >
}

export type SchoolUserUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.SchoolUserWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SchoolUserUpdateWithoutUserInput,
    Prisma.SchoolUserUncheckedUpdateWithoutUserInput
  >
}

export type SchoolUserUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.SchoolUserScalarWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUserUpdateManyMutationInput,
    Prisma.SchoolUserUncheckedUpdateManyWithoutUserInput
  >
}

export type SchoolUserCreateManySchoolInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: string
}

export type SchoolUserUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutSchoolsNestedInput
}

export type SchoolUserUncheckedUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SchoolUserUncheckedUpdateManyWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SchoolUserCreateManyUserInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
}

export type SchoolUserUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneRequiredWithoutSchoolAdminsNestedInput
}

export type SchoolUserUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SchoolUserUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SchoolUserSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    role?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    userId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['schoolUser']
>

export type SchoolUserSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    role?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    userId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['schoolUser']
>

export type SchoolUserSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    role?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    userId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['schoolUser']
>

export type SchoolUserSelectScalar = {
  id?: boolean
  role?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  schoolId?: boolean
  userId?: boolean
}

export type SchoolUserOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'role' | 'createdAt' | 'updatedAt' | 'schoolId' | 'userId',
  ExtArgs['result']['schoolUser']
>
export type SchoolUserInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type SchoolUserIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type SchoolUserIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $SchoolUserPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'SchoolUser'
  objects: {
    school: Prisma.$SchoolPayload<ExtArgs>
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      role: string
      createdAt: Date
      updatedAt: Date
      schoolId: string
      userId: string
    },
    ExtArgs['result']['schoolUser']
  >
  composites: {}
}

export type SchoolUserGetPayload<S extends boolean | null | undefined | SchoolUserDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$SchoolUserPayload, S>

export type SchoolUserCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<SchoolUserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: SchoolUserCountAggregateInputType | true
}

export interface SchoolUserDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['SchoolUser']
    meta: { name: 'SchoolUser' }
  }
  /**
   * Find zero or one SchoolUser that matches the filter.
   * @param {SchoolUserFindUniqueArgs} args - Arguments to find a SchoolUser
   * @example
   * // Get one SchoolUser
   * const schoolUser = await prisma.schoolUser.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SchoolUserFindUniqueArgs>(
    args: Prisma.SelectSubset<T, SchoolUserFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one SchoolUser that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SchoolUserFindUniqueOrThrowArgs} args - Arguments to find a SchoolUser
   * @example
   * // Get one SchoolUser
   * const schoolUser = await prisma.schoolUser.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SchoolUserFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, SchoolUserFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SchoolUser that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUserFindFirstArgs} args - Arguments to find a SchoolUser
   * @example
   * // Get one SchoolUser
   * const schoolUser = await prisma.schoolUser.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SchoolUserFindFirstArgs>(
    args?: Prisma.SelectSubset<T, SchoolUserFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SchoolUser that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUserFindFirstOrThrowArgs} args - Arguments to find a SchoolUser
   * @example
   * // Get one SchoolUser
   * const schoolUser = await prisma.schoolUser.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SchoolUserFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, SchoolUserFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more SchoolUsers that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUserFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all SchoolUsers
   * const schoolUsers = await prisma.schoolUser.findMany()
   *
   * // Get first 10 SchoolUsers
   * const schoolUsers = await prisma.schoolUser.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const schoolUserWithIdOnly = await prisma.schoolUser.findMany({ select: { id: true } })
   *
   */
  findMany<T extends SchoolUserFindManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolUserFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a SchoolUser.
   * @param {SchoolUserCreateArgs} args - Arguments to create a SchoolUser.
   * @example
   * // Create one SchoolUser
   * const SchoolUser = await prisma.schoolUser.create({
   *   data: {
   *     // ... data to create a SchoolUser
   *   }
   * })
   *
   */
  create<T extends SchoolUserCreateArgs>(
    args: Prisma.SelectSubset<T, SchoolUserCreateArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many SchoolUsers.
   * @param {SchoolUserCreateManyArgs} args - Arguments to create many SchoolUsers.
   * @example
   * // Create many SchoolUsers
   * const schoolUser = await prisma.schoolUser.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends SchoolUserCreateManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolUserCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many SchoolUsers and returns the data saved in the database.
   * @param {SchoolUserCreateManyAndReturnArgs} args - Arguments to create many SchoolUsers.
   * @example
   * // Create many SchoolUsers
   * const schoolUser = await prisma.schoolUser.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many SchoolUsers and only return the `id`
   * const schoolUserWithIdOnly = await prisma.schoolUser.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends SchoolUserCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, SchoolUserCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a SchoolUser.
   * @param {SchoolUserDeleteArgs} args - Arguments to delete one SchoolUser.
   * @example
   * // Delete one SchoolUser
   * const SchoolUser = await prisma.schoolUser.delete({
   *   where: {
   *     // ... filter to delete one SchoolUser
   *   }
   * })
   *
   */
  delete<T extends SchoolUserDeleteArgs>(
    args: Prisma.SelectSubset<T, SchoolUserDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one SchoolUser.
   * @param {SchoolUserUpdateArgs} args - Arguments to update one SchoolUser.
   * @example
   * // Update one SchoolUser
   * const schoolUser = await prisma.schoolUser.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends SchoolUserUpdateArgs>(
    args: Prisma.SelectSubset<T, SchoolUserUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more SchoolUsers.
   * @param {SchoolUserDeleteManyArgs} args - Arguments to filter SchoolUsers to delete.
   * @example
   * // Delete a few SchoolUsers
   * const { count } = await prisma.schoolUser.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends SchoolUserDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolUserDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SchoolUsers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUserUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many SchoolUsers
   * const schoolUser = await prisma.schoolUser.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends SchoolUserUpdateManyArgs>(
    args: Prisma.SelectSubset<T, SchoolUserUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SchoolUsers and returns the data updated in the database.
   * @param {SchoolUserUpdateManyAndReturnArgs} args - Arguments to update many SchoolUsers.
   * @example
   * // Update many SchoolUsers
   * const schoolUser = await prisma.schoolUser.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more SchoolUsers and only return the `id`
   * const schoolUserWithIdOnly = await prisma.schoolUser.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends SchoolUserUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, SchoolUserUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one SchoolUser.
   * @param {SchoolUserUpsertArgs} args - Arguments to update or create a SchoolUser.
   * @example
   * // Update or create a SchoolUser
   * const schoolUser = await prisma.schoolUser.upsert({
   *   create: {
   *     // ... data to create a SchoolUser
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the SchoolUser we want to update
   *   }
   * })
   */
  upsert<T extends SchoolUserUpsertArgs>(
    args: Prisma.SelectSubset<T, SchoolUserUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolUserClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolUserPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of SchoolUsers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUserCountArgs} args - Arguments to filter SchoolUsers to count.
   * @example
   * // Count the number of SchoolUsers
   * const count = await prisma.schoolUser.count({
   *   where: {
   *     // ... the filter for the SchoolUsers we want to count
   *   }
   * })
   **/
  count<T extends SchoolUserCountArgs>(
    args?: Prisma.Subset<T, SchoolUserCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SchoolUserCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a SchoolUser.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends SchoolUserAggregateArgs>(
    args: Prisma.Subset<T, SchoolUserAggregateArgs>
  ): Prisma.PrismaPromise<GetSchoolUserAggregateType<T>>

  /**
   * Group by SchoolUser.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUserGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends SchoolUserGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SchoolUserGroupByArgs['orderBy'] }
      : { orderBy?: SchoolUserGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, SchoolUserGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetSchoolUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SchoolUser model
   */
  readonly fields: SchoolUserFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for SchoolUser.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SchoolUserClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  school<T extends Prisma.SchoolDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.SchoolDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    | runtime.Types.Result.GetResult<
        Prisma.$SchoolPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the SchoolUser model
 */
export interface SchoolUserFieldRefs {
  readonly id: Prisma.FieldRef<'SchoolUser', 'String'>
  readonly role: Prisma.FieldRef<'SchoolUser', 'String'>
  readonly createdAt: Prisma.FieldRef<'SchoolUser', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'SchoolUser', 'DateTime'>
  readonly schoolId: Prisma.FieldRef<'SchoolUser', 'String'>
  readonly userId: Prisma.FieldRef<'SchoolUser', 'String'>
}

// Custom InputTypes
/**
 * SchoolUser findUnique
 */
export type SchoolUserFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * Filter, which SchoolUser to fetch.
   */
  where: Prisma.SchoolUserWhereUniqueInput
}

/**
 * SchoolUser findUniqueOrThrow
 */
export type SchoolUserFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * Filter, which SchoolUser to fetch.
   */
  where: Prisma.SchoolUserWhereUniqueInput
}

/**
 * SchoolUser findFirst
 */
export type SchoolUserFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * Filter, which SchoolUser to fetch.
   */
  where?: Prisma.SchoolUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolUsers to fetch.
   */
  orderBy?: Prisma.SchoolUserOrderByWithRelationInput | Prisma.SchoolUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SchoolUsers.
   */
  cursor?: Prisma.SchoolUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolUsers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SchoolUsers.
   */
  distinct?: Prisma.SchoolUserScalarFieldEnum | Prisma.SchoolUserScalarFieldEnum[]
}

/**
 * SchoolUser findFirstOrThrow
 */
export type SchoolUserFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * Filter, which SchoolUser to fetch.
   */
  where?: Prisma.SchoolUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolUsers to fetch.
   */
  orderBy?: Prisma.SchoolUserOrderByWithRelationInput | Prisma.SchoolUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SchoolUsers.
   */
  cursor?: Prisma.SchoolUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolUsers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SchoolUsers.
   */
  distinct?: Prisma.SchoolUserScalarFieldEnum | Prisma.SchoolUserScalarFieldEnum[]
}

/**
 * SchoolUser findMany
 */
export type SchoolUserFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * Filter, which SchoolUsers to fetch.
   */
  where?: Prisma.SchoolUserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolUsers to fetch.
   */
  orderBy?: Prisma.SchoolUserOrderByWithRelationInput | Prisma.SchoolUserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing SchoolUsers.
   */
  cursor?: Prisma.SchoolUserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolUsers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolUsers.
   */
  skip?: number
  distinct?: Prisma.SchoolUserScalarFieldEnum | Prisma.SchoolUserScalarFieldEnum[]
}

/**
 * SchoolUser create
 */
export type SchoolUserCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * The data needed to create a SchoolUser.
   */
  data: Prisma.XOR<Prisma.SchoolUserCreateInput, Prisma.SchoolUserUncheckedCreateInput>
}

/**
 * SchoolUser createMany
 */
export type SchoolUserCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many SchoolUsers.
   */
  data: Prisma.SchoolUserCreateManyInput | Prisma.SchoolUserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * SchoolUser createManyAndReturn
 */
export type SchoolUserCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * The data used to create many SchoolUsers.
   */
  data: Prisma.SchoolUserCreateManyInput | Prisma.SchoolUserCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * SchoolUser update
 */
export type SchoolUserUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * The data needed to update a SchoolUser.
   */
  data: Prisma.XOR<Prisma.SchoolUserUpdateInput, Prisma.SchoolUserUncheckedUpdateInput>
  /**
   * Choose, which SchoolUser to update.
   */
  where: Prisma.SchoolUserWhereUniqueInput
}

/**
 * SchoolUser updateMany
 */
export type SchoolUserUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update SchoolUsers.
   */
  data: Prisma.XOR<
    Prisma.SchoolUserUpdateManyMutationInput,
    Prisma.SchoolUserUncheckedUpdateManyInput
  >
  /**
   * Filter which SchoolUsers to update
   */
  where?: Prisma.SchoolUserWhereInput
  /**
   * Limit how many SchoolUsers to update.
   */
  limit?: number
}

/**
 * SchoolUser updateManyAndReturn
 */
export type SchoolUserUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * The data used to update SchoolUsers.
   */
  data: Prisma.XOR<
    Prisma.SchoolUserUpdateManyMutationInput,
    Prisma.SchoolUserUncheckedUpdateManyInput
  >
  /**
   * Filter which SchoolUsers to update
   */
  where?: Prisma.SchoolUserWhereInput
  /**
   * Limit how many SchoolUsers to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * SchoolUser upsert
 */
export type SchoolUserUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * The filter to search for the SchoolUser to update in case it exists.
   */
  where: Prisma.SchoolUserWhereUniqueInput
  /**
   * In case the SchoolUser found by the `where` argument doesn't exist, create a new SchoolUser with this data.
   */
  create: Prisma.XOR<Prisma.SchoolUserCreateInput, Prisma.SchoolUserUncheckedCreateInput>
  /**
   * In case the SchoolUser was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SchoolUserUpdateInput, Prisma.SchoolUserUncheckedUpdateInput>
}

/**
 * SchoolUser delete
 */
export type SchoolUserDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  /**
   * Filter which SchoolUser to delete.
   */
  where: Prisma.SchoolUserWhereUniqueInput
}

/**
 * SchoolUser deleteMany
 */
export type SchoolUserDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SchoolUsers to delete
   */
  where?: Prisma.SchoolUserWhereInput
  /**
   * Limit how many SchoolUsers to delete.
   */
  limit?: number
}

/**
 * SchoolUser without action
 */
export type SchoolUserDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
}
