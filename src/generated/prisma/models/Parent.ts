/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Parent` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Parent
 *
 */
export type ParentModel = runtime.Types.Result.DefaultSelection<Prisma.$ParentPayload>

export type AggregateParent = {
  _count: ParentCountAggregateOutputType | null
  _min: ParentMinAggregateOutputType | null
  _max: ParentMaxAggregateOutputType | null
}

export type ParentMinAggregateOutputType = {
  id: string | null
  userId: string | null
  occupation: string | null
  relationship: string | null
}

export type ParentMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  occupation: string | null
  relationship: string | null
}

export type ParentCountAggregateOutputType = {
  id: number
  userId: number
  occupation: number
  relationship: number
  _all: number
}

export type ParentMinAggregateInputType = {
  id?: true
  userId?: true
  occupation?: true
  relationship?: true
}

export type ParentMaxAggregateInputType = {
  id?: true
  userId?: true
  occupation?: true
  relationship?: true
}

export type ParentCountAggregateInputType = {
  id?: true
  userId?: true
  occupation?: true
  relationship?: true
  _all?: true
}

export type ParentAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Parent to aggregate.
   */
  where?: Prisma.ParentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Parents to fetch.
   */
  orderBy?: Prisma.ParentOrderByWithRelationInput | Prisma.ParentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.ParentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Parents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Parents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Parents
   **/
  _count?: true | ParentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: ParentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: ParentMaxAggregateInputType
}

export type GetParentAggregateType<T extends ParentAggregateArgs> = {
  [P in keyof T & keyof AggregateParent]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateParent[P]>
    : Prisma.GetScalarType<T[P], AggregateParent[P]>
}

export type ParentGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.ParentWhereInput
  orderBy?: Prisma.ParentOrderByWithAggregationInput | Prisma.ParentOrderByWithAggregationInput[]
  by: Prisma.ParentScalarFieldEnum[] | Prisma.ParentScalarFieldEnum
  having?: Prisma.ParentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ParentCountAggregateInputType | true
  _min?: ParentMinAggregateInputType
  _max?: ParentMaxAggregateInputType
}

export type ParentGroupByOutputType = {
  id: string
  userId: string
  occupation: string | null
  relationship: string
  _count: ParentCountAggregateOutputType | null
  _min: ParentMinAggregateOutputType | null
  _max: ParentMaxAggregateOutputType | null
}

type GetParentGroupByPayload<T extends ParentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ParentGroupByOutputType, T['by']> & {
      [P in keyof T & keyof ParentGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], ParentGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], ParentGroupByOutputType[P]>
    }
  >
>

export type ParentWhereInput = {
  AND?: Prisma.ParentWhereInput | Prisma.ParentWhereInput[]
  OR?: Prisma.ParentWhereInput[]
  NOT?: Prisma.ParentWhereInput | Prisma.ParentWhereInput[]
  id?: Prisma.StringFilter<'Parent'> | string
  userId?: Prisma.StringFilter<'Parent'> | string
  occupation?: Prisma.StringNullableFilter<'Parent'> | string | null
  relationship?: Prisma.StringFilter<'Parent'> | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type ParentOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  occupation?: Prisma.SortOrderInput | Prisma.SortOrder
  relationship?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type ParentWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    userId?: string
    AND?: Prisma.ParentWhereInput | Prisma.ParentWhereInput[]
    OR?: Prisma.ParentWhereInput[]
    NOT?: Prisma.ParentWhereInput | Prisma.ParentWhereInput[]
    occupation?: Prisma.StringNullableFilter<'Parent'> | string | null
    relationship?: Prisma.StringFilter<'Parent'> | string
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id' | 'userId'
>

export type ParentOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  occupation?: Prisma.SortOrderInput | Prisma.SortOrder
  relationship?: Prisma.SortOrder
  _count?: Prisma.ParentCountOrderByAggregateInput
  _max?: Prisma.ParentMaxOrderByAggregateInput
  _min?: Prisma.ParentMinOrderByAggregateInput
}

export type ParentScalarWhereWithAggregatesInput = {
  AND?: Prisma.ParentScalarWhereWithAggregatesInput | Prisma.ParentScalarWhereWithAggregatesInput[]
  OR?: Prisma.ParentScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ParentScalarWhereWithAggregatesInput | Prisma.ParentScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Parent'> | string
  userId?: Prisma.StringWithAggregatesFilter<'Parent'> | string
  occupation?: Prisma.StringNullableWithAggregatesFilter<'Parent'> | string | null
  relationship?: Prisma.StringWithAggregatesFilter<'Parent'> | string
}

export type ParentCreateInput = {
  id?: string
  occupation?: string | null
  relationship: string
  user: Prisma.UserCreateNestedOneWithoutParentProfileInput
}

export type ParentUncheckedCreateInput = {
  id?: string
  userId: string
  occupation?: string | null
  relationship: string
}

export type ParentUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  occupation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  relationship?: Prisma.StringFieldUpdateOperationsInput | string
  user?: Prisma.UserUpdateOneRequiredWithoutParentProfileNestedInput
}

export type ParentUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  occupation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  relationship?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ParentCreateManyInput = {
  id?: string
  userId: string
  occupation?: string | null
  relationship: string
}

export type ParentUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  occupation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  relationship?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ParentUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  occupation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  relationship?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ParentNullableScalarRelationFilter = {
  is?: Prisma.ParentWhereInput | null
  isNot?: Prisma.ParentWhereInput | null
}

export type ParentCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  occupation?: Prisma.SortOrder
  relationship?: Prisma.SortOrder
}

export type ParentMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  occupation?: Prisma.SortOrder
  relationship?: Prisma.SortOrder
}

export type ParentMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  occupation?: Prisma.SortOrder
  relationship?: Prisma.SortOrder
}

export type ParentCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.ParentCreateWithoutUserInput,
    Prisma.ParentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.ParentCreateOrConnectWithoutUserInput
  connect?: Prisma.ParentWhereUniqueInput
}

export type ParentUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.ParentCreateWithoutUserInput,
    Prisma.ParentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.ParentCreateOrConnectWithoutUserInput
  connect?: Prisma.ParentWhereUniqueInput
}

export type ParentUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.ParentCreateWithoutUserInput,
    Prisma.ParentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.ParentCreateOrConnectWithoutUserInput
  upsert?: Prisma.ParentUpsertWithoutUserInput
  disconnect?: Prisma.ParentWhereInput | boolean
  delete?: Prisma.ParentWhereInput | boolean
  connect?: Prisma.ParentWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.ParentUpdateToOneWithWhereWithoutUserInput,
      Prisma.ParentUpdateWithoutUserInput
    >,
    Prisma.ParentUncheckedUpdateWithoutUserInput
  >
}

export type ParentUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.ParentCreateWithoutUserInput,
    Prisma.ParentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.ParentCreateOrConnectWithoutUserInput
  upsert?: Prisma.ParentUpsertWithoutUserInput
  disconnect?: Prisma.ParentWhereInput | boolean
  delete?: Prisma.ParentWhereInput | boolean
  connect?: Prisma.ParentWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.ParentUpdateToOneWithWhereWithoutUserInput,
      Prisma.ParentUpdateWithoutUserInput
    >,
    Prisma.ParentUncheckedUpdateWithoutUserInput
  >
}

export type ParentCreateWithoutUserInput = {
  id?: string
  occupation?: string | null
  relationship: string
}

export type ParentUncheckedCreateWithoutUserInput = {
  id?: string
  occupation?: string | null
  relationship: string
}

export type ParentCreateOrConnectWithoutUserInput = {
  where: Prisma.ParentWhereUniqueInput
  create: Prisma.XOR<
    Prisma.ParentCreateWithoutUserInput,
    Prisma.ParentUncheckedCreateWithoutUserInput
  >
}

export type ParentUpsertWithoutUserInput = {
  update: Prisma.XOR<
    Prisma.ParentUpdateWithoutUserInput,
    Prisma.ParentUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.ParentCreateWithoutUserInput,
    Prisma.ParentUncheckedCreateWithoutUserInput
  >
  where?: Prisma.ParentWhereInput
}

export type ParentUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.ParentWhereInput
  data: Prisma.XOR<
    Prisma.ParentUpdateWithoutUserInput,
    Prisma.ParentUncheckedUpdateWithoutUserInput
  >
}

export type ParentUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  occupation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  relationship?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ParentUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  occupation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  relationship?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ParentSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    occupation?: boolean
    relationship?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['parent']
>

export type ParentSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    occupation?: boolean
    relationship?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['parent']
>

export type ParentSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    occupation?: boolean
    relationship?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['parent']
>

export type ParentSelectScalar = {
  id?: boolean
  userId?: boolean
  occupation?: boolean
  relationship?: boolean
}

export type ParentOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'userId' | 'occupation' | 'relationship',
  ExtArgs['result']['parent']
>
export type ParentInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ParentIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ParentIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $ParentPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Parent'
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      userId: string
      occupation: string | null
      relationship: string
    },
    ExtArgs['result']['parent']
  >
  composites: {}
}

export type ParentGetPayload<S extends boolean | null | undefined | ParentDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$ParentPayload, S>

export type ParentCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<ParentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: ParentCountAggregateInputType | true
}

export interface ParentDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Parent']; meta: { name: 'Parent' } }
  /**
   * Find zero or one Parent that matches the filter.
   * @param {ParentFindUniqueArgs} args - Arguments to find a Parent
   * @example
   * // Get one Parent
   * const parent = await prisma.parent.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ParentFindUniqueArgs>(
    args: Prisma.SelectSubset<T, ParentFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<
      Prisma.$ParentPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Parent that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ParentFindUniqueOrThrowArgs} args - Arguments to find a Parent
   * @example
   * // Get one Parent
   * const parent = await prisma.parent.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ParentFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, ParentFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<
      Prisma.$ParentPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Parent that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ParentFindFirstArgs} args - Arguments to find a Parent
   * @example
   * // Get one Parent
   * const parent = await prisma.parent.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ParentFindFirstArgs>(
    args?: Prisma.SelectSubset<T, ParentFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<
      Prisma.$ParentPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Parent that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ParentFindFirstOrThrowArgs} args - Arguments to find a Parent
   * @example
   * // Get one Parent
   * const parent = await prisma.parent.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ParentFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, ParentFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<
      Prisma.$ParentPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Parents that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ParentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Parents
   * const parents = await prisma.parent.findMany()
   *
   * // Get first 10 Parents
   * const parents = await prisma.parent.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const parentWithIdOnly = await prisma.parent.findMany({ select: { id: true } })
   *
   */
  findMany<T extends ParentFindManyArgs>(
    args?: Prisma.SelectSubset<T, ParentFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<Prisma.$ParentPayload<ExtArgs>, T, 'findMany', GlobalOmitOptions>
  >

  /**
   * Create a Parent.
   * @param {ParentCreateArgs} args - Arguments to create a Parent.
   * @example
   * // Create one Parent
   * const Parent = await prisma.parent.create({
   *   data: {
   *     // ... data to create a Parent
   *   }
   * })
   *
   */
  create<T extends ParentCreateArgs>(
    args: Prisma.SelectSubset<T, ParentCreateArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<Prisma.$ParentPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Parents.
   * @param {ParentCreateManyArgs} args - Arguments to create many Parents.
   * @example
   * // Create many Parents
   * const parent = await prisma.parent.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends ParentCreateManyArgs>(
    args?: Prisma.SelectSubset<T, ParentCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Parents and returns the data saved in the database.
   * @param {ParentCreateManyAndReturnArgs} args - Arguments to create many Parents.
   * @example
   * // Create many Parents
   * const parent = await prisma.parent.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Parents and only return the `id`
   * const parentWithIdOnly = await prisma.parent.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends ParentCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, ParentCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$ParentPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Parent.
   * @param {ParentDeleteArgs} args - Arguments to delete one Parent.
   * @example
   * // Delete one Parent
   * const Parent = await prisma.parent.delete({
   *   where: {
   *     // ... filter to delete one Parent
   *   }
   * })
   *
   */
  delete<T extends ParentDeleteArgs>(
    args: Prisma.SelectSubset<T, ParentDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<Prisma.$ParentPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Parent.
   * @param {ParentUpdateArgs} args - Arguments to update one Parent.
   * @example
   * // Update one Parent
   * const parent = await prisma.parent.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends ParentUpdateArgs>(
    args: Prisma.SelectSubset<T, ParentUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<Prisma.$ParentPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Parents.
   * @param {ParentDeleteManyArgs} args - Arguments to filter Parents to delete.
   * @example
   * // Delete a few Parents
   * const { count } = await prisma.parent.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends ParentDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, ParentDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Parents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ParentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Parents
   * const parent = await prisma.parent.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends ParentUpdateManyArgs>(
    args: Prisma.SelectSubset<T, ParentUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Parents and returns the data updated in the database.
   * @param {ParentUpdateManyAndReturnArgs} args - Arguments to update many Parents.
   * @example
   * // Update many Parents
   * const parent = await prisma.parent.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Parents and only return the `id`
   * const parentWithIdOnly = await prisma.parent.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends ParentUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, ParentUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$ParentPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Parent.
   * @param {ParentUpsertArgs} args - Arguments to update or create a Parent.
   * @example
   * // Update or create a Parent
   * const parent = await prisma.parent.upsert({
   *   create: {
   *     // ... data to create a Parent
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Parent we want to update
   *   }
   * })
   */
  upsert<T extends ParentUpsertArgs>(
    args: Prisma.SelectSubset<T, ParentUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<Prisma.$ParentPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Parents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ParentCountArgs} args - Arguments to filter Parents to count.
   * @example
   * // Count the number of Parents
   * const count = await prisma.parent.count({
   *   where: {
   *     // ... the filter for the Parents we want to count
   *   }
   * })
   **/
  count<T extends ParentCountArgs>(
    args?: Prisma.Subset<T, ParentCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ParentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Parent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ParentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends ParentAggregateArgs>(
    args: Prisma.Subset<T, ParentAggregateArgs>
  ): Prisma.PrismaPromise<GetParentAggregateType<T>>

  /**
   * Group by Parent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ParentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends ParentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ParentGroupByArgs['orderBy'] }
      : { orderBy?: ParentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, ParentGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetParentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Parent model
   */
  readonly fields: ParentFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Parent.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ParentClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Parent model
 */
export interface ParentFieldRefs {
  readonly id: Prisma.FieldRef<'Parent', 'String'>
  readonly userId: Prisma.FieldRef<'Parent', 'String'>
  readonly occupation: Prisma.FieldRef<'Parent', 'String'>
  readonly relationship: Prisma.FieldRef<'Parent', 'String'>
}

// Custom InputTypes
/**
 * Parent findUnique
 */
export type ParentFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * Filter, which Parent to fetch.
   */
  where: Prisma.ParentWhereUniqueInput
}

/**
 * Parent findUniqueOrThrow
 */
export type ParentFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * Filter, which Parent to fetch.
   */
  where: Prisma.ParentWhereUniqueInput
}

/**
 * Parent findFirst
 */
export type ParentFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * Filter, which Parent to fetch.
   */
  where?: Prisma.ParentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Parents to fetch.
   */
  orderBy?: Prisma.ParentOrderByWithRelationInput | Prisma.ParentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Parents.
   */
  cursor?: Prisma.ParentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Parents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Parents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Parents.
   */
  distinct?: Prisma.ParentScalarFieldEnum | Prisma.ParentScalarFieldEnum[]
}

/**
 * Parent findFirstOrThrow
 */
export type ParentFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * Filter, which Parent to fetch.
   */
  where?: Prisma.ParentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Parents to fetch.
   */
  orderBy?: Prisma.ParentOrderByWithRelationInput | Prisma.ParentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Parents.
   */
  cursor?: Prisma.ParentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Parents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Parents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Parents.
   */
  distinct?: Prisma.ParentScalarFieldEnum | Prisma.ParentScalarFieldEnum[]
}

/**
 * Parent findMany
 */
export type ParentFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * Filter, which Parents to fetch.
   */
  where?: Prisma.ParentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Parents to fetch.
   */
  orderBy?: Prisma.ParentOrderByWithRelationInput | Prisma.ParentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Parents.
   */
  cursor?: Prisma.ParentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Parents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Parents.
   */
  skip?: number
  distinct?: Prisma.ParentScalarFieldEnum | Prisma.ParentScalarFieldEnum[]
}

/**
 * Parent create
 */
export type ParentCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * The data needed to create a Parent.
   */
  data: Prisma.XOR<Prisma.ParentCreateInput, Prisma.ParentUncheckedCreateInput>
}

/**
 * Parent createMany
 */
export type ParentCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Parents.
   */
  data: Prisma.ParentCreateManyInput | Prisma.ParentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Parent createManyAndReturn
 */
export type ParentCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * The data used to create many Parents.
   */
  data: Prisma.ParentCreateManyInput | Prisma.ParentCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Parent update
 */
export type ParentUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * The data needed to update a Parent.
   */
  data: Prisma.XOR<Prisma.ParentUpdateInput, Prisma.ParentUncheckedUpdateInput>
  /**
   * Choose, which Parent to update.
   */
  where: Prisma.ParentWhereUniqueInput
}

/**
 * Parent updateMany
 */
export type ParentUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Parents.
   */
  data: Prisma.XOR<Prisma.ParentUpdateManyMutationInput, Prisma.ParentUncheckedUpdateManyInput>
  /**
   * Filter which Parents to update
   */
  where?: Prisma.ParentWhereInput
  /**
   * Limit how many Parents to update.
   */
  limit?: number
}

/**
 * Parent updateManyAndReturn
 */
export type ParentUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * The data used to update Parents.
   */
  data: Prisma.XOR<Prisma.ParentUpdateManyMutationInput, Prisma.ParentUncheckedUpdateManyInput>
  /**
   * Filter which Parents to update
   */
  where?: Prisma.ParentWhereInput
  /**
   * Limit how many Parents to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Parent upsert
 */
export type ParentUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * The filter to search for the Parent to update in case it exists.
   */
  where: Prisma.ParentWhereUniqueInput
  /**
   * In case the Parent found by the `where` argument doesn't exist, create a new Parent with this data.
   */
  create: Prisma.XOR<Prisma.ParentCreateInput, Prisma.ParentUncheckedCreateInput>
  /**
   * In case the Parent was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ParentUpdateInput, Prisma.ParentUncheckedUpdateInput>
}

/**
 * Parent delete
 */
export type ParentDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  /**
   * Filter which Parent to delete.
   */
  where: Prisma.ParentWhereUniqueInput
}

/**
 * Parent deleteMany
 */
export type ParentDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Parents to delete
   */
  where?: Prisma.ParentWhereInput
  /**
   * Limit how many Parents to delete.
   */
  limit?: number
}

/**
 * Parent without action
 */
export type ParentDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
}
