/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `School` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model School
 *
 */
export type SchoolModel = runtime.Types.Result.DefaultSelection<Prisma.$SchoolPayload>

export type AggregateSchool = {
  _count: SchoolCountAggregateOutputType | null
  _avg: SchoolAvgAggregateOutputType | null
  _sum: SchoolSumAggregateOutputType | null
  _min: SchoolMinAggregateOutputType | null
  _max: SchoolMaxAggregateOutputType | null
}

export type SchoolAvgAggregateOutputType = {
  typeId: number | null
}

export type SchoolSumAggregateOutputType = {
  typeId: number | null
}

export type SchoolMinAggregateOutputType = {
  id: string | null
  name: string | null
  address: string | null
  phone: string | null
  email: string | null
  website: string | null
  motto: string | null
  description: string | null
  logo: string | null
  certification: string | null
  isActive: boolean | null
  verified: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  typeId: number | null
}

export type SchoolMaxAggregateOutputType = {
  id: string | null
  name: string | null
  address: string | null
  phone: string | null
  email: string | null
  website: string | null
  motto: string | null
  description: string | null
  logo: string | null
  certification: string | null
  isActive: boolean | null
  verified: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  typeId: number | null
}

export type SchoolCountAggregateOutputType = {
  id: number
  name: number
  address: number
  phone: number
  email: number
  website: number
  motto: number
  description: number
  logo: number
  certification: number
  isActive: number
  verified: number
  createdAt: number
  updatedAt: number
  typeId: number
  _all: number
}

export type SchoolAvgAggregateInputType = {
  typeId?: true
}

export type SchoolSumAggregateInputType = {
  typeId?: true
}

export type SchoolMinAggregateInputType = {
  id?: true
  name?: true
  address?: true
  phone?: true
  email?: true
  website?: true
  motto?: true
  description?: true
  logo?: true
  certification?: true
  isActive?: true
  verified?: true
  createdAt?: true
  updatedAt?: true
  typeId?: true
}

export type SchoolMaxAggregateInputType = {
  id?: true
  name?: true
  address?: true
  phone?: true
  email?: true
  website?: true
  motto?: true
  description?: true
  logo?: true
  certification?: true
  isActive?: true
  verified?: true
  createdAt?: true
  updatedAt?: true
  typeId?: true
}

export type SchoolCountAggregateInputType = {
  id?: true
  name?: true
  address?: true
  phone?: true
  email?: true
  website?: true
  motto?: true
  description?: true
  logo?: true
  certification?: true
  isActive?: true
  verified?: true
  createdAt?: true
  updatedAt?: true
  typeId?: true
  _all?: true
}

export type SchoolAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which School to aggregate.
   */
  where?: Prisma.SchoolWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Schools to fetch.
   */
  orderBy?: Prisma.SchoolOrderByWithRelationInput | Prisma.SchoolOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.SchoolWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Schools from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Schools.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Schools
   **/
  _count?: true | SchoolCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to average
   **/
  _avg?: SchoolAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to sum
   **/
  _sum?: SchoolSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: SchoolMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: SchoolMaxAggregateInputType
}

export type GetSchoolAggregateType<T extends SchoolAggregateArgs> = {
  [P in keyof T & keyof AggregateSchool]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSchool[P]>
    : Prisma.GetScalarType<T[P], AggregateSchool[P]>
}

export type SchoolGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SchoolWhereInput
  orderBy?: Prisma.SchoolOrderByWithAggregationInput | Prisma.SchoolOrderByWithAggregationInput[]
  by: Prisma.SchoolScalarFieldEnum[] | Prisma.SchoolScalarFieldEnum
  having?: Prisma.SchoolScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SchoolCountAggregateInputType | true
  _avg?: SchoolAvgAggregateInputType
  _sum?: SchoolSumAggregateInputType
  _min?: SchoolMinAggregateInputType
  _max?: SchoolMaxAggregateInputType
}

export type SchoolGroupByOutputType = {
  id: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto: string | null
  description: string | null
  logo: string | null
  certification: string | null
  isActive: boolean
  verified: boolean
  createdAt: Date
  updatedAt: Date
  typeId: number
  _count: SchoolCountAggregateOutputType | null
  _avg: SchoolAvgAggregateOutputType | null
  _sum: SchoolSumAggregateOutputType | null
  _min: SchoolMinAggregateOutputType | null
  _max: SchoolMaxAggregateOutputType | null
}

type GetSchoolGroupByPayload<T extends SchoolGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SchoolGroupByOutputType, T['by']> & {
      [P in keyof T & keyof SchoolGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], SchoolGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], SchoolGroupByOutputType[P]>
    }
  >
>

export type SchoolWhereInput = {
  AND?: Prisma.SchoolWhereInput | Prisma.SchoolWhereInput[]
  OR?: Prisma.SchoolWhereInput[]
  NOT?: Prisma.SchoolWhereInput | Prisma.SchoolWhereInput[]
  id?: Prisma.StringFilter<'School'> | string
  name?: Prisma.StringFilter<'School'> | string
  address?: Prisma.StringFilter<'School'> | string
  phone?: Prisma.StringFilter<'School'> | string
  email?: Prisma.StringFilter<'School'> | string
  website?: Prisma.StringFilter<'School'> | string
  motto?: Prisma.StringNullableFilter<'School'> | string | null
  description?: Prisma.StringNullableFilter<'School'> | string | null
  logo?: Prisma.StringNullableFilter<'School'> | string | null
  certification?: Prisma.StringNullableFilter<'School'> | string | null
  isActive?: Prisma.BoolFilter<'School'> | boolean
  verified?: Prisma.BoolFilter<'School'> | boolean
  createdAt?: Prisma.DateTimeFilter<'School'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'School'> | Date | string
  typeId?: Prisma.IntFilter<'School'> | number
  type?: Prisma.XOR<Prisma.SchoolTypeScalarRelationFilter, Prisma.SchoolTypeWhereInput>
  students?: Prisma.StudentListRelationFilter
  departments?: Prisma.DepartmentListRelationFilter
  schoolAdmins?: Prisma.SchoolUserListRelationFilter
  sessions?: Prisma.SessionListRelationFilter
  subjects?: Prisma.SubjectListRelationFilter
  classes?: Prisma.ClassListRelationFilter
}

export type SchoolOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  address?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  email?: Prisma.SortOrder
  website?: Prisma.SortOrder
  motto?: Prisma.SortOrderInput | Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  logo?: Prisma.SortOrderInput | Prisma.SortOrder
  certification?: Prisma.SortOrderInput | Prisma.SortOrder
  isActive?: Prisma.SortOrder
  verified?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  typeId?: Prisma.SortOrder
  type?: Prisma.SchoolTypeOrderByWithRelationInput
  students?: Prisma.StudentOrderByRelationAggregateInput
  departments?: Prisma.DepartmentOrderByRelationAggregateInput
  schoolAdmins?: Prisma.SchoolUserOrderByRelationAggregateInput
  sessions?: Prisma.SessionOrderByRelationAggregateInput
  subjects?: Prisma.SubjectOrderByRelationAggregateInput
  classes?: Prisma.ClassOrderByRelationAggregateInput
}

export type SchoolWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    AND?: Prisma.SchoolWhereInput | Prisma.SchoolWhereInput[]
    OR?: Prisma.SchoolWhereInput[]
    NOT?: Prisma.SchoolWhereInput | Prisma.SchoolWhereInput[]
    name?: Prisma.StringFilter<'School'> | string
    address?: Prisma.StringFilter<'School'> | string
    phone?: Prisma.StringFilter<'School'> | string
    email?: Prisma.StringFilter<'School'> | string
    website?: Prisma.StringFilter<'School'> | string
    motto?: Prisma.StringNullableFilter<'School'> | string | null
    description?: Prisma.StringNullableFilter<'School'> | string | null
    logo?: Prisma.StringNullableFilter<'School'> | string | null
    certification?: Prisma.StringNullableFilter<'School'> | string | null
    isActive?: Prisma.BoolFilter<'School'> | boolean
    verified?: Prisma.BoolFilter<'School'> | boolean
    createdAt?: Prisma.DateTimeFilter<'School'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'School'> | Date | string
    typeId?: Prisma.IntFilter<'School'> | number
    type?: Prisma.XOR<Prisma.SchoolTypeScalarRelationFilter, Prisma.SchoolTypeWhereInput>
    students?: Prisma.StudentListRelationFilter
    departments?: Prisma.DepartmentListRelationFilter
    schoolAdmins?: Prisma.SchoolUserListRelationFilter
    sessions?: Prisma.SessionListRelationFilter
    subjects?: Prisma.SubjectListRelationFilter
    classes?: Prisma.ClassListRelationFilter
  },
  'id'
>

export type SchoolOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  address?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  email?: Prisma.SortOrder
  website?: Prisma.SortOrder
  motto?: Prisma.SortOrderInput | Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  logo?: Prisma.SortOrderInput | Prisma.SortOrder
  certification?: Prisma.SortOrderInput | Prisma.SortOrder
  isActive?: Prisma.SortOrder
  verified?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  typeId?: Prisma.SortOrder
  _count?: Prisma.SchoolCountOrderByAggregateInput
  _avg?: Prisma.SchoolAvgOrderByAggregateInput
  _max?: Prisma.SchoolMaxOrderByAggregateInput
  _min?: Prisma.SchoolMinOrderByAggregateInput
  _sum?: Prisma.SchoolSumOrderByAggregateInput
}

export type SchoolScalarWhereWithAggregatesInput = {
  AND?: Prisma.SchoolScalarWhereWithAggregatesInput | Prisma.SchoolScalarWhereWithAggregatesInput[]
  OR?: Prisma.SchoolScalarWhereWithAggregatesInput[]
  NOT?: Prisma.SchoolScalarWhereWithAggregatesInput | Prisma.SchoolScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'School'> | string
  name?: Prisma.StringWithAggregatesFilter<'School'> | string
  address?: Prisma.StringWithAggregatesFilter<'School'> | string
  phone?: Prisma.StringWithAggregatesFilter<'School'> | string
  email?: Prisma.StringWithAggregatesFilter<'School'> | string
  website?: Prisma.StringWithAggregatesFilter<'School'> | string
  motto?: Prisma.StringNullableWithAggregatesFilter<'School'> | string | null
  description?: Prisma.StringNullableWithAggregatesFilter<'School'> | string | null
  logo?: Prisma.StringNullableWithAggregatesFilter<'School'> | string | null
  certification?: Prisma.StringNullableWithAggregatesFilter<'School'> | string | null
  isActive?: Prisma.BoolWithAggregatesFilter<'School'> | boolean
  verified?: Prisma.BoolWithAggregatesFilter<'School'> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'School'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'School'> | Date | string
  typeId?: Prisma.IntWithAggregatesFilter<'School'> | number
}

export type SchoolCreateInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  type: Prisma.SchoolTypeCreateNestedOneWithoutSchoolsInput
  students?: Prisma.StudentCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
  students?: Prisma.StudentUncheckedCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentUncheckedCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectUncheckedCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  type?: Prisma.SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput
  students?: Prisma.StudentUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
  students?: Prisma.StudentUncheckedUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUncheckedUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUncheckedUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolCreateManyInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
}

export type SchoolUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SchoolUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
}

export type SchoolScalarRelationFilter = {
  is?: Prisma.SchoolWhereInput
  isNot?: Prisma.SchoolWhereInput
}

export type SchoolListRelationFilter = {
  every?: Prisma.SchoolWhereInput
  some?: Prisma.SchoolWhereInput
  none?: Prisma.SchoolWhereInput
}

export type SchoolOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SchoolCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  address?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  email?: Prisma.SortOrder
  website?: Prisma.SortOrder
  motto?: Prisma.SortOrder
  description?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  certification?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  verified?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  typeId?: Prisma.SortOrder
}

export type SchoolAvgOrderByAggregateInput = {
  typeId?: Prisma.SortOrder
}

export type SchoolMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  address?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  email?: Prisma.SortOrder
  website?: Prisma.SortOrder
  motto?: Prisma.SortOrder
  description?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  certification?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  verified?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  typeId?: Prisma.SortOrder
}

export type SchoolMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  address?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  email?: Prisma.SortOrder
  website?: Prisma.SortOrder
  motto?: Prisma.SortOrder
  description?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  certification?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  verified?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  typeId?: Prisma.SortOrder
}

export type SchoolSumOrderByAggregateInput = {
  typeId?: Prisma.SortOrder
}

export type SchoolNullableScalarRelationFilter = {
  is?: Prisma.SchoolWhereInput | null
  isNot?: Prisma.SchoolWhereInput | null
}

export type SchoolCreateNestedOneWithoutClassesInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutClassesInput,
    Prisma.SchoolUncheckedCreateWithoutClassesInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutClassesInput
  connect?: Prisma.SchoolWhereUniqueInput
}

export type SchoolUpdateOneRequiredWithoutClassesNestedInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutClassesInput,
    Prisma.SchoolUncheckedCreateWithoutClassesInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutClassesInput
  upsert?: Prisma.SchoolUpsertWithoutClassesInput
  connect?: Prisma.SchoolWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SchoolUpdateToOneWithWhereWithoutClassesInput,
      Prisma.SchoolUpdateWithoutClassesInput
    >,
    Prisma.SchoolUncheckedUpdateWithoutClassesInput
  >
}

export type SchoolCreateNestedOneWithoutSubjectsInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutSubjectsInput,
    Prisma.SchoolUncheckedCreateWithoutSubjectsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutSubjectsInput
  connect?: Prisma.SchoolWhereUniqueInput
}

export type SchoolUpdateOneRequiredWithoutSubjectsNestedInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutSubjectsInput,
    Prisma.SchoolUncheckedCreateWithoutSubjectsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutSubjectsInput
  upsert?: Prisma.SchoolUpsertWithoutSubjectsInput
  connect?: Prisma.SchoolWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SchoolUpdateToOneWithWhereWithoutSubjectsInput,
      Prisma.SchoolUpdateWithoutSubjectsInput
    >,
    Prisma.SchoolUncheckedUpdateWithoutSubjectsInput
  >
}

export type SchoolCreateNestedManyWithoutTypeInput = {
  create?:
    | Prisma.XOR<Prisma.SchoolCreateWithoutTypeInput, Prisma.SchoolUncheckedCreateWithoutTypeInput>
    | Prisma.SchoolCreateWithoutTypeInput[]
    | Prisma.SchoolUncheckedCreateWithoutTypeInput[]
  connectOrCreate?:
    | Prisma.SchoolCreateOrConnectWithoutTypeInput
    | Prisma.SchoolCreateOrConnectWithoutTypeInput[]
  createMany?: Prisma.SchoolCreateManyTypeInputEnvelope
  connect?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
}

export type SchoolUncheckedCreateNestedManyWithoutTypeInput = {
  create?:
    | Prisma.XOR<Prisma.SchoolCreateWithoutTypeInput, Prisma.SchoolUncheckedCreateWithoutTypeInput>
    | Prisma.SchoolCreateWithoutTypeInput[]
    | Prisma.SchoolUncheckedCreateWithoutTypeInput[]
  connectOrCreate?:
    | Prisma.SchoolCreateOrConnectWithoutTypeInput
    | Prisma.SchoolCreateOrConnectWithoutTypeInput[]
  createMany?: Prisma.SchoolCreateManyTypeInputEnvelope
  connect?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
}

export type SchoolUpdateManyWithoutTypeNestedInput = {
  create?:
    | Prisma.XOR<Prisma.SchoolCreateWithoutTypeInput, Prisma.SchoolUncheckedCreateWithoutTypeInput>
    | Prisma.SchoolCreateWithoutTypeInput[]
    | Prisma.SchoolUncheckedCreateWithoutTypeInput[]
  connectOrCreate?:
    | Prisma.SchoolCreateOrConnectWithoutTypeInput
    | Prisma.SchoolCreateOrConnectWithoutTypeInput[]
  upsert?:
    | Prisma.SchoolUpsertWithWhereUniqueWithoutTypeInput
    | Prisma.SchoolUpsertWithWhereUniqueWithoutTypeInput[]
  createMany?: Prisma.SchoolCreateManyTypeInputEnvelope
  set?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  disconnect?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  delete?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  connect?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  update?:
    | Prisma.SchoolUpdateWithWhereUniqueWithoutTypeInput
    | Prisma.SchoolUpdateWithWhereUniqueWithoutTypeInput[]
  updateMany?:
    | Prisma.SchoolUpdateManyWithWhereWithoutTypeInput
    | Prisma.SchoolUpdateManyWithWhereWithoutTypeInput[]
  deleteMany?: Prisma.SchoolScalarWhereInput | Prisma.SchoolScalarWhereInput[]
}

export type SchoolUncheckedUpdateManyWithoutTypeNestedInput = {
  create?:
    | Prisma.XOR<Prisma.SchoolCreateWithoutTypeInput, Prisma.SchoolUncheckedCreateWithoutTypeInput>
    | Prisma.SchoolCreateWithoutTypeInput[]
    | Prisma.SchoolUncheckedCreateWithoutTypeInput[]
  connectOrCreate?:
    | Prisma.SchoolCreateOrConnectWithoutTypeInput
    | Prisma.SchoolCreateOrConnectWithoutTypeInput[]
  upsert?:
    | Prisma.SchoolUpsertWithWhereUniqueWithoutTypeInput
    | Prisma.SchoolUpsertWithWhereUniqueWithoutTypeInput[]
  createMany?: Prisma.SchoolCreateManyTypeInputEnvelope
  set?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  disconnect?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  delete?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  connect?: Prisma.SchoolWhereUniqueInput | Prisma.SchoolWhereUniqueInput[]
  update?:
    | Prisma.SchoolUpdateWithWhereUniqueWithoutTypeInput
    | Prisma.SchoolUpdateWithWhereUniqueWithoutTypeInput[]
  updateMany?:
    | Prisma.SchoolUpdateManyWithWhereWithoutTypeInput
    | Prisma.SchoolUpdateManyWithWhereWithoutTypeInput[]
  deleteMany?: Prisma.SchoolScalarWhereInput | Prisma.SchoolScalarWhereInput[]
}

export type SchoolCreateNestedOneWithoutSchoolAdminsInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutSchoolAdminsInput,
    Prisma.SchoolUncheckedCreateWithoutSchoolAdminsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutSchoolAdminsInput
  connect?: Prisma.SchoolWhereUniqueInput
}

export type SchoolUpdateOneRequiredWithoutSchoolAdminsNestedInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutSchoolAdminsInput,
    Prisma.SchoolUncheckedCreateWithoutSchoolAdminsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutSchoolAdminsInput
  upsert?: Prisma.SchoolUpsertWithoutSchoolAdminsInput
  connect?: Prisma.SchoolWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SchoolUpdateToOneWithWhereWithoutSchoolAdminsInput,
      Prisma.SchoolUpdateWithoutSchoolAdminsInput
    >,
    Prisma.SchoolUncheckedUpdateWithoutSchoolAdminsInput
  >
}

export type SchoolCreateNestedOneWithoutDepartmentsInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutDepartmentsInput,
    Prisma.SchoolUncheckedCreateWithoutDepartmentsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutDepartmentsInput
  connect?: Prisma.SchoolWhereUniqueInput
}

export type SchoolUpdateOneRequiredWithoutDepartmentsNestedInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutDepartmentsInput,
    Prisma.SchoolUncheckedCreateWithoutDepartmentsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutDepartmentsInput
  upsert?: Prisma.SchoolUpsertWithoutDepartmentsInput
  connect?: Prisma.SchoolWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SchoolUpdateToOneWithWhereWithoutDepartmentsInput,
      Prisma.SchoolUpdateWithoutDepartmentsInput
    >,
    Prisma.SchoolUncheckedUpdateWithoutDepartmentsInput
  >
}

export type SchoolCreateNestedOneWithoutSessionsInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutSessionsInput,
    Prisma.SchoolUncheckedCreateWithoutSessionsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutSessionsInput
  connect?: Prisma.SchoolWhereUniqueInput
}

export type SchoolUpdateOneRequiredWithoutSessionsNestedInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutSessionsInput,
    Prisma.SchoolUncheckedCreateWithoutSessionsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutSessionsInput
  upsert?: Prisma.SchoolUpsertWithoutSessionsInput
  connect?: Prisma.SchoolWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SchoolUpdateToOneWithWhereWithoutSessionsInput,
      Prisma.SchoolUpdateWithoutSessionsInput
    >,
    Prisma.SchoolUncheckedUpdateWithoutSessionsInput
  >
}

export type SchoolCreateNestedOneWithoutStudentsInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutStudentsInput,
    Prisma.SchoolUncheckedCreateWithoutStudentsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutStudentsInput
  connect?: Prisma.SchoolWhereUniqueInput
}

export type SchoolUpdateOneWithoutStudentsNestedInput = {
  create?: Prisma.XOR<
    Prisma.SchoolCreateWithoutStudentsInput,
    Prisma.SchoolUncheckedCreateWithoutStudentsInput
  >
  connectOrCreate?: Prisma.SchoolCreateOrConnectWithoutStudentsInput
  upsert?: Prisma.SchoolUpsertWithoutStudentsInput
  disconnect?: Prisma.SchoolWhereInput | boolean
  delete?: Prisma.SchoolWhereInput | boolean
  connect?: Prisma.SchoolWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SchoolUpdateToOneWithWhereWithoutStudentsInput,
      Prisma.SchoolUpdateWithoutStudentsInput
    >,
    Prisma.SchoolUncheckedUpdateWithoutStudentsInput
  >
}

export type SchoolCreateWithoutClassesInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  type: Prisma.SchoolTypeCreateNestedOneWithoutSchoolsInput
  students?: Prisma.StudentCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateWithoutClassesInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
  students?: Prisma.StudentUncheckedCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentUncheckedCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolCreateOrConnectWithoutClassesInput = {
  where: Prisma.SchoolWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutClassesInput,
    Prisma.SchoolUncheckedCreateWithoutClassesInput
  >
}

export type SchoolUpsertWithoutClassesInput = {
  update: Prisma.XOR<
    Prisma.SchoolUpdateWithoutClassesInput,
    Prisma.SchoolUncheckedUpdateWithoutClassesInput
  >
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutClassesInput,
    Prisma.SchoolUncheckedCreateWithoutClassesInput
  >
  where?: Prisma.SchoolWhereInput
}

export type SchoolUpdateToOneWithWhereWithoutClassesInput = {
  where?: Prisma.SchoolWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateWithoutClassesInput,
    Prisma.SchoolUncheckedUpdateWithoutClassesInput
  >
}

export type SchoolUpdateWithoutClassesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  type?: Prisma.SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput
  students?: Prisma.StudentUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateWithoutClassesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
  students?: Prisma.StudentUncheckedUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUncheckedUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolCreateWithoutSubjectsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  type: Prisma.SchoolTypeCreateNestedOneWithoutSchoolsInput
  students?: Prisma.StudentCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateWithoutSubjectsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
  students?: Prisma.StudentUncheckedCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentUncheckedCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolCreateOrConnectWithoutSubjectsInput = {
  where: Prisma.SchoolWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutSubjectsInput,
    Prisma.SchoolUncheckedCreateWithoutSubjectsInput
  >
}

export type SchoolUpsertWithoutSubjectsInput = {
  update: Prisma.XOR<
    Prisma.SchoolUpdateWithoutSubjectsInput,
    Prisma.SchoolUncheckedUpdateWithoutSubjectsInput
  >
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutSubjectsInput,
    Prisma.SchoolUncheckedCreateWithoutSubjectsInput
  >
  where?: Prisma.SchoolWhereInput
}

export type SchoolUpdateToOneWithWhereWithoutSubjectsInput = {
  where?: Prisma.SchoolWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateWithoutSubjectsInput,
    Prisma.SchoolUncheckedUpdateWithoutSubjectsInput
  >
}

export type SchoolUpdateWithoutSubjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  type?: Prisma.SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput
  students?: Prisma.StudentUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateWithoutSubjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
  students?: Prisma.StudentUncheckedUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUncheckedUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolCreateWithoutTypeInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  students?: Prisma.StudentCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateWithoutTypeInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  students?: Prisma.StudentUncheckedCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentUncheckedCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectUncheckedCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolCreateOrConnectWithoutTypeInput = {
  where: Prisma.SchoolWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutTypeInput,
    Prisma.SchoolUncheckedCreateWithoutTypeInput
  >
}

export type SchoolCreateManyTypeInputEnvelope = {
  data: Prisma.SchoolCreateManyTypeInput | Prisma.SchoolCreateManyTypeInput[]
  skipDuplicates?: boolean
}

export type SchoolUpsertWithWhereUniqueWithoutTypeInput = {
  where: Prisma.SchoolWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SchoolUpdateWithoutTypeInput,
    Prisma.SchoolUncheckedUpdateWithoutTypeInput
  >
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutTypeInput,
    Prisma.SchoolUncheckedCreateWithoutTypeInput
  >
}

export type SchoolUpdateWithWhereUniqueWithoutTypeInput = {
  where: Prisma.SchoolWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateWithoutTypeInput,
    Prisma.SchoolUncheckedUpdateWithoutTypeInput
  >
}

export type SchoolUpdateManyWithWhereWithoutTypeInput = {
  where: Prisma.SchoolScalarWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateManyMutationInput,
    Prisma.SchoolUncheckedUpdateManyWithoutTypeInput
  >
}

export type SchoolScalarWhereInput = {
  AND?: Prisma.SchoolScalarWhereInput | Prisma.SchoolScalarWhereInput[]
  OR?: Prisma.SchoolScalarWhereInput[]
  NOT?: Prisma.SchoolScalarWhereInput | Prisma.SchoolScalarWhereInput[]
  id?: Prisma.StringFilter<'School'> | string
  name?: Prisma.StringFilter<'School'> | string
  address?: Prisma.StringFilter<'School'> | string
  phone?: Prisma.StringFilter<'School'> | string
  email?: Prisma.StringFilter<'School'> | string
  website?: Prisma.StringFilter<'School'> | string
  motto?: Prisma.StringNullableFilter<'School'> | string | null
  description?: Prisma.StringNullableFilter<'School'> | string | null
  logo?: Prisma.StringNullableFilter<'School'> | string | null
  certification?: Prisma.StringNullableFilter<'School'> | string | null
  isActive?: Prisma.BoolFilter<'School'> | boolean
  verified?: Prisma.BoolFilter<'School'> | boolean
  createdAt?: Prisma.DateTimeFilter<'School'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'School'> | Date | string
  typeId?: Prisma.IntFilter<'School'> | number
}

export type SchoolCreateWithoutSchoolAdminsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  type: Prisma.SchoolTypeCreateNestedOneWithoutSchoolsInput
  students?: Prisma.StudentCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateWithoutSchoolAdminsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
  students?: Prisma.StudentUncheckedCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentUncheckedCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectUncheckedCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolCreateOrConnectWithoutSchoolAdminsInput = {
  where: Prisma.SchoolWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutSchoolAdminsInput,
    Prisma.SchoolUncheckedCreateWithoutSchoolAdminsInput
  >
}

export type SchoolUpsertWithoutSchoolAdminsInput = {
  update: Prisma.XOR<
    Prisma.SchoolUpdateWithoutSchoolAdminsInput,
    Prisma.SchoolUncheckedUpdateWithoutSchoolAdminsInput
  >
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutSchoolAdminsInput,
    Prisma.SchoolUncheckedCreateWithoutSchoolAdminsInput
  >
  where?: Prisma.SchoolWhereInput
}

export type SchoolUpdateToOneWithWhereWithoutSchoolAdminsInput = {
  where?: Prisma.SchoolWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateWithoutSchoolAdminsInput,
    Prisma.SchoolUncheckedUpdateWithoutSchoolAdminsInput
  >
}

export type SchoolUpdateWithoutSchoolAdminsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  type?: Prisma.SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput
  students?: Prisma.StudentUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateWithoutSchoolAdminsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
  students?: Prisma.StudentUncheckedUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUncheckedUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUncheckedUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolCreateWithoutDepartmentsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  type: Prisma.SchoolTypeCreateNestedOneWithoutSchoolsInput
  students?: Prisma.StudentCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateWithoutDepartmentsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
  students?: Prisma.StudentUncheckedCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectUncheckedCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolCreateOrConnectWithoutDepartmentsInput = {
  where: Prisma.SchoolWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutDepartmentsInput,
    Prisma.SchoolUncheckedCreateWithoutDepartmentsInput
  >
}

export type SchoolUpsertWithoutDepartmentsInput = {
  update: Prisma.XOR<
    Prisma.SchoolUpdateWithoutDepartmentsInput,
    Prisma.SchoolUncheckedUpdateWithoutDepartmentsInput
  >
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutDepartmentsInput,
    Prisma.SchoolUncheckedCreateWithoutDepartmentsInput
  >
  where?: Prisma.SchoolWhereInput
}

export type SchoolUpdateToOneWithWhereWithoutDepartmentsInput = {
  where?: Prisma.SchoolWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateWithoutDepartmentsInput,
    Prisma.SchoolUncheckedUpdateWithoutDepartmentsInput
  >
}

export type SchoolUpdateWithoutDepartmentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  type?: Prisma.SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput
  students?: Prisma.StudentUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateWithoutDepartmentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
  students?: Prisma.StudentUncheckedUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUncheckedUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolCreateWithoutSessionsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  type: Prisma.SchoolTypeCreateNestedOneWithoutSchoolsInput
  students?: Prisma.StudentCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateWithoutSessionsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
  students?: Prisma.StudentUncheckedCreateNestedManyWithoutSchoolInput
  departments?: Prisma.DepartmentUncheckedCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectUncheckedCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolCreateOrConnectWithoutSessionsInput = {
  where: Prisma.SchoolWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutSessionsInput,
    Prisma.SchoolUncheckedCreateWithoutSessionsInput
  >
}

export type SchoolUpsertWithoutSessionsInput = {
  update: Prisma.XOR<
    Prisma.SchoolUpdateWithoutSessionsInput,
    Prisma.SchoolUncheckedUpdateWithoutSessionsInput
  >
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutSessionsInput,
    Prisma.SchoolUncheckedCreateWithoutSessionsInput
  >
  where?: Prisma.SchoolWhereInput
}

export type SchoolUpdateToOneWithWhereWithoutSessionsInput = {
  where?: Prisma.SchoolWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateWithoutSessionsInput,
    Prisma.SchoolUncheckedUpdateWithoutSessionsInput
  >
}

export type SchoolUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  type?: Prisma.SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput
  students?: Prisma.StudentUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
  students?: Prisma.StudentUncheckedUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUncheckedUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUncheckedUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolCreateWithoutStudentsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  type: Prisma.SchoolTypeCreateNestedOneWithoutSchoolsInput
  departments?: Prisma.DepartmentCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassCreateNestedManyWithoutSchoolInput
}

export type SchoolUncheckedCreateWithoutStudentsInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  typeId: number
  departments?: Prisma.DepartmentUncheckedCreateNestedManyWithoutSchoolInput
  schoolAdmins?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutSchoolInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutSchoolInput
  subjects?: Prisma.SubjectUncheckedCreateNestedManyWithoutSchoolInput
  classes?: Prisma.ClassUncheckedCreateNestedManyWithoutSchoolInput
}

export type SchoolCreateOrConnectWithoutStudentsInput = {
  where: Prisma.SchoolWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutStudentsInput,
    Prisma.SchoolUncheckedCreateWithoutStudentsInput
  >
}

export type SchoolUpsertWithoutStudentsInput = {
  update: Prisma.XOR<
    Prisma.SchoolUpdateWithoutStudentsInput,
    Prisma.SchoolUncheckedUpdateWithoutStudentsInput
  >
  create: Prisma.XOR<
    Prisma.SchoolCreateWithoutStudentsInput,
    Prisma.SchoolUncheckedCreateWithoutStudentsInput
  >
  where?: Prisma.SchoolWhereInput
}

export type SchoolUpdateToOneWithWhereWithoutStudentsInput = {
  where?: Prisma.SchoolWhereInput
  data: Prisma.XOR<
    Prisma.SchoolUpdateWithoutStudentsInput,
    Prisma.SchoolUncheckedUpdateWithoutStudentsInput
  >
}

export type SchoolUpdateWithoutStudentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  type?: Prisma.SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput
  departments?: Prisma.DepartmentUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateWithoutStudentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  typeId?: Prisma.IntFieldUpdateOperationsInput | number
  departments?: Prisma.DepartmentUncheckedUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUncheckedUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolCreateManyTypeInput = {
  id?: string
  name: string
  address: string
  phone: string
  email: string
  website: string
  motto?: string | null
  description?: string | null
  logo?: string | null
  certification?: string | null
  isActive?: boolean
  verified?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SchoolUpdateWithoutTypeInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  students?: Prisma.StudentUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateWithoutTypeInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  students?: Prisma.StudentUncheckedUpdateManyWithoutSchoolNestedInput
  departments?: Prisma.DepartmentUncheckedUpdateManyWithoutSchoolNestedInput
  schoolAdmins?: Prisma.SchoolUserUncheckedUpdateManyWithoutSchoolNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutSchoolNestedInput
  subjects?: Prisma.SubjectUncheckedUpdateManyWithoutSchoolNestedInput
  classes?: Prisma.ClassUncheckedUpdateManyWithoutSchoolNestedInput
}

export type SchoolUncheckedUpdateManyWithoutTypeInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  motto?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  certification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  verified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

/**
 * Count Type SchoolCountOutputType
 */

export type SchoolCountOutputType = {
  students: number
  departments: number
  schoolAdmins: number
  sessions: number
  subjects: number
  classes: number
}

export type SchoolCountOutputTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  students?: boolean | SchoolCountOutputTypeCountStudentsArgs
  departments?: boolean | SchoolCountOutputTypeCountDepartmentsArgs
  schoolAdmins?: boolean | SchoolCountOutputTypeCountSchoolAdminsArgs
  sessions?: boolean | SchoolCountOutputTypeCountSessionsArgs
  subjects?: boolean | SchoolCountOutputTypeCountSubjectsArgs
  classes?: boolean | SchoolCountOutputTypeCountClassesArgs
}

/**
 * SchoolCountOutputType without action
 */
export type SchoolCountOutputTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolCountOutputType
   */
  select?: Prisma.SchoolCountOutputTypeSelect<ExtArgs> | null
}

/**
 * SchoolCountOutputType without action
 */
export type SchoolCountOutputTypeCountStudentsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.StudentWhereInput
}

/**
 * SchoolCountOutputType without action
 */
export type SchoolCountOutputTypeCountDepartmentsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.DepartmentWhereInput
}

/**
 * SchoolCountOutputType without action
 */
export type SchoolCountOutputTypeCountSchoolAdminsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SchoolUserWhereInput
}

/**
 * SchoolCountOutputType without action
 */
export type SchoolCountOutputTypeCountSessionsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SessionWhereInput
}

/**
 * SchoolCountOutputType without action
 */
export type SchoolCountOutputTypeCountSubjectsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SubjectWhereInput
}

/**
 * SchoolCountOutputType without action
 */
export type SchoolCountOutputTypeCountClassesArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.ClassWhereInput
}

export type SchoolSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    address?: boolean
    phone?: boolean
    email?: boolean
    website?: boolean
    motto?: boolean
    description?: boolean
    logo?: boolean
    certification?: boolean
    isActive?: boolean
    verified?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    typeId?: boolean
    type?: boolean | Prisma.SchoolTypeDefaultArgs<ExtArgs>
    students?: boolean | Prisma.School$studentsArgs<ExtArgs>
    departments?: boolean | Prisma.School$departmentsArgs<ExtArgs>
    schoolAdmins?: boolean | Prisma.School$schoolAdminsArgs<ExtArgs>
    sessions?: boolean | Prisma.School$sessionsArgs<ExtArgs>
    subjects?: boolean | Prisma.School$subjectsArgs<ExtArgs>
    classes?: boolean | Prisma.School$classesArgs<ExtArgs>
    _count?: boolean | Prisma.SchoolCountOutputTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['school']
>

export type SchoolSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    address?: boolean
    phone?: boolean
    email?: boolean
    website?: boolean
    motto?: boolean
    description?: boolean
    logo?: boolean
    certification?: boolean
    isActive?: boolean
    verified?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    typeId?: boolean
    type?: boolean | Prisma.SchoolTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['school']
>

export type SchoolSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    address?: boolean
    phone?: boolean
    email?: boolean
    website?: boolean
    motto?: boolean
    description?: boolean
    logo?: boolean
    certification?: boolean
    isActive?: boolean
    verified?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    typeId?: boolean
    type?: boolean | Prisma.SchoolTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['school']
>

export type SchoolSelectScalar = {
  id?: boolean
  name?: boolean
  address?: boolean
  phone?: boolean
  email?: boolean
  website?: boolean
  motto?: boolean
  description?: boolean
  logo?: boolean
  certification?: boolean
  isActive?: boolean
  verified?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  typeId?: boolean
}

export type SchoolOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  | 'id'
  | 'name'
  | 'address'
  | 'phone'
  | 'email'
  | 'website'
  | 'motto'
  | 'description'
  | 'logo'
  | 'certification'
  | 'isActive'
  | 'verified'
  | 'createdAt'
  | 'updatedAt'
  | 'typeId',
  ExtArgs['result']['school']
>
export type SchoolInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  type?: boolean | Prisma.SchoolTypeDefaultArgs<ExtArgs>
  students?: boolean | Prisma.School$studentsArgs<ExtArgs>
  departments?: boolean | Prisma.School$departmentsArgs<ExtArgs>
  schoolAdmins?: boolean | Prisma.School$schoolAdminsArgs<ExtArgs>
  sessions?: boolean | Prisma.School$sessionsArgs<ExtArgs>
  subjects?: boolean | Prisma.School$subjectsArgs<ExtArgs>
  classes?: boolean | Prisma.School$classesArgs<ExtArgs>
  _count?: boolean | Prisma.SchoolCountOutputTypeDefaultArgs<ExtArgs>
}
export type SchoolIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  type?: boolean | Prisma.SchoolTypeDefaultArgs<ExtArgs>
}
export type SchoolIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  type?: boolean | Prisma.SchoolTypeDefaultArgs<ExtArgs>
}

export type $SchoolPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'School'
  objects: {
    type: Prisma.$SchoolTypePayload<ExtArgs>
    students: Prisma.$StudentPayload<ExtArgs>[]
    departments: Prisma.$DepartmentPayload<ExtArgs>[]
    schoolAdmins: Prisma.$SchoolUserPayload<ExtArgs>[]
    sessions: Prisma.$SessionPayload<ExtArgs>[]
    subjects: Prisma.$SubjectPayload<ExtArgs>[]
    classes: Prisma.$ClassPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      name: string
      address: string
      phone: string
      email: string
      website: string
      motto: string | null
      description: string | null
      logo: string | null
      certification: string | null
      isActive: boolean
      verified: boolean
      createdAt: Date
      updatedAt: Date
      typeId: number
    },
    ExtArgs['result']['school']
  >
  composites: {}
}

export type SchoolGetPayload<S extends boolean | null | undefined | SchoolDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$SchoolPayload, S>

export type SchoolCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<SchoolFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: SchoolCountAggregateInputType | true
}

export interface SchoolDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['School']; meta: { name: 'School' } }
  /**
   * Find zero or one School that matches the filter.
   * @param {SchoolFindUniqueArgs} args - Arguments to find a School
   * @example
   * // Get one School
   * const school = await prisma.school.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SchoolFindUniqueArgs>(
    args: Prisma.SelectSubset<T, SchoolFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one School that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SchoolFindUniqueOrThrowArgs} args - Arguments to find a School
   * @example
   * // Get one School
   * const school = await prisma.school.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SchoolFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, SchoolFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first School that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolFindFirstArgs} args - Arguments to find a School
   * @example
   * // Get one School
   * const school = await prisma.school.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SchoolFindFirstArgs>(
    args?: Prisma.SelectSubset<T, SchoolFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first School that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolFindFirstOrThrowArgs} args - Arguments to find a School
   * @example
   * // Get one School
   * const school = await prisma.school.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SchoolFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, SchoolFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Schools that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Schools
   * const schools = await prisma.school.findMany()
   *
   * // Get first 10 Schools
   * const schools = await prisma.school.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const schoolWithIdOnly = await prisma.school.findMany({ select: { id: true } })
   *
   */
  findMany<T extends SchoolFindManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<Prisma.$SchoolPayload<ExtArgs>, T, 'findMany', GlobalOmitOptions>
  >

  /**
   * Create a School.
   * @param {SchoolCreateArgs} args - Arguments to create a School.
   * @example
   * // Create one School
   * const School = await prisma.school.create({
   *   data: {
   *     // ... data to create a School
   *   }
   * })
   *
   */
  create<T extends SchoolCreateArgs>(
    args: Prisma.SelectSubset<T, SchoolCreateArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<Prisma.$SchoolPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Schools.
   * @param {SchoolCreateManyArgs} args - Arguments to create many Schools.
   * @example
   * // Create many Schools
   * const school = await prisma.school.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends SchoolCreateManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Schools and returns the data saved in the database.
   * @param {SchoolCreateManyAndReturnArgs} args - Arguments to create many Schools.
   * @example
   * // Create many Schools
   * const school = await prisma.school.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Schools and only return the `id`
   * const schoolWithIdOnly = await prisma.school.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends SchoolCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, SchoolCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a School.
   * @param {SchoolDeleteArgs} args - Arguments to delete one School.
   * @example
   * // Delete one School
   * const School = await prisma.school.delete({
   *   where: {
   *     // ... filter to delete one School
   *   }
   * })
   *
   */
  delete<T extends SchoolDeleteArgs>(
    args: Prisma.SelectSubset<T, SchoolDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<Prisma.$SchoolPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one School.
   * @param {SchoolUpdateArgs} args - Arguments to update one School.
   * @example
   * // Update one School
   * const school = await prisma.school.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends SchoolUpdateArgs>(
    args: Prisma.SelectSubset<T, SchoolUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<Prisma.$SchoolPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Schools.
   * @param {SchoolDeleteManyArgs} args - Arguments to filter Schools to delete.
   * @example
   * // Delete a few Schools
   * const { count } = await prisma.school.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends SchoolDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Schools.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Schools
   * const school = await prisma.school.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends SchoolUpdateManyArgs>(
    args: Prisma.SelectSubset<T, SchoolUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Schools and returns the data updated in the database.
   * @param {SchoolUpdateManyAndReturnArgs} args - Arguments to update many Schools.
   * @example
   * // Update many Schools
   * const school = await prisma.school.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Schools and only return the `id`
   * const schoolWithIdOnly = await prisma.school.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends SchoolUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, SchoolUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one School.
   * @param {SchoolUpsertArgs} args - Arguments to update or create a School.
   * @example
   * // Update or create a School
   * const school = await prisma.school.upsert({
   *   create: {
   *     // ... data to create a School
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the School we want to update
   *   }
   * })
   */
  upsert<T extends SchoolUpsertArgs>(
    args: Prisma.SelectSubset<T, SchoolUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<Prisma.$SchoolPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Schools.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolCountArgs} args - Arguments to filter Schools to count.
   * @example
   * // Count the number of Schools
   * const count = await prisma.school.count({
   *   where: {
   *     // ... the filter for the Schools we want to count
   *   }
   * })
   **/
  count<T extends SchoolCountArgs>(
    args?: Prisma.Subset<T, SchoolCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SchoolCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a School.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends SchoolAggregateArgs>(
    args: Prisma.Subset<T, SchoolAggregateArgs>
  ): Prisma.PrismaPromise<GetSchoolAggregateType<T>>

  /**
   * Group by School.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends SchoolGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SchoolGroupByArgs['orderBy'] }
      : { orderBy?: SchoolGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, SchoolGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetSchoolGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the School model
   */
  readonly fields: SchoolFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for School.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SchoolClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  type<T extends Prisma.SchoolTypeDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.SchoolTypeDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    | runtime.Types.Result.GetResult<
        Prisma.$SchoolTypePayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  students<T extends Prisma.School$studentsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.School$studentsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$StudentPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  departments<T extends Prisma.School$departmentsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.School$departmentsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$DepartmentPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  schoolAdmins<T extends Prisma.School$schoolAdminsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.School$schoolAdminsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SchoolUserPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  sessions<T extends Prisma.School$sessionsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.School$sessionsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SessionPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  subjects<T extends Prisma.School$subjectsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.School$subjectsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SubjectPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  classes<T extends Prisma.School$classesArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.School$classesArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$ClassPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the School model
 */
export interface SchoolFieldRefs {
  readonly id: Prisma.FieldRef<'School', 'String'>
  readonly name: Prisma.FieldRef<'School', 'String'>
  readonly address: Prisma.FieldRef<'School', 'String'>
  readonly phone: Prisma.FieldRef<'School', 'String'>
  readonly email: Prisma.FieldRef<'School', 'String'>
  readonly website: Prisma.FieldRef<'School', 'String'>
  readonly motto: Prisma.FieldRef<'School', 'String'>
  readonly description: Prisma.FieldRef<'School', 'String'>
  readonly logo: Prisma.FieldRef<'School', 'String'>
  readonly certification: Prisma.FieldRef<'School', 'String'>
  readonly isActive: Prisma.FieldRef<'School', 'Boolean'>
  readonly verified: Prisma.FieldRef<'School', 'Boolean'>
  readonly createdAt: Prisma.FieldRef<'School', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'School', 'DateTime'>
  readonly typeId: Prisma.FieldRef<'School', 'Int'>
}

// Custom InputTypes
/**
 * School findUnique
 */
export type SchoolFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * Filter, which School to fetch.
   */
  where: Prisma.SchoolWhereUniqueInput
}

/**
 * School findUniqueOrThrow
 */
export type SchoolFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * Filter, which School to fetch.
   */
  where: Prisma.SchoolWhereUniqueInput
}

/**
 * School findFirst
 */
export type SchoolFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * Filter, which School to fetch.
   */
  where?: Prisma.SchoolWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Schools to fetch.
   */
  orderBy?: Prisma.SchoolOrderByWithRelationInput | Prisma.SchoolOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Schools.
   */
  cursor?: Prisma.SchoolWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Schools from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Schools.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Schools.
   */
  distinct?: Prisma.SchoolScalarFieldEnum | Prisma.SchoolScalarFieldEnum[]
}

/**
 * School findFirstOrThrow
 */
export type SchoolFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * Filter, which School to fetch.
   */
  where?: Prisma.SchoolWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Schools to fetch.
   */
  orderBy?: Prisma.SchoolOrderByWithRelationInput | Prisma.SchoolOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Schools.
   */
  cursor?: Prisma.SchoolWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Schools from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Schools.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Schools.
   */
  distinct?: Prisma.SchoolScalarFieldEnum | Prisma.SchoolScalarFieldEnum[]
}

/**
 * School findMany
 */
export type SchoolFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * Filter, which Schools to fetch.
   */
  where?: Prisma.SchoolWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Schools to fetch.
   */
  orderBy?: Prisma.SchoolOrderByWithRelationInput | Prisma.SchoolOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Schools.
   */
  cursor?: Prisma.SchoolWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Schools from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Schools.
   */
  skip?: number
  distinct?: Prisma.SchoolScalarFieldEnum | Prisma.SchoolScalarFieldEnum[]
}

/**
 * School create
 */
export type SchoolCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * The data needed to create a School.
   */
  data: Prisma.XOR<Prisma.SchoolCreateInput, Prisma.SchoolUncheckedCreateInput>
}

/**
 * School createMany
 */
export type SchoolCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Schools.
   */
  data: Prisma.SchoolCreateManyInput | Prisma.SchoolCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * School createManyAndReturn
 */
export type SchoolCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * The data used to create many Schools.
   */
  data: Prisma.SchoolCreateManyInput | Prisma.SchoolCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * School update
 */
export type SchoolUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * The data needed to update a School.
   */
  data: Prisma.XOR<Prisma.SchoolUpdateInput, Prisma.SchoolUncheckedUpdateInput>
  /**
   * Choose, which School to update.
   */
  where: Prisma.SchoolWhereUniqueInput
}

/**
 * School updateMany
 */
export type SchoolUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Schools.
   */
  data: Prisma.XOR<Prisma.SchoolUpdateManyMutationInput, Prisma.SchoolUncheckedUpdateManyInput>
  /**
   * Filter which Schools to update
   */
  where?: Prisma.SchoolWhereInput
  /**
   * Limit how many Schools to update.
   */
  limit?: number
}

/**
 * School updateManyAndReturn
 */
export type SchoolUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * The data used to update Schools.
   */
  data: Prisma.XOR<Prisma.SchoolUpdateManyMutationInput, Prisma.SchoolUncheckedUpdateManyInput>
  /**
   * Filter which Schools to update
   */
  where?: Prisma.SchoolWhereInput
  /**
   * Limit how many Schools to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * School upsert
 */
export type SchoolUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * The filter to search for the School to update in case it exists.
   */
  where: Prisma.SchoolWhereUniqueInput
  /**
   * In case the School found by the `where` argument doesn't exist, create a new School with this data.
   */
  create: Prisma.XOR<Prisma.SchoolCreateInput, Prisma.SchoolUncheckedCreateInput>
  /**
   * In case the School was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SchoolUpdateInput, Prisma.SchoolUncheckedUpdateInput>
}

/**
 * School delete
 */
export type SchoolDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  /**
   * Filter which School to delete.
   */
  where: Prisma.SchoolWhereUniqueInput
}

/**
 * School deleteMany
 */
export type SchoolDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Schools to delete
   */
  where?: Prisma.SchoolWhereInput
  /**
   * Limit how many Schools to delete.
   */
  limit?: number
}

/**
 * School.students
 */
export type School$studentsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  where?: Prisma.StudentWhereInput
  orderBy?: Prisma.StudentOrderByWithRelationInput | Prisma.StudentOrderByWithRelationInput[]
  cursor?: Prisma.StudentWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.StudentScalarFieldEnum | Prisma.StudentScalarFieldEnum[]
}

/**
 * School.departments
 */
export type School$departmentsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Department
   */
  select?: Prisma.DepartmentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Department
   */
  omit?: Prisma.DepartmentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentInclude<ExtArgs> | null
  where?: Prisma.DepartmentWhereInput
  orderBy?: Prisma.DepartmentOrderByWithRelationInput | Prisma.DepartmentOrderByWithRelationInput[]
  cursor?: Prisma.DepartmentWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DepartmentScalarFieldEnum | Prisma.DepartmentScalarFieldEnum[]
}

/**
 * School.schoolAdmins
 */
export type School$schoolAdminsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  where?: Prisma.SchoolUserWhereInput
  orderBy?: Prisma.SchoolUserOrderByWithRelationInput | Prisma.SchoolUserOrderByWithRelationInput[]
  cursor?: Prisma.SchoolUserWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SchoolUserScalarFieldEnum | Prisma.SchoolUserScalarFieldEnum[]
}

/**
 * School.sessions
 */
export type School$sessionsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  where?: Prisma.SessionWhereInput
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  cursor?: Prisma.SessionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * School.subjects
 */
export type School$subjectsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  where?: Prisma.SubjectWhereInput
  orderBy?: Prisma.SubjectOrderByWithRelationInput | Prisma.SubjectOrderByWithRelationInput[]
  cursor?: Prisma.SubjectWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SubjectScalarFieldEnum | Prisma.SubjectScalarFieldEnum[]
}

/**
 * School.classes
 */
export type School$classesArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Class
   */
  select?: Prisma.ClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Class
   */
  omit?: Prisma.ClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClassInclude<ExtArgs> | null
  where?: Prisma.ClassWhereInput
  orderBy?: Prisma.ClassOrderByWithRelationInput | Prisma.ClassOrderByWithRelationInput[]
  cursor?: Prisma.ClassWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ClassScalarFieldEnum | Prisma.ClassScalarFieldEnum[]
}

/**
 * School without action
 */
export type SchoolDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
}
