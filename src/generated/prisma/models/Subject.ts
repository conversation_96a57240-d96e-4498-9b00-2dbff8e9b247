/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Subject` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Subject
 *
 */
export type SubjectModel = runtime.Types.Result.DefaultSelection<Prisma.$SubjectPayload>

export type AggregateSubject = {
  _count: SubjectCountAggregateOutputType | null
  _min: SubjectMinAggregateOutputType | null
  _max: SubjectMaxAggregateOutputType | null
}

export type SubjectMinAggregateOutputType = {
  id: string | null
  name: string | null
  code: string | null
  description: string | null
  createdAt: Date | null
  updatedAt: Date | null
  schoolId: string | null
}

export type SubjectMaxAggregateOutputType = {
  id: string | null
  name: string | null
  code: string | null
  description: string | null
  createdAt: Date | null
  updatedAt: Date | null
  schoolId: string | null
}

export type SubjectCountAggregateOutputType = {
  id: number
  name: number
  code: number
  description: number
  createdAt: number
  updatedAt: number
  schoolId: number
  _all: number
}

export type SubjectMinAggregateInputType = {
  id?: true
  name?: true
  code?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
}

export type SubjectMaxAggregateInputType = {
  id?: true
  name?: true
  code?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
}

export type SubjectCountAggregateInputType = {
  id?: true
  name?: true
  code?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  schoolId?: true
  _all?: true
}

export type SubjectAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Subject to aggregate.
   */
  where?: Prisma.SubjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Subjects to fetch.
   */
  orderBy?: Prisma.SubjectOrderByWithRelationInput | Prisma.SubjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.SubjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Subjects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Subjects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Subjects
   **/
  _count?: true | SubjectCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: SubjectMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: SubjectMaxAggregateInputType
}

export type GetSubjectAggregateType<T extends SubjectAggregateArgs> = {
  [P in keyof T & keyof AggregateSubject]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSubject[P]>
    : Prisma.GetScalarType<T[P], AggregateSubject[P]>
}

export type SubjectGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SubjectWhereInput
  orderBy?: Prisma.SubjectOrderByWithAggregationInput | Prisma.SubjectOrderByWithAggregationInput[]
  by: Prisma.SubjectScalarFieldEnum[] | Prisma.SubjectScalarFieldEnum
  having?: Prisma.SubjectScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SubjectCountAggregateInputType | true
  _min?: SubjectMinAggregateInputType
  _max?: SubjectMaxAggregateInputType
}

export type SubjectGroupByOutputType = {
  id: string
  name: string
  code: string
  description: string
  createdAt: Date
  updatedAt: Date
  schoolId: string
  _count: SubjectCountAggregateOutputType | null
  _min: SubjectMinAggregateOutputType | null
  _max: SubjectMaxAggregateOutputType | null
}

type GetSubjectGroupByPayload<T extends SubjectGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SubjectGroupByOutputType, T['by']> & {
      [P in keyof T & keyof SubjectGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], SubjectGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], SubjectGroupByOutputType[P]>
    }
  >
>

export type SubjectWhereInput = {
  AND?: Prisma.SubjectWhereInput | Prisma.SubjectWhereInput[]
  OR?: Prisma.SubjectWhereInput[]
  NOT?: Prisma.SubjectWhereInput | Prisma.SubjectWhereInput[]
  id?: Prisma.StringFilter<'Subject'> | string
  name?: Prisma.StringFilter<'Subject'> | string
  code?: Prisma.StringFilter<'Subject'> | string
  description?: Prisma.StringFilter<'Subject'> | string
  createdAt?: Prisma.DateTimeFilter<'Subject'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Subject'> | Date | string
  schoolId?: Prisma.StringFilter<'Subject'> | string
  school?: Prisma.XOR<Prisma.SchoolScalarRelationFilter, Prisma.SchoolWhereInput>
  classes?: Prisma.SubjectClassListRelationFilter
  teachers?: Prisma.SubjectTeacherListRelationFilter
}

export type SubjectOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  code?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  school?: Prisma.SchoolOrderByWithRelationInput
  classes?: Prisma.SubjectClassOrderByRelationAggregateInput
  teachers?: Prisma.SubjectTeacherOrderByRelationAggregateInput
}

export type SubjectWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    name_schoolId?: Prisma.SubjectNameSchoolIdCompoundUniqueInput
    code_schoolId?: Prisma.SubjectCodeSchoolIdCompoundUniqueInput
    AND?: Prisma.SubjectWhereInput | Prisma.SubjectWhereInput[]
    OR?: Prisma.SubjectWhereInput[]
    NOT?: Prisma.SubjectWhereInput | Prisma.SubjectWhereInput[]
    name?: Prisma.StringFilter<'Subject'> | string
    code?: Prisma.StringFilter<'Subject'> | string
    description?: Prisma.StringFilter<'Subject'> | string
    createdAt?: Prisma.DateTimeFilter<'Subject'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'Subject'> | Date | string
    schoolId?: Prisma.StringFilter<'Subject'> | string
    school?: Prisma.XOR<Prisma.SchoolScalarRelationFilter, Prisma.SchoolWhereInput>
    classes?: Prisma.SubjectClassListRelationFilter
    teachers?: Prisma.SubjectTeacherListRelationFilter
  },
  'id' | 'name_schoolId' | 'code_schoolId'
>

export type SubjectOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  code?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
  _count?: Prisma.SubjectCountOrderByAggregateInput
  _max?: Prisma.SubjectMaxOrderByAggregateInput
  _min?: Prisma.SubjectMinOrderByAggregateInput
}

export type SubjectScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.SubjectScalarWhereWithAggregatesInput
    | Prisma.SubjectScalarWhereWithAggregatesInput[]
  OR?: Prisma.SubjectScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.SubjectScalarWhereWithAggregatesInput
    | Prisma.SubjectScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Subject'> | string
  name?: Prisma.StringWithAggregatesFilter<'Subject'> | string
  code?: Prisma.StringWithAggregatesFilter<'Subject'> | string
  description?: Prisma.StringWithAggregatesFilter<'Subject'> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'Subject'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'Subject'> | Date | string
  schoolId?: Prisma.StringWithAggregatesFilter<'Subject'> | string
}

export type SubjectCreateInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  school: Prisma.SchoolCreateNestedOneWithoutSubjectsInput
  classes?: Prisma.SubjectClassCreateNestedManyWithoutSubjectInput
  teachers?: Prisma.SubjectTeacherCreateNestedManyWithoutSubjectInput
}

export type SubjectUncheckedCreateInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
  classes?: Prisma.SubjectClassUncheckedCreateNestedManyWithoutSubjectInput
  teachers?: Prisma.SubjectTeacherUncheckedCreateNestedManyWithoutSubjectInput
}

export type SubjectUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneRequiredWithoutSubjectsNestedInput
  classes?: Prisma.SubjectClassUpdateManyWithoutSubjectNestedInput
  teachers?: Prisma.SubjectTeacherUpdateManyWithoutSubjectNestedInput
}

export type SubjectUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
  classes?: Prisma.SubjectClassUncheckedUpdateManyWithoutSubjectNestedInput
  teachers?: Prisma.SubjectTeacherUncheckedUpdateManyWithoutSubjectNestedInput
}

export type SubjectCreateManyInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
}

export type SubjectUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SubjectUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectNameSchoolIdCompoundUniqueInput = {
  name: string
  schoolId: string
}

export type SubjectCodeSchoolIdCompoundUniqueInput = {
  code: string
  schoolId: string
}

export type SubjectCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  code?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type SubjectMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  code?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type SubjectMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  code?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type SubjectScalarRelationFilter = {
  is?: Prisma.SubjectWhereInput
  isNot?: Prisma.SubjectWhereInput
}

export type SubjectListRelationFilter = {
  every?: Prisma.SubjectWhereInput
  some?: Prisma.SubjectWhereInput
  none?: Prisma.SubjectWhereInput
}

export type SubjectOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SubjectCreateNestedOneWithoutClassesInput = {
  create?: Prisma.XOR<
    Prisma.SubjectCreateWithoutClassesInput,
    Prisma.SubjectUncheckedCreateWithoutClassesInput
  >
  connectOrCreate?: Prisma.SubjectCreateOrConnectWithoutClassesInput
  connect?: Prisma.SubjectWhereUniqueInput
}

export type SubjectUpdateOneRequiredWithoutClassesNestedInput = {
  create?: Prisma.XOR<
    Prisma.SubjectCreateWithoutClassesInput,
    Prisma.SubjectUncheckedCreateWithoutClassesInput
  >
  connectOrCreate?: Prisma.SubjectCreateOrConnectWithoutClassesInput
  upsert?: Prisma.SubjectUpsertWithoutClassesInput
  connect?: Prisma.SubjectWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SubjectUpdateToOneWithWhereWithoutClassesInput,
      Prisma.SubjectUpdateWithoutClassesInput
    >,
    Prisma.SubjectUncheckedUpdateWithoutClassesInput
  >
}

export type SubjectCreateNestedOneWithoutTeachersInput = {
  create?: Prisma.XOR<
    Prisma.SubjectCreateWithoutTeachersInput,
    Prisma.SubjectUncheckedCreateWithoutTeachersInput
  >
  connectOrCreate?: Prisma.SubjectCreateOrConnectWithoutTeachersInput
  connect?: Prisma.SubjectWhereUniqueInput
}

export type SubjectUpdateOneRequiredWithoutTeachersNestedInput = {
  create?: Prisma.XOR<
    Prisma.SubjectCreateWithoutTeachersInput,
    Prisma.SubjectUncheckedCreateWithoutTeachersInput
  >
  connectOrCreate?: Prisma.SubjectCreateOrConnectWithoutTeachersInput
  upsert?: Prisma.SubjectUpsertWithoutTeachersInput
  connect?: Prisma.SubjectWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SubjectUpdateToOneWithWhereWithoutTeachersInput,
      Prisma.SubjectUpdateWithoutTeachersInput
    >,
    Prisma.SubjectUncheckedUpdateWithoutTeachersInput
  >
}

export type SubjectCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectCreateWithoutSchoolInput,
        Prisma.SubjectUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SubjectCreateWithoutSchoolInput[]
    | Prisma.SubjectUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.SubjectCreateManySchoolInputEnvelope
  connect?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
}

export type SubjectUncheckedCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectCreateWithoutSchoolInput,
        Prisma.SubjectUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SubjectCreateWithoutSchoolInput[]
    | Prisma.SubjectUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.SubjectCreateManySchoolInputEnvelope
  connect?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
}

export type SubjectUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectCreateWithoutSchoolInput,
        Prisma.SubjectUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SubjectCreateWithoutSchoolInput[]
    | Prisma.SubjectUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.SubjectUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.SubjectUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.SubjectCreateManySchoolInputEnvelope
  set?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  disconnect?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  delete?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  connect?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  update?:
    | Prisma.SubjectUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.SubjectUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.SubjectUpdateManyWithWhereWithoutSchoolInput
    | Prisma.SubjectUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.SubjectScalarWhereInput | Prisma.SubjectScalarWhereInput[]
}

export type SubjectUncheckedUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectCreateWithoutSchoolInput,
        Prisma.SubjectUncheckedCreateWithoutSchoolInput
      >
    | Prisma.SubjectCreateWithoutSchoolInput[]
    | Prisma.SubjectUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput
    | Prisma.SubjectCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.SubjectUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.SubjectUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.SubjectCreateManySchoolInputEnvelope
  set?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  disconnect?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  delete?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  connect?: Prisma.SubjectWhereUniqueInput | Prisma.SubjectWhereUniqueInput[]
  update?:
    | Prisma.SubjectUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.SubjectUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.SubjectUpdateManyWithWhereWithoutSchoolInput
    | Prisma.SubjectUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.SubjectScalarWhereInput | Prisma.SubjectScalarWhereInput[]
}

export type SubjectCreateWithoutClassesInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  school: Prisma.SchoolCreateNestedOneWithoutSubjectsInput
  teachers?: Prisma.SubjectTeacherCreateNestedManyWithoutSubjectInput
}

export type SubjectUncheckedCreateWithoutClassesInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
  teachers?: Prisma.SubjectTeacherUncheckedCreateNestedManyWithoutSubjectInput
}

export type SubjectCreateOrConnectWithoutClassesInput = {
  where: Prisma.SubjectWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SubjectCreateWithoutClassesInput,
    Prisma.SubjectUncheckedCreateWithoutClassesInput
  >
}

export type SubjectUpsertWithoutClassesInput = {
  update: Prisma.XOR<
    Prisma.SubjectUpdateWithoutClassesInput,
    Prisma.SubjectUncheckedUpdateWithoutClassesInput
  >
  create: Prisma.XOR<
    Prisma.SubjectCreateWithoutClassesInput,
    Prisma.SubjectUncheckedCreateWithoutClassesInput
  >
  where?: Prisma.SubjectWhereInput
}

export type SubjectUpdateToOneWithWhereWithoutClassesInput = {
  where?: Prisma.SubjectWhereInput
  data: Prisma.XOR<
    Prisma.SubjectUpdateWithoutClassesInput,
    Prisma.SubjectUncheckedUpdateWithoutClassesInput
  >
}

export type SubjectUpdateWithoutClassesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneRequiredWithoutSubjectsNestedInput
  teachers?: Prisma.SubjectTeacherUpdateManyWithoutSubjectNestedInput
}

export type SubjectUncheckedUpdateWithoutClassesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
  teachers?: Prisma.SubjectTeacherUncheckedUpdateManyWithoutSubjectNestedInput
}

export type SubjectCreateWithoutTeachersInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  school: Prisma.SchoolCreateNestedOneWithoutSubjectsInput
  classes?: Prisma.SubjectClassCreateNestedManyWithoutSubjectInput
}

export type SubjectUncheckedCreateWithoutTeachersInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  schoolId: string
  classes?: Prisma.SubjectClassUncheckedCreateNestedManyWithoutSubjectInput
}

export type SubjectCreateOrConnectWithoutTeachersInput = {
  where: Prisma.SubjectWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SubjectCreateWithoutTeachersInput,
    Prisma.SubjectUncheckedCreateWithoutTeachersInput
  >
}

export type SubjectUpsertWithoutTeachersInput = {
  update: Prisma.XOR<
    Prisma.SubjectUpdateWithoutTeachersInput,
    Prisma.SubjectUncheckedUpdateWithoutTeachersInput
  >
  create: Prisma.XOR<
    Prisma.SubjectCreateWithoutTeachersInput,
    Prisma.SubjectUncheckedCreateWithoutTeachersInput
  >
  where?: Prisma.SubjectWhereInput
}

export type SubjectUpdateToOneWithWhereWithoutTeachersInput = {
  where?: Prisma.SubjectWhereInput
  data: Prisma.XOR<
    Prisma.SubjectUpdateWithoutTeachersInput,
    Prisma.SubjectUncheckedUpdateWithoutTeachersInput
  >
}

export type SubjectUpdateWithoutTeachersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneRequiredWithoutSubjectsNestedInput
  classes?: Prisma.SubjectClassUpdateManyWithoutSubjectNestedInput
}

export type SubjectUncheckedUpdateWithoutTeachersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.StringFieldUpdateOperationsInput | string
  classes?: Prisma.SubjectClassUncheckedUpdateManyWithoutSubjectNestedInput
}

export type SubjectCreateWithoutSchoolInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  classes?: Prisma.SubjectClassCreateNestedManyWithoutSubjectInput
  teachers?: Prisma.SubjectTeacherCreateNestedManyWithoutSubjectInput
}

export type SubjectUncheckedCreateWithoutSchoolInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  classes?: Prisma.SubjectClassUncheckedCreateNestedManyWithoutSubjectInput
  teachers?: Prisma.SubjectTeacherUncheckedCreateNestedManyWithoutSubjectInput
}

export type SubjectCreateOrConnectWithoutSchoolInput = {
  where: Prisma.SubjectWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SubjectCreateWithoutSchoolInput,
    Prisma.SubjectUncheckedCreateWithoutSchoolInput
  >
}

export type SubjectCreateManySchoolInputEnvelope = {
  data: Prisma.SubjectCreateManySchoolInput | Prisma.SubjectCreateManySchoolInput[]
  skipDuplicates?: boolean
}

export type SubjectUpsertWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.SubjectWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SubjectUpdateWithoutSchoolInput,
    Prisma.SubjectUncheckedUpdateWithoutSchoolInput
  >
  create: Prisma.XOR<
    Prisma.SubjectCreateWithoutSchoolInput,
    Prisma.SubjectUncheckedCreateWithoutSchoolInput
  >
}

export type SubjectUpdateWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.SubjectWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SubjectUpdateWithoutSchoolInput,
    Prisma.SubjectUncheckedUpdateWithoutSchoolInput
  >
}

export type SubjectUpdateManyWithWhereWithoutSchoolInput = {
  where: Prisma.SubjectScalarWhereInput
  data: Prisma.XOR<
    Prisma.SubjectUpdateManyMutationInput,
    Prisma.SubjectUncheckedUpdateManyWithoutSchoolInput
  >
}

export type SubjectScalarWhereInput = {
  AND?: Prisma.SubjectScalarWhereInput | Prisma.SubjectScalarWhereInput[]
  OR?: Prisma.SubjectScalarWhereInput[]
  NOT?: Prisma.SubjectScalarWhereInput | Prisma.SubjectScalarWhereInput[]
  id?: Prisma.StringFilter<'Subject'> | string
  name?: Prisma.StringFilter<'Subject'> | string
  code?: Prisma.StringFilter<'Subject'> | string
  description?: Prisma.StringFilter<'Subject'> | string
  createdAt?: Prisma.DateTimeFilter<'Subject'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Subject'> | Date | string
  schoolId?: Prisma.StringFilter<'Subject'> | string
}

export type SubjectCreateManySchoolInput = {
  id?: string
  name: string
  code: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SubjectUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  classes?: Prisma.SubjectClassUpdateManyWithoutSubjectNestedInput
  teachers?: Prisma.SubjectTeacherUpdateManyWithoutSubjectNestedInput
}

export type SubjectUncheckedUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  classes?: Prisma.SubjectClassUncheckedUpdateManyWithoutSubjectNestedInput
  teachers?: Prisma.SubjectTeacherUncheckedUpdateManyWithoutSubjectNestedInput
}

export type SubjectUncheckedUpdateManyWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  code?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

/**
 * Count Type SubjectCountOutputType
 */

export type SubjectCountOutputType = {
  classes: number
  teachers: number
}

export type SubjectCountOutputTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  classes?: boolean | SubjectCountOutputTypeCountClassesArgs
  teachers?: boolean | SubjectCountOutputTypeCountTeachersArgs
}

/**
 * SubjectCountOutputType without action
 */
export type SubjectCountOutputTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectCountOutputType
   */
  select?: Prisma.SubjectCountOutputTypeSelect<ExtArgs> | null
}

/**
 * SubjectCountOutputType without action
 */
export type SubjectCountOutputTypeCountClassesArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SubjectClassWhereInput
}

/**
 * SubjectCountOutputType without action
 */
export type SubjectCountOutputTypeCountTeachersArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SubjectTeacherWhereInput
}

export type SubjectSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    code?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
    classes?: boolean | Prisma.Subject$classesArgs<ExtArgs>
    teachers?: boolean | Prisma.Subject$teachersArgs<ExtArgs>
    _count?: boolean | Prisma.SubjectCountOutputTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subject']
>

export type SubjectSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    code?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subject']
>

export type SubjectSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    code?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subject']
>

export type SubjectSelectScalar = {
  id?: boolean
  name?: boolean
  code?: boolean
  description?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  schoolId?: boolean
}

export type SubjectOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'name' | 'code' | 'description' | 'createdAt' | 'updatedAt' | 'schoolId',
  ExtArgs['result']['subject']
>
export type SubjectInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
  classes?: boolean | Prisma.Subject$classesArgs<ExtArgs>
  teachers?: boolean | Prisma.Subject$teachersArgs<ExtArgs>
  _count?: boolean | Prisma.SubjectCountOutputTypeDefaultArgs<ExtArgs>
}
export type SubjectIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
}
export type SubjectIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.SchoolDefaultArgs<ExtArgs>
}

export type $SubjectPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Subject'
  objects: {
    school: Prisma.$SchoolPayload<ExtArgs>
    classes: Prisma.$SubjectClassPayload<ExtArgs>[]
    teachers: Prisma.$SubjectTeacherPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      name: string
      code: string
      description: string
      createdAt: Date
      updatedAt: Date
      schoolId: string
    },
    ExtArgs['result']['subject']
  >
  composites: {}
}

export type SubjectGetPayload<S extends boolean | null | undefined | SubjectDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$SubjectPayload, S>

export type SubjectCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<SubjectFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: SubjectCountAggregateInputType | true
}

export interface SubjectDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Subject']; meta: { name: 'Subject' } }
  /**
   * Find zero or one Subject that matches the filter.
   * @param {SubjectFindUniqueArgs} args - Arguments to find a Subject
   * @example
   * // Get one Subject
   * const subject = await prisma.subject.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SubjectFindUniqueArgs>(
    args: Prisma.SelectSubset<T, SubjectFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Subject that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SubjectFindUniqueOrThrowArgs} args - Arguments to find a Subject
   * @example
   * // Get one Subject
   * const subject = await prisma.subject.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SubjectFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, SubjectFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Subject that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectFindFirstArgs} args - Arguments to find a Subject
   * @example
   * // Get one Subject
   * const subject = await prisma.subject.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SubjectFindFirstArgs>(
    args?: Prisma.SelectSubset<T, SubjectFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Subject that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectFindFirstOrThrowArgs} args - Arguments to find a Subject
   * @example
   * // Get one Subject
   * const subject = await prisma.subject.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SubjectFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, SubjectFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Subjects that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Subjects
   * const subjects = await prisma.subject.findMany()
   *
   * // Get first 10 Subjects
   * const subjects = await prisma.subject.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const subjectWithIdOnly = await prisma.subject.findMany({ select: { id: true } })
   *
   */
  findMany<T extends SubjectFindManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a Subject.
   * @param {SubjectCreateArgs} args - Arguments to create a Subject.
   * @example
   * // Create one Subject
   * const Subject = await prisma.subject.create({
   *   data: {
   *     // ... data to create a Subject
   *   }
   * })
   *
   */
  create<T extends SubjectCreateArgs>(
    args: Prisma.SelectSubset<T, SubjectCreateArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<Prisma.$SubjectPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Subjects.
   * @param {SubjectCreateManyArgs} args - Arguments to create many Subjects.
   * @example
   * // Create many Subjects
   * const subject = await prisma.subject.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends SubjectCreateManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Subjects and returns the data saved in the database.
   * @param {SubjectCreateManyAndReturnArgs} args - Arguments to create many Subjects.
   * @example
   * // Create many Subjects
   * const subject = await prisma.subject.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Subjects and only return the `id`
   * const subjectWithIdOnly = await prisma.subject.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends SubjectCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, SubjectCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Subject.
   * @param {SubjectDeleteArgs} args - Arguments to delete one Subject.
   * @example
   * // Delete one Subject
   * const Subject = await prisma.subject.delete({
   *   where: {
   *     // ... filter to delete one Subject
   *   }
   * })
   *
   */
  delete<T extends SubjectDeleteArgs>(
    args: Prisma.SelectSubset<T, SubjectDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<Prisma.$SubjectPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Subject.
   * @param {SubjectUpdateArgs} args - Arguments to update one Subject.
   * @example
   * // Update one Subject
   * const subject = await prisma.subject.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends SubjectUpdateArgs>(
    args: Prisma.SelectSubset<T, SubjectUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<Prisma.$SubjectPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Subjects.
   * @param {SubjectDeleteManyArgs} args - Arguments to filter Subjects to delete.
   * @example
   * // Delete a few Subjects
   * const { count } = await prisma.subject.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends SubjectDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Subjects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Subjects
   * const subject = await prisma.subject.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends SubjectUpdateManyArgs>(
    args: Prisma.SelectSubset<T, SubjectUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Subjects and returns the data updated in the database.
   * @param {SubjectUpdateManyAndReturnArgs} args - Arguments to update many Subjects.
   * @example
   * // Update many Subjects
   * const subject = await prisma.subject.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Subjects and only return the `id`
   * const subjectWithIdOnly = await prisma.subject.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends SubjectUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, SubjectUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Subject.
   * @param {SubjectUpsertArgs} args - Arguments to update or create a Subject.
   * @example
   * // Update or create a Subject
   * const subject = await prisma.subject.upsert({
   *   create: {
   *     // ... data to create a Subject
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Subject we want to update
   *   }
   * })
   */
  upsert<T extends SubjectUpsertArgs>(
    args: Prisma.SelectSubset<T, SubjectUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    runtime.Types.Result.GetResult<Prisma.$SubjectPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Subjects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectCountArgs} args - Arguments to filter Subjects to count.
   * @example
   * // Count the number of Subjects
   * const count = await prisma.subject.count({
   *   where: {
   *     // ... the filter for the Subjects we want to count
   *   }
   * })
   **/
  count<T extends SubjectCountArgs>(
    args?: Prisma.Subset<T, SubjectCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SubjectCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Subject.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends SubjectAggregateArgs>(
    args: Prisma.Subset<T, SubjectAggregateArgs>
  ): Prisma.PrismaPromise<GetSubjectAggregateType<T>>

  /**
   * Group by Subject.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends SubjectGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SubjectGroupByArgs['orderBy'] }
      : { orderBy?: SubjectGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, SubjectGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetSubjectGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Subject model
   */
  readonly fields: SubjectFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Subject.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SubjectClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  school<T extends Prisma.SchoolDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.SchoolDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    | runtime.Types.Result.GetResult<
        Prisma.$SchoolPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  classes<T extends Prisma.Subject$classesArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.Subject$classesArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SubjectClassPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  teachers<T extends Prisma.Subject$teachersArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.Subject$teachersArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SubjectTeacherPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Subject model
 */
export interface SubjectFieldRefs {
  readonly id: Prisma.FieldRef<'Subject', 'String'>
  readonly name: Prisma.FieldRef<'Subject', 'String'>
  readonly code: Prisma.FieldRef<'Subject', 'String'>
  readonly description: Prisma.FieldRef<'Subject', 'String'>
  readonly createdAt: Prisma.FieldRef<'Subject', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'Subject', 'DateTime'>
  readonly schoolId: Prisma.FieldRef<'Subject', 'String'>
}

// Custom InputTypes
/**
 * Subject findUnique
 */
export type SubjectFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * Filter, which Subject to fetch.
   */
  where: Prisma.SubjectWhereUniqueInput
}

/**
 * Subject findUniqueOrThrow
 */
export type SubjectFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * Filter, which Subject to fetch.
   */
  where: Prisma.SubjectWhereUniqueInput
}

/**
 * Subject findFirst
 */
export type SubjectFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * Filter, which Subject to fetch.
   */
  where?: Prisma.SubjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Subjects to fetch.
   */
  orderBy?: Prisma.SubjectOrderByWithRelationInput | Prisma.SubjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Subjects.
   */
  cursor?: Prisma.SubjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Subjects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Subjects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Subjects.
   */
  distinct?: Prisma.SubjectScalarFieldEnum | Prisma.SubjectScalarFieldEnum[]
}

/**
 * Subject findFirstOrThrow
 */
export type SubjectFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * Filter, which Subject to fetch.
   */
  where?: Prisma.SubjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Subjects to fetch.
   */
  orderBy?: Prisma.SubjectOrderByWithRelationInput | Prisma.SubjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Subjects.
   */
  cursor?: Prisma.SubjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Subjects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Subjects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Subjects.
   */
  distinct?: Prisma.SubjectScalarFieldEnum | Prisma.SubjectScalarFieldEnum[]
}

/**
 * Subject findMany
 */
export type SubjectFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * Filter, which Subjects to fetch.
   */
  where?: Prisma.SubjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Subjects to fetch.
   */
  orderBy?: Prisma.SubjectOrderByWithRelationInput | Prisma.SubjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Subjects.
   */
  cursor?: Prisma.SubjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Subjects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Subjects.
   */
  skip?: number
  distinct?: Prisma.SubjectScalarFieldEnum | Prisma.SubjectScalarFieldEnum[]
}

/**
 * Subject create
 */
export type SubjectCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * The data needed to create a Subject.
   */
  data: Prisma.XOR<Prisma.SubjectCreateInput, Prisma.SubjectUncheckedCreateInput>
}

/**
 * Subject createMany
 */
export type SubjectCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Subjects.
   */
  data: Prisma.SubjectCreateManyInput | Prisma.SubjectCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Subject createManyAndReturn
 */
export type SubjectCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * The data used to create many Subjects.
   */
  data: Prisma.SubjectCreateManyInput | Prisma.SubjectCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Subject update
 */
export type SubjectUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * The data needed to update a Subject.
   */
  data: Prisma.XOR<Prisma.SubjectUpdateInput, Prisma.SubjectUncheckedUpdateInput>
  /**
   * Choose, which Subject to update.
   */
  where: Prisma.SubjectWhereUniqueInput
}

/**
 * Subject updateMany
 */
export type SubjectUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Subjects.
   */
  data: Prisma.XOR<Prisma.SubjectUpdateManyMutationInput, Prisma.SubjectUncheckedUpdateManyInput>
  /**
   * Filter which Subjects to update
   */
  where?: Prisma.SubjectWhereInput
  /**
   * Limit how many Subjects to update.
   */
  limit?: number
}

/**
 * Subject updateManyAndReturn
 */
export type SubjectUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * The data used to update Subjects.
   */
  data: Prisma.XOR<Prisma.SubjectUpdateManyMutationInput, Prisma.SubjectUncheckedUpdateManyInput>
  /**
   * Filter which Subjects to update
   */
  where?: Prisma.SubjectWhereInput
  /**
   * Limit how many Subjects to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Subject upsert
 */
export type SubjectUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * The filter to search for the Subject to update in case it exists.
   */
  where: Prisma.SubjectWhereUniqueInput
  /**
   * In case the Subject found by the `where` argument doesn't exist, create a new Subject with this data.
   */
  create: Prisma.XOR<Prisma.SubjectCreateInput, Prisma.SubjectUncheckedCreateInput>
  /**
   * In case the Subject was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SubjectUpdateInput, Prisma.SubjectUncheckedUpdateInput>
}

/**
 * Subject delete
 */
export type SubjectDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
  /**
   * Filter which Subject to delete.
   */
  where: Prisma.SubjectWhereUniqueInput
}

/**
 * Subject deleteMany
 */
export type SubjectDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Subjects to delete
   */
  where?: Prisma.SubjectWhereInput
  /**
   * Limit how many Subjects to delete.
   */
  limit?: number
}

/**
 * Subject.classes
 */
export type Subject$classesArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectClass
   */
  select?: Prisma.SubjectClassSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectClass
   */
  omit?: Prisma.SubjectClassOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectClassInclude<ExtArgs> | null
  where?: Prisma.SubjectClassWhereInput
  orderBy?:
    | Prisma.SubjectClassOrderByWithRelationInput
    | Prisma.SubjectClassOrderByWithRelationInput[]
  cursor?: Prisma.SubjectClassWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SubjectClassScalarFieldEnum | Prisma.SubjectClassScalarFieldEnum[]
}

/**
 * Subject.teachers
 */
export type Subject$teachersArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  where?: Prisma.SubjectTeacherWhereInput
  orderBy?:
    | Prisma.SubjectTeacherOrderByWithRelationInput
    | Prisma.SubjectTeacherOrderByWithRelationInput[]
  cursor?: Prisma.SubjectTeacherWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SubjectTeacherScalarFieldEnum | Prisma.SubjectTeacherScalarFieldEnum[]
}

/**
 * Subject without action
 */
export type SubjectDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Subject
   */
  select?: Prisma.SubjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subject
   */
  omit?: Prisma.SubjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectInclude<ExtArgs> | null
}
