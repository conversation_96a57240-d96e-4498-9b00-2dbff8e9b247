/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Staff` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Staff
 *
 */
export type StaffModel = runtime.Types.Result.DefaultSelection<Prisma.$StaffPayload>

export type AggregateStaff = {
  _count: StaffCountAggregateOutputType | null
  _avg: StaffAvgAggregateOutputType | null
  _sum: StaffSumAggregateOutputType | null
  _min: StaffMinAggregateOutputType | null
  _max: StaffMaxAggregateOutputType | null
}

export type StaffAvgAggregateOutputType = {
  experience: number | null
}

export type StaffSumAggregateOutputType = {
  experience: number | null
}

export type StaffMinAggregateOutputType = {
  id: string | null
  userId: string | null
  employeeId: string | null
  qualification: string | null
  experience: number | null
  joiningDate: Date | null
}

export type StaffMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  employeeId: string | null
  qualification: string | null
  experience: number | null
  joiningDate: Date | null
}

export type StaffCountAggregateOutputType = {
  id: number
  userId: number
  employeeId: number
  qualification: number
  experience: number
  joiningDate: number
  _all: number
}

export type StaffAvgAggregateInputType = {
  experience?: true
}

export type StaffSumAggregateInputType = {
  experience?: true
}

export type StaffMinAggregateInputType = {
  id?: true
  userId?: true
  employeeId?: true
  qualification?: true
  experience?: true
  joiningDate?: true
}

export type StaffMaxAggregateInputType = {
  id?: true
  userId?: true
  employeeId?: true
  qualification?: true
  experience?: true
  joiningDate?: true
}

export type StaffCountAggregateInputType = {
  id?: true
  userId?: true
  employeeId?: true
  qualification?: true
  experience?: true
  joiningDate?: true
  _all?: true
}

export type StaffAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Staff to aggregate.
   */
  where?: Prisma.StaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Staff to fetch.
   */
  orderBy?: Prisma.StaffOrderByWithRelationInput | Prisma.StaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.StaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Staff from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Staff.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Staff
   **/
  _count?: true | StaffCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to average
   **/
  _avg?: StaffAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to sum
   **/
  _sum?: StaffSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: StaffMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: StaffMaxAggregateInputType
}

export type GetStaffAggregateType<T extends StaffAggregateArgs> = {
  [P in keyof T & keyof AggregateStaff]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateStaff[P]>
    : Prisma.GetScalarType<T[P], AggregateStaff[P]>
}

export type StaffGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.StaffWhereInput
  orderBy?: Prisma.StaffOrderByWithAggregationInput | Prisma.StaffOrderByWithAggregationInput[]
  by: Prisma.StaffScalarFieldEnum[] | Prisma.StaffScalarFieldEnum
  having?: Prisma.StaffScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: StaffCountAggregateInputType | true
  _avg?: StaffAvgAggregateInputType
  _sum?: StaffSumAggregateInputType
  _min?: StaffMinAggregateInputType
  _max?: StaffMaxAggregateInputType
}

export type StaffGroupByOutputType = {
  id: string
  userId: string
  employeeId: string
  qualification: string | null
  experience: number | null
  joiningDate: Date
  _count: StaffCountAggregateOutputType | null
  _avg: StaffAvgAggregateOutputType | null
  _sum: StaffSumAggregateOutputType | null
  _min: StaffMinAggregateOutputType | null
  _max: StaffMaxAggregateOutputType | null
}

type GetStaffGroupByPayload<T extends StaffGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<StaffGroupByOutputType, T['by']> & {
      [P in keyof T & keyof StaffGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], StaffGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], StaffGroupByOutputType[P]>
    }
  >
>

export type StaffWhereInput = {
  AND?: Prisma.StaffWhereInput | Prisma.StaffWhereInput[]
  OR?: Prisma.StaffWhereInput[]
  NOT?: Prisma.StaffWhereInput | Prisma.StaffWhereInput[]
  id?: Prisma.StringFilter<'Staff'> | string
  userId?: Prisma.StringFilter<'Staff'> | string
  employeeId?: Prisma.StringFilter<'Staff'> | string
  qualification?: Prisma.StringNullableFilter<'Staff'> | string | null
  experience?: Prisma.IntNullableFilter<'Staff'> | number | null
  joiningDate?: Prisma.DateTimeFilter<'Staff'> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type StaffOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrderInput | Prisma.SortOrder
  experience?: Prisma.SortOrderInput | Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type StaffWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    userId?: string
    employeeId?: string
    AND?: Prisma.StaffWhereInput | Prisma.StaffWhereInput[]
    OR?: Prisma.StaffWhereInput[]
    NOT?: Prisma.StaffWhereInput | Prisma.StaffWhereInput[]
    qualification?: Prisma.StringNullableFilter<'Staff'> | string | null
    experience?: Prisma.IntNullableFilter<'Staff'> | number | null
    joiningDate?: Prisma.DateTimeFilter<'Staff'> | Date | string
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id' | 'userId' | 'employeeId'
>

export type StaffOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrderInput | Prisma.SortOrder
  experience?: Prisma.SortOrderInput | Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
  _count?: Prisma.StaffCountOrderByAggregateInput
  _avg?: Prisma.StaffAvgOrderByAggregateInput
  _max?: Prisma.StaffMaxOrderByAggregateInput
  _min?: Prisma.StaffMinOrderByAggregateInput
  _sum?: Prisma.StaffSumOrderByAggregateInput
}

export type StaffScalarWhereWithAggregatesInput = {
  AND?: Prisma.StaffScalarWhereWithAggregatesInput | Prisma.StaffScalarWhereWithAggregatesInput[]
  OR?: Prisma.StaffScalarWhereWithAggregatesInput[]
  NOT?: Prisma.StaffScalarWhereWithAggregatesInput | Prisma.StaffScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Staff'> | string
  userId?: Prisma.StringWithAggregatesFilter<'Staff'> | string
  employeeId?: Prisma.StringWithAggregatesFilter<'Staff'> | string
  qualification?: Prisma.StringNullableWithAggregatesFilter<'Staff'> | string | null
  experience?: Prisma.IntNullableWithAggregatesFilter<'Staff'> | number | null
  joiningDate?: Prisma.DateTimeWithAggregatesFilter<'Staff'> | Date | string
}

export type StaffCreateInput = {
  id?: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
  user: Prisma.UserCreateNestedOneWithoutStaffProfileInput
}

export type StaffUncheckedCreateInput = {
  id?: string
  userId: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
}

export type StaffUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutStaffProfileNestedInput
}

export type StaffUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StaffCreateManyInput = {
  id?: string
  userId: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
}

export type StaffUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StaffUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StaffNullableScalarRelationFilter = {
  is?: Prisma.StaffWhereInput | null
  isNot?: Prisma.StaffWhereInput | null
}

export type StaffCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
}

export type StaffAvgOrderByAggregateInput = {
  experience?: Prisma.SortOrder
}

export type StaffMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
}

export type StaffMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
}

export type StaffSumOrderByAggregateInput = {
  experience?: Prisma.SortOrder
}

export type StaffCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.StaffCreateWithoutUserInput,
    Prisma.StaffUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StaffCreateOrConnectWithoutUserInput
  connect?: Prisma.StaffWhereUniqueInput
}

export type StaffUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.StaffCreateWithoutUserInput,
    Prisma.StaffUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StaffCreateOrConnectWithoutUserInput
  connect?: Prisma.StaffWhereUniqueInput
}

export type StaffUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.StaffCreateWithoutUserInput,
    Prisma.StaffUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StaffCreateOrConnectWithoutUserInput
  upsert?: Prisma.StaffUpsertWithoutUserInput
  disconnect?: Prisma.StaffWhereInput | boolean
  delete?: Prisma.StaffWhereInput | boolean
  connect?: Prisma.StaffWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.StaffUpdateToOneWithWhereWithoutUserInput,
      Prisma.StaffUpdateWithoutUserInput
    >,
    Prisma.StaffUncheckedUpdateWithoutUserInput
  >
}

export type StaffUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.StaffCreateWithoutUserInput,
    Prisma.StaffUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StaffCreateOrConnectWithoutUserInput
  upsert?: Prisma.StaffUpsertWithoutUserInput
  disconnect?: Prisma.StaffWhereInput | boolean
  delete?: Prisma.StaffWhereInput | boolean
  connect?: Prisma.StaffWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.StaffUpdateToOneWithWhereWithoutUserInput,
      Prisma.StaffUpdateWithoutUserInput
    >,
    Prisma.StaffUncheckedUpdateWithoutUserInput
  >
}

export type StaffCreateWithoutUserInput = {
  id?: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
}

export type StaffUncheckedCreateWithoutUserInput = {
  id?: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
}

export type StaffCreateOrConnectWithoutUserInput = {
  where: Prisma.StaffWhereUniqueInput
  create: Prisma.XOR<
    Prisma.StaffCreateWithoutUserInput,
    Prisma.StaffUncheckedCreateWithoutUserInput
  >
}

export type StaffUpsertWithoutUserInput = {
  update: Prisma.XOR<
    Prisma.StaffUpdateWithoutUserInput,
    Prisma.StaffUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.StaffCreateWithoutUserInput,
    Prisma.StaffUncheckedCreateWithoutUserInput
  >
  where?: Prisma.StaffWhereInput
}

export type StaffUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.StaffWhereInput
  data: Prisma.XOR<Prisma.StaffUpdateWithoutUserInput, Prisma.StaffUncheckedUpdateWithoutUserInput>
}

export type StaffUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StaffUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StaffSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    employeeId?: boolean
    qualification?: boolean
    experience?: boolean
    joiningDate?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['staff']
>

export type StaffSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    employeeId?: boolean
    qualification?: boolean
    experience?: boolean
    joiningDate?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['staff']
>

export type StaffSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    employeeId?: boolean
    qualification?: boolean
    experience?: boolean
    joiningDate?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['staff']
>

export type StaffSelectScalar = {
  id?: boolean
  userId?: boolean
  employeeId?: boolean
  qualification?: boolean
  experience?: boolean
  joiningDate?: boolean
}

export type StaffOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'userId' | 'employeeId' | 'qualification' | 'experience' | 'joiningDate',
  ExtArgs['result']['staff']
>
export type StaffInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type StaffIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type StaffIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $StaffPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Staff'
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      userId: string
      employeeId: string
      qualification: string | null
      experience: number | null
      joiningDate: Date
    },
    ExtArgs['result']['staff']
  >
  composites: {}
}

export type StaffGetPayload<S extends boolean | null | undefined | StaffDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$StaffPayload, S>

export type StaffCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<StaffFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: StaffCountAggregateInputType | true
}

export interface StaffDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Staff']; meta: { name: 'Staff' } }
  /**
   * Find zero or one Staff that matches the filter.
   * @param {StaffFindUniqueArgs} args - Arguments to find a Staff
   * @example
   * // Get one Staff
   * const staff = await prisma.staff.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends StaffFindUniqueArgs>(
    args: Prisma.SelectSubset<T, StaffFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$StaffPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Staff that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {StaffFindUniqueOrThrowArgs} args - Arguments to find a Staff
   * @example
   * // Get one Staff
   * const staff = await prisma.staff.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends StaffFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, StaffFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$StaffPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Staff that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StaffFindFirstArgs} args - Arguments to find a Staff
   * @example
   * // Get one Staff
   * const staff = await prisma.staff.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends StaffFindFirstArgs>(
    args?: Prisma.SelectSubset<T, StaffFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$StaffPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Staff that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StaffFindFirstOrThrowArgs} args - Arguments to find a Staff
   * @example
   * // Get one Staff
   * const staff = await prisma.staff.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends StaffFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, StaffFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$StaffPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Staff that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StaffFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Staff
   * const staff = await prisma.staff.findMany()
   *
   * // Get first 10 Staff
   * const staff = await prisma.staff.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const staffWithIdOnly = await prisma.staff.findMany({ select: { id: true } })
   *
   */
  findMany<T extends StaffFindManyArgs>(
    args?: Prisma.SelectSubset<T, StaffFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<Prisma.$StaffPayload<ExtArgs>, T, 'findMany', GlobalOmitOptions>
  >

  /**
   * Create a Staff.
   * @param {StaffCreateArgs} args - Arguments to create a Staff.
   * @example
   * // Create one Staff
   * const Staff = await prisma.staff.create({
   *   data: {
   *     // ... data to create a Staff
   *   }
   * })
   *
   */
  create<T extends StaffCreateArgs>(
    args: Prisma.SelectSubset<T, StaffCreateArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<Prisma.$StaffPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Staff.
   * @param {StaffCreateManyArgs} args - Arguments to create many Staff.
   * @example
   * // Create many Staff
   * const staff = await prisma.staff.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends StaffCreateManyArgs>(
    args?: Prisma.SelectSubset<T, StaffCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Staff and returns the data saved in the database.
   * @param {StaffCreateManyAndReturnArgs} args - Arguments to create many Staff.
   * @example
   * // Create many Staff
   * const staff = await prisma.staff.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Staff and only return the `id`
   * const staffWithIdOnly = await prisma.staff.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends StaffCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, StaffCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$StaffPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Staff.
   * @param {StaffDeleteArgs} args - Arguments to delete one Staff.
   * @example
   * // Delete one Staff
   * const Staff = await prisma.staff.delete({
   *   where: {
   *     // ... filter to delete one Staff
   *   }
   * })
   *
   */
  delete<T extends StaffDeleteArgs>(
    args: Prisma.SelectSubset<T, StaffDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<Prisma.$StaffPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Staff.
   * @param {StaffUpdateArgs} args - Arguments to update one Staff.
   * @example
   * // Update one Staff
   * const staff = await prisma.staff.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends StaffUpdateArgs>(
    args: Prisma.SelectSubset<T, StaffUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<Prisma.$StaffPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Staff.
   * @param {StaffDeleteManyArgs} args - Arguments to filter Staff to delete.
   * @example
   * // Delete a few Staff
   * const { count } = await prisma.staff.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends StaffDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, StaffDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Staff.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StaffUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Staff
   * const staff = await prisma.staff.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends StaffUpdateManyArgs>(
    args: Prisma.SelectSubset<T, StaffUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Staff and returns the data updated in the database.
   * @param {StaffUpdateManyAndReturnArgs} args - Arguments to update many Staff.
   * @example
   * // Update many Staff
   * const staff = await prisma.staff.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Staff and only return the `id`
   * const staffWithIdOnly = await prisma.staff.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends StaffUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, StaffUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$StaffPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Staff.
   * @param {StaffUpsertArgs} args - Arguments to update or create a Staff.
   * @example
   * // Update or create a Staff
   * const staff = await prisma.staff.upsert({
   *   create: {
   *     // ... data to create a Staff
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Staff we want to update
   *   }
   * })
   */
  upsert<T extends StaffUpsertArgs>(
    args: Prisma.SelectSubset<T, StaffUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<Prisma.$StaffPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Staff.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StaffCountArgs} args - Arguments to filter Staff to count.
   * @example
   * // Count the number of Staff
   * const count = await prisma.staff.count({
   *   where: {
   *     // ... the filter for the Staff we want to count
   *   }
   * })
   **/
  count<T extends StaffCountArgs>(
    args?: Prisma.Subset<T, StaffCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], StaffCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Staff.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StaffAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends StaffAggregateArgs>(
    args: Prisma.Subset<T, StaffAggregateArgs>
  ): Prisma.PrismaPromise<GetStaffAggregateType<T>>

  /**
   * Group by Staff.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StaffGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends StaffGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: StaffGroupByArgs['orderBy'] }
      : { orderBy?: StaffGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, StaffGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetStaffGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Staff model
   */
  readonly fields: StaffFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Staff.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__StaffClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Staff model
 */
export interface StaffFieldRefs {
  readonly id: Prisma.FieldRef<'Staff', 'String'>
  readonly userId: Prisma.FieldRef<'Staff', 'String'>
  readonly employeeId: Prisma.FieldRef<'Staff', 'String'>
  readonly qualification: Prisma.FieldRef<'Staff', 'String'>
  readonly experience: Prisma.FieldRef<'Staff', 'Int'>
  readonly joiningDate: Prisma.FieldRef<'Staff', 'DateTime'>
}

// Custom InputTypes
/**
 * Staff findUnique
 */
export type StaffFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * Filter, which Staff to fetch.
   */
  where: Prisma.StaffWhereUniqueInput
}

/**
 * Staff findUniqueOrThrow
 */
export type StaffFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * Filter, which Staff to fetch.
   */
  where: Prisma.StaffWhereUniqueInput
}

/**
 * Staff findFirst
 */
export type StaffFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * Filter, which Staff to fetch.
   */
  where?: Prisma.StaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Staff to fetch.
   */
  orderBy?: Prisma.StaffOrderByWithRelationInput | Prisma.StaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Staff.
   */
  cursor?: Prisma.StaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Staff from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Staff.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Staff.
   */
  distinct?: Prisma.StaffScalarFieldEnum | Prisma.StaffScalarFieldEnum[]
}

/**
 * Staff findFirstOrThrow
 */
export type StaffFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * Filter, which Staff to fetch.
   */
  where?: Prisma.StaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Staff to fetch.
   */
  orderBy?: Prisma.StaffOrderByWithRelationInput | Prisma.StaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Staff.
   */
  cursor?: Prisma.StaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Staff from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Staff.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Staff.
   */
  distinct?: Prisma.StaffScalarFieldEnum | Prisma.StaffScalarFieldEnum[]
}

/**
 * Staff findMany
 */
export type StaffFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * Filter, which Staff to fetch.
   */
  where?: Prisma.StaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Staff to fetch.
   */
  orderBy?: Prisma.StaffOrderByWithRelationInput | Prisma.StaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Staff.
   */
  cursor?: Prisma.StaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Staff from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Staff.
   */
  skip?: number
  distinct?: Prisma.StaffScalarFieldEnum | Prisma.StaffScalarFieldEnum[]
}

/**
 * Staff create
 */
export type StaffCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * The data needed to create a Staff.
   */
  data: Prisma.XOR<Prisma.StaffCreateInput, Prisma.StaffUncheckedCreateInput>
}

/**
 * Staff createMany
 */
export type StaffCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Staff.
   */
  data: Prisma.StaffCreateManyInput | Prisma.StaffCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Staff createManyAndReturn
 */
export type StaffCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * The data used to create many Staff.
   */
  data: Prisma.StaffCreateManyInput | Prisma.StaffCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Staff update
 */
export type StaffUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * The data needed to update a Staff.
   */
  data: Prisma.XOR<Prisma.StaffUpdateInput, Prisma.StaffUncheckedUpdateInput>
  /**
   * Choose, which Staff to update.
   */
  where: Prisma.StaffWhereUniqueInput
}

/**
 * Staff updateMany
 */
export type StaffUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Staff.
   */
  data: Prisma.XOR<Prisma.StaffUpdateManyMutationInput, Prisma.StaffUncheckedUpdateManyInput>
  /**
   * Filter which Staff to update
   */
  where?: Prisma.StaffWhereInput
  /**
   * Limit how many Staff to update.
   */
  limit?: number
}

/**
 * Staff updateManyAndReturn
 */
export type StaffUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * The data used to update Staff.
   */
  data: Prisma.XOR<Prisma.StaffUpdateManyMutationInput, Prisma.StaffUncheckedUpdateManyInput>
  /**
   * Filter which Staff to update
   */
  where?: Prisma.StaffWhereInput
  /**
   * Limit how many Staff to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Staff upsert
 */
export type StaffUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * The filter to search for the Staff to update in case it exists.
   */
  where: Prisma.StaffWhereUniqueInput
  /**
   * In case the Staff found by the `where` argument doesn't exist, create a new Staff with this data.
   */
  create: Prisma.XOR<Prisma.StaffCreateInput, Prisma.StaffUncheckedCreateInput>
  /**
   * In case the Staff was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.StaffUpdateInput, Prisma.StaffUncheckedUpdateInput>
}

/**
 * Staff delete
 */
export type StaffDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  /**
   * Filter which Staff to delete.
   */
  where: Prisma.StaffWhereUniqueInput
}

/**
 * Staff deleteMany
 */
export type StaffDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Staff to delete
   */
  where?: Prisma.StaffWhereInput
  /**
   * Limit how many Staff to delete.
   */
  limit?: number
}

/**
 * Staff without action
 */
export type StaffDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
}
