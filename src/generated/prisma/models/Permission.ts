/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Permission` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Permission
 *
 */
export type PermissionModel = runtime.Types.Result.DefaultSelection<Prisma.$PermissionPayload>

export type AggregatePermission = {
  _count: PermissionCountAggregateOutputType | null
  _min: PermissionMinAggregateOutputType | null
  _max: PermissionMaxAggregateOutputType | null
}

export type PermissionMinAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  resource: string | null
  action: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PermissionMaxAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  resource: string | null
  action: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PermissionCountAggregateOutputType = {
  id: number
  name: number
  description: number
  resource: number
  action: number
  createdAt: number
  updatedAt: number
  _all: number
}

export type PermissionMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  resource?: true
  action?: true
  createdAt?: true
  updatedAt?: true
}

export type PermissionMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  resource?: true
  action?: true
  createdAt?: true
  updatedAt?: true
}

export type PermissionCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  resource?: true
  action?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type PermissionAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Permission to aggregate.
   */
  where?: Prisma.PermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Permissions to fetch.
   */
  orderBy?: Prisma.PermissionOrderByWithRelationInput | Prisma.PermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.PermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Permissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Permissions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Permissions
   **/
  _count?: true | PermissionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: PermissionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: PermissionMaxAggregateInputType
}

export type GetPermissionAggregateType<T extends PermissionAggregateArgs> = {
  [P in keyof T & keyof AggregatePermission]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePermission[P]>
    : Prisma.GetScalarType<T[P], AggregatePermission[P]>
}

export type PermissionGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.PermissionWhereInput
  orderBy?:
    | Prisma.PermissionOrderByWithAggregationInput
    | Prisma.PermissionOrderByWithAggregationInput[]
  by: Prisma.PermissionScalarFieldEnum[] | Prisma.PermissionScalarFieldEnum
  having?: Prisma.PermissionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PermissionCountAggregateInputType | true
  _min?: PermissionMinAggregateInputType
  _max?: PermissionMaxAggregateInputType
}

export type PermissionGroupByOutputType = {
  id: string
  name: string
  description: string | null
  resource: string
  action: string
  createdAt: Date
  updatedAt: Date
  _count: PermissionCountAggregateOutputType | null
  _min: PermissionMinAggregateOutputType | null
  _max: PermissionMaxAggregateOutputType | null
}

type GetPermissionGroupByPayload<T extends PermissionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PermissionGroupByOutputType, T['by']> & {
      [P in keyof T & keyof PermissionGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], PermissionGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], PermissionGroupByOutputType[P]>
    }
  >
>

export type PermissionWhereInput = {
  AND?: Prisma.PermissionWhereInput | Prisma.PermissionWhereInput[]
  OR?: Prisma.PermissionWhereInput[]
  NOT?: Prisma.PermissionWhereInput | Prisma.PermissionWhereInput[]
  id?: Prisma.StringFilter<'Permission'> | string
  name?: Prisma.StringFilter<'Permission'> | string
  description?: Prisma.StringNullableFilter<'Permission'> | string | null
  resource?: Prisma.StringFilter<'Permission'> | string
  action?: Prisma.StringFilter<'Permission'> | string
  createdAt?: Prisma.DateTimeFilter<'Permission'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Permission'> | Date | string
  rolePermissions?: Prisma.RolePermissionListRelationFilter
}

export type PermissionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  resource?: Prisma.SortOrder
  action?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  rolePermissions?: Prisma.RolePermissionOrderByRelationAggregateInput
}

export type PermissionWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    name?: string
    resource_action?: Prisma.PermissionResourceActionCompoundUniqueInput
    AND?: Prisma.PermissionWhereInput | Prisma.PermissionWhereInput[]
    OR?: Prisma.PermissionWhereInput[]
    NOT?: Prisma.PermissionWhereInput | Prisma.PermissionWhereInput[]
    description?: Prisma.StringNullableFilter<'Permission'> | string | null
    resource?: Prisma.StringFilter<'Permission'> | string
    action?: Prisma.StringFilter<'Permission'> | string
    createdAt?: Prisma.DateTimeFilter<'Permission'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'Permission'> | Date | string
    rolePermissions?: Prisma.RolePermissionListRelationFilter
  },
  'id' | 'name' | 'resource_action'
>

export type PermissionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  resource?: Prisma.SortOrder
  action?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.PermissionCountOrderByAggregateInput
  _max?: Prisma.PermissionMaxOrderByAggregateInput
  _min?: Prisma.PermissionMinOrderByAggregateInput
}

export type PermissionScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.PermissionScalarWhereWithAggregatesInput
    | Prisma.PermissionScalarWhereWithAggregatesInput[]
  OR?: Prisma.PermissionScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.PermissionScalarWhereWithAggregatesInput
    | Prisma.PermissionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Permission'> | string
  name?: Prisma.StringWithAggregatesFilter<'Permission'> | string
  description?: Prisma.StringNullableWithAggregatesFilter<'Permission'> | string | null
  resource?: Prisma.StringWithAggregatesFilter<'Permission'> | string
  action?: Prisma.StringWithAggregatesFilter<'Permission'> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'Permission'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'Permission'> | Date | string
}

export type PermissionCreateInput = {
  id?: string
  name: string
  description?: string | null
  resource: string
  action: string
  createdAt?: Date | string
  updatedAt?: Date | string
  rolePermissions?: Prisma.RolePermissionCreateNestedManyWithoutPermissionInput
}

export type PermissionUncheckedCreateInput = {
  id?: string
  name: string
  description?: string | null
  resource: string
  action: string
  createdAt?: Date | string
  updatedAt?: Date | string
  rolePermissions?: Prisma.RolePermissionUncheckedCreateNestedManyWithoutPermissionInput
}

export type PermissionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  rolePermissions?: Prisma.RolePermissionUpdateManyWithoutPermissionNestedInput
}

export type PermissionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  rolePermissions?: Prisma.RolePermissionUncheckedUpdateManyWithoutPermissionNestedInput
}

export type PermissionCreateManyInput = {
  id?: string
  name: string
  description?: string | null
  resource: string
  action: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PermissionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PermissionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PermissionResourceActionCompoundUniqueInput = {
  resource: string
  action: string
}

export type PermissionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  action?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PermissionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  action?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PermissionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  action?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PermissionScalarRelationFilter = {
  is?: Prisma.PermissionWhereInput
  isNot?: Prisma.PermissionWhereInput
}

export type PermissionCreateNestedOneWithoutRolePermissionsInput = {
  create?: Prisma.XOR<
    Prisma.PermissionCreateWithoutRolePermissionsInput,
    Prisma.PermissionUncheckedCreateWithoutRolePermissionsInput
  >
  connectOrCreate?: Prisma.PermissionCreateOrConnectWithoutRolePermissionsInput
  connect?: Prisma.PermissionWhereUniqueInput
}

export type PermissionUpdateOneRequiredWithoutRolePermissionsNestedInput = {
  create?: Prisma.XOR<
    Prisma.PermissionCreateWithoutRolePermissionsInput,
    Prisma.PermissionUncheckedCreateWithoutRolePermissionsInput
  >
  connectOrCreate?: Prisma.PermissionCreateOrConnectWithoutRolePermissionsInput
  upsert?: Prisma.PermissionUpsertWithoutRolePermissionsInput
  connect?: Prisma.PermissionWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.PermissionUpdateToOneWithWhereWithoutRolePermissionsInput,
      Prisma.PermissionUpdateWithoutRolePermissionsInput
    >,
    Prisma.PermissionUncheckedUpdateWithoutRolePermissionsInput
  >
}

export type PermissionCreateWithoutRolePermissionsInput = {
  id?: string
  name: string
  description?: string | null
  resource: string
  action: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PermissionUncheckedCreateWithoutRolePermissionsInput = {
  id?: string
  name: string
  description?: string | null
  resource: string
  action: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PermissionCreateOrConnectWithoutRolePermissionsInput = {
  where: Prisma.PermissionWhereUniqueInput
  create: Prisma.XOR<
    Prisma.PermissionCreateWithoutRolePermissionsInput,
    Prisma.PermissionUncheckedCreateWithoutRolePermissionsInput
  >
}

export type PermissionUpsertWithoutRolePermissionsInput = {
  update: Prisma.XOR<
    Prisma.PermissionUpdateWithoutRolePermissionsInput,
    Prisma.PermissionUncheckedUpdateWithoutRolePermissionsInput
  >
  create: Prisma.XOR<
    Prisma.PermissionCreateWithoutRolePermissionsInput,
    Prisma.PermissionUncheckedCreateWithoutRolePermissionsInput
  >
  where?: Prisma.PermissionWhereInput
}

export type PermissionUpdateToOneWithWhereWithoutRolePermissionsInput = {
  where?: Prisma.PermissionWhereInput
  data: Prisma.XOR<
    Prisma.PermissionUpdateWithoutRolePermissionsInput,
    Prisma.PermissionUncheckedUpdateWithoutRolePermissionsInput
  >
}

export type PermissionUpdateWithoutRolePermissionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PermissionUncheckedUpdateWithoutRolePermissionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

/**
 * Count Type PermissionCountOutputType
 */

export type PermissionCountOutputType = {
  rolePermissions: number
}

export type PermissionCountOutputTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  rolePermissions?: boolean | PermissionCountOutputTypeCountRolePermissionsArgs
}

/**
 * PermissionCountOutputType without action
 */
export type PermissionCountOutputTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the PermissionCountOutputType
   */
  select?: Prisma.PermissionCountOutputTypeSelect<ExtArgs> | null
}

/**
 * PermissionCountOutputType without action
 */
export type PermissionCountOutputTypeCountRolePermissionsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.RolePermissionWhereInput
}

export type PermissionSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    resource?: boolean
    action?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    rolePermissions?: boolean | Prisma.Permission$rolePermissionsArgs<ExtArgs>
    _count?: boolean | Prisma.PermissionCountOutputTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['permission']
>

export type PermissionSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    resource?: boolean
    action?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  },
  ExtArgs['result']['permission']
>

export type PermissionSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    resource?: boolean
    action?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  },
  ExtArgs['result']['permission']
>

export type PermissionSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  resource?: boolean
  action?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type PermissionOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'name' | 'description' | 'resource' | 'action' | 'createdAt' | 'updatedAt',
  ExtArgs['result']['permission']
>
export type PermissionInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  rolePermissions?: boolean | Prisma.Permission$rolePermissionsArgs<ExtArgs>
  _count?: boolean | Prisma.PermissionCountOutputTypeDefaultArgs<ExtArgs>
}
export type PermissionIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {}
export type PermissionIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {}

export type $PermissionPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Permission'
  objects: {
    rolePermissions: Prisma.$RolePermissionPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      name: string
      description: string | null
      resource: string
      action: string
      createdAt: Date
      updatedAt: Date
    },
    ExtArgs['result']['permission']
  >
  composites: {}
}

export type PermissionGetPayload<S extends boolean | null | undefined | PermissionDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$PermissionPayload, S>

export type PermissionCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<PermissionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: PermissionCountAggregateInputType | true
}

export interface PermissionDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['Permission']
    meta: { name: 'Permission' }
  }
  /**
   * Find zero or one Permission that matches the filter.
   * @param {PermissionFindUniqueArgs} args - Arguments to find a Permission
   * @example
   * // Get one Permission
   * const permission = await prisma.permission.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends PermissionFindUniqueArgs>(
    args: Prisma.SelectSubset<T, PermissionFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Permission that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {PermissionFindUniqueOrThrowArgs} args - Arguments to find a Permission
   * @example
   * // Get one Permission
   * const permission = await prisma.permission.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends PermissionFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, PermissionFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Permission that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PermissionFindFirstArgs} args - Arguments to find a Permission
   * @example
   * // Get one Permission
   * const permission = await prisma.permission.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends PermissionFindFirstArgs>(
    args?: Prisma.SelectSubset<T, PermissionFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Permission that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PermissionFindFirstOrThrowArgs} args - Arguments to find a Permission
   * @example
   * // Get one Permission
   * const permission = await prisma.permission.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends PermissionFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, PermissionFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Permissions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PermissionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Permissions
   * const permissions = await prisma.permission.findMany()
   *
   * // Get first 10 Permissions
   * const permissions = await prisma.permission.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const permissionWithIdOnly = await prisma.permission.findMany({ select: { id: true } })
   *
   */
  findMany<T extends PermissionFindManyArgs>(
    args?: Prisma.SelectSubset<T, PermissionFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a Permission.
   * @param {PermissionCreateArgs} args - Arguments to create a Permission.
   * @example
   * // Create one Permission
   * const Permission = await prisma.permission.create({
   *   data: {
   *     // ... data to create a Permission
   *   }
   * })
   *
   */
  create<T extends PermissionCreateArgs>(
    args: Prisma.SelectSubset<T, PermissionCreateArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Permissions.
   * @param {PermissionCreateManyArgs} args - Arguments to create many Permissions.
   * @example
   * // Create many Permissions
   * const permission = await prisma.permission.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends PermissionCreateManyArgs>(
    args?: Prisma.SelectSubset<T, PermissionCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Permissions and returns the data saved in the database.
   * @param {PermissionCreateManyAndReturnArgs} args - Arguments to create many Permissions.
   * @example
   * // Create many Permissions
   * const permission = await prisma.permission.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Permissions and only return the `id`
   * const permissionWithIdOnly = await prisma.permission.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends PermissionCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, PermissionCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Permission.
   * @param {PermissionDeleteArgs} args - Arguments to delete one Permission.
   * @example
   * // Delete one Permission
   * const Permission = await prisma.permission.delete({
   *   where: {
   *     // ... filter to delete one Permission
   *   }
   * })
   *
   */
  delete<T extends PermissionDeleteArgs>(
    args: Prisma.SelectSubset<T, PermissionDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Permission.
   * @param {PermissionUpdateArgs} args - Arguments to update one Permission.
   * @example
   * // Update one Permission
   * const permission = await prisma.permission.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends PermissionUpdateArgs>(
    args: Prisma.SelectSubset<T, PermissionUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Permissions.
   * @param {PermissionDeleteManyArgs} args - Arguments to filter Permissions to delete.
   * @example
   * // Delete a few Permissions
   * const { count } = await prisma.permission.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends PermissionDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, PermissionDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Permissions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PermissionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Permissions
   * const permission = await prisma.permission.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends PermissionUpdateManyArgs>(
    args: Prisma.SelectSubset<T, PermissionUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Permissions and returns the data updated in the database.
   * @param {PermissionUpdateManyAndReturnArgs} args - Arguments to update many Permissions.
   * @example
   * // Update many Permissions
   * const permission = await prisma.permission.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Permissions and only return the `id`
   * const permissionWithIdOnly = await prisma.permission.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends PermissionUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, PermissionUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Permission.
   * @param {PermissionUpsertArgs} args - Arguments to update or create a Permission.
   * @example
   * // Update or create a Permission
   * const permission = await prisma.permission.upsert({
   *   create: {
   *     // ... data to create a Permission
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Permission we want to update
   *   }
   * })
   */
  upsert<T extends PermissionUpsertArgs>(
    args: Prisma.SelectSubset<T, PermissionUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$PermissionPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Permissions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PermissionCountArgs} args - Arguments to filter Permissions to count.
   * @example
   * // Count the number of Permissions
   * const count = await prisma.permission.count({
   *   where: {
   *     // ... the filter for the Permissions we want to count
   *   }
   * })
   **/
  count<T extends PermissionCountArgs>(
    args?: Prisma.Subset<T, PermissionCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PermissionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Permission.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PermissionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends PermissionAggregateArgs>(
    args: Prisma.Subset<T, PermissionAggregateArgs>
  ): Prisma.PrismaPromise<GetPermissionAggregateType<T>>

  /**
   * Group by Permission.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PermissionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends PermissionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: PermissionGroupByArgs['orderBy'] }
      : { orderBy?: PermissionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, PermissionGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetPermissionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Permission model
   */
  readonly fields: PermissionFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Permission.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__PermissionClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  rolePermissions<T extends Prisma.Permission$rolePermissionsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.Permission$rolePermissionsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$RolePermissionPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Permission model
 */
export interface PermissionFieldRefs {
  readonly id: Prisma.FieldRef<'Permission', 'String'>
  readonly name: Prisma.FieldRef<'Permission', 'String'>
  readonly description: Prisma.FieldRef<'Permission', 'String'>
  readonly resource: Prisma.FieldRef<'Permission', 'String'>
  readonly action: Prisma.FieldRef<'Permission', 'String'>
  readonly createdAt: Prisma.FieldRef<'Permission', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'Permission', 'DateTime'>
}

// Custom InputTypes
/**
 * Permission findUnique
 */
export type PermissionFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * Filter, which Permission to fetch.
   */
  where: Prisma.PermissionWhereUniqueInput
}

/**
 * Permission findUniqueOrThrow
 */
export type PermissionFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * Filter, which Permission to fetch.
   */
  where: Prisma.PermissionWhereUniqueInput
}

/**
 * Permission findFirst
 */
export type PermissionFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * Filter, which Permission to fetch.
   */
  where?: Prisma.PermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Permissions to fetch.
   */
  orderBy?: Prisma.PermissionOrderByWithRelationInput | Prisma.PermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Permissions.
   */
  cursor?: Prisma.PermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Permissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Permissions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Permissions.
   */
  distinct?: Prisma.PermissionScalarFieldEnum | Prisma.PermissionScalarFieldEnum[]
}

/**
 * Permission findFirstOrThrow
 */
export type PermissionFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * Filter, which Permission to fetch.
   */
  where?: Prisma.PermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Permissions to fetch.
   */
  orderBy?: Prisma.PermissionOrderByWithRelationInput | Prisma.PermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Permissions.
   */
  cursor?: Prisma.PermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Permissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Permissions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Permissions.
   */
  distinct?: Prisma.PermissionScalarFieldEnum | Prisma.PermissionScalarFieldEnum[]
}

/**
 * Permission findMany
 */
export type PermissionFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * Filter, which Permissions to fetch.
   */
  where?: Prisma.PermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Permissions to fetch.
   */
  orderBy?: Prisma.PermissionOrderByWithRelationInput | Prisma.PermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Permissions.
   */
  cursor?: Prisma.PermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Permissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Permissions.
   */
  skip?: number
  distinct?: Prisma.PermissionScalarFieldEnum | Prisma.PermissionScalarFieldEnum[]
}

/**
 * Permission create
 */
export type PermissionCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * The data needed to create a Permission.
   */
  data: Prisma.XOR<Prisma.PermissionCreateInput, Prisma.PermissionUncheckedCreateInput>
}

/**
 * Permission createMany
 */
export type PermissionCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Permissions.
   */
  data: Prisma.PermissionCreateManyInput | Prisma.PermissionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Permission createManyAndReturn
 */
export type PermissionCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * The data used to create many Permissions.
   */
  data: Prisma.PermissionCreateManyInput | Prisma.PermissionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Permission update
 */
export type PermissionUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * The data needed to update a Permission.
   */
  data: Prisma.XOR<Prisma.PermissionUpdateInput, Prisma.PermissionUncheckedUpdateInput>
  /**
   * Choose, which Permission to update.
   */
  where: Prisma.PermissionWhereUniqueInput
}

/**
 * Permission updateMany
 */
export type PermissionUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Permissions.
   */
  data: Prisma.XOR<
    Prisma.PermissionUpdateManyMutationInput,
    Prisma.PermissionUncheckedUpdateManyInput
  >
  /**
   * Filter which Permissions to update
   */
  where?: Prisma.PermissionWhereInput
  /**
   * Limit how many Permissions to update.
   */
  limit?: number
}

/**
 * Permission updateManyAndReturn
 */
export type PermissionUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * The data used to update Permissions.
   */
  data: Prisma.XOR<
    Prisma.PermissionUpdateManyMutationInput,
    Prisma.PermissionUncheckedUpdateManyInput
  >
  /**
   * Filter which Permissions to update
   */
  where?: Prisma.PermissionWhereInput
  /**
   * Limit how many Permissions to update.
   */
  limit?: number
}

/**
 * Permission upsert
 */
export type PermissionUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * The filter to search for the Permission to update in case it exists.
   */
  where: Prisma.PermissionWhereUniqueInput
  /**
   * In case the Permission found by the `where` argument doesn't exist, create a new Permission with this data.
   */
  create: Prisma.XOR<Prisma.PermissionCreateInput, Prisma.PermissionUncheckedCreateInput>
  /**
   * In case the Permission was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.PermissionUpdateInput, Prisma.PermissionUncheckedUpdateInput>
}

/**
 * Permission delete
 */
export type PermissionDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
  /**
   * Filter which Permission to delete.
   */
  where: Prisma.PermissionWhereUniqueInput
}

/**
 * Permission deleteMany
 */
export type PermissionDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Permissions to delete
   */
  where?: Prisma.PermissionWhereInput
  /**
   * Limit how many Permissions to delete.
   */
  limit?: number
}

/**
 * Permission.rolePermissions
 */
export type Permission$rolePermissionsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  where?: Prisma.RolePermissionWhereInput
  orderBy?:
    | Prisma.RolePermissionOrderByWithRelationInput
    | Prisma.RolePermissionOrderByWithRelationInput[]
  cursor?: Prisma.RolePermissionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RolePermissionScalarFieldEnum | Prisma.RolePermissionScalarFieldEnum[]
}

/**
 * Permission without action
 */
export type PermissionDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Permission
   */
  select?: Prisma.PermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Permission
   */
  omit?: Prisma.PermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PermissionInclude<ExtArgs> | null
}
