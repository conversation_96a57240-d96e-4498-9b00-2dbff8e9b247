/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `LoginLog` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model LoginLog
 *
 */
export type LoginLogModel = runtime.Types.Result.DefaultSelection<Prisma.$LoginLogPayload>

export type AggregateLoginLog = {
  _count: LoginLogCountAggregateOutputType | null
  _min: LoginLogMinAggregateOutputType | null
  _max: LoginLogMaxAggregateOutputType | null
}

export type LoginLogMinAggregateOutputType = {
  id: string | null
  userId: string | null
  ipAddress: string | null
  userAgent: string | null
  loginAt: Date | null
  logoutAt: Date | null
}

export type LoginLogMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  ipAddress: string | null
  userAgent: string | null
  loginAt: Date | null
  logoutAt: Date | null
}

export type LoginLogCountAggregateOutputType = {
  id: number
  userId: number
  ipAddress: number
  userAgent: number
  loginAt: number
  logoutAt: number
  _all: number
}

export type LoginLogMinAggregateInputType = {
  id?: true
  userId?: true
  ipAddress?: true
  userAgent?: true
  loginAt?: true
  logoutAt?: true
}

export type LoginLogMaxAggregateInputType = {
  id?: true
  userId?: true
  ipAddress?: true
  userAgent?: true
  loginAt?: true
  logoutAt?: true
}

export type LoginLogCountAggregateInputType = {
  id?: true
  userId?: true
  ipAddress?: true
  userAgent?: true
  loginAt?: true
  logoutAt?: true
  _all?: true
}

export type LoginLogAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which LoginLog to aggregate.
   */
  where?: Prisma.LoginLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of LoginLogs to fetch.
   */
  orderBy?: Prisma.LoginLogOrderByWithRelationInput | Prisma.LoginLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.LoginLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` LoginLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` LoginLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned LoginLogs
   **/
  _count?: true | LoginLogCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: LoginLogMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: LoginLogMaxAggregateInputType
}

export type GetLoginLogAggregateType<T extends LoginLogAggregateArgs> = {
  [P in keyof T & keyof AggregateLoginLog]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLoginLog[P]>
    : Prisma.GetScalarType<T[P], AggregateLoginLog[P]>
}

export type LoginLogGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.LoginLogWhereInput
  orderBy?:
    | Prisma.LoginLogOrderByWithAggregationInput
    | Prisma.LoginLogOrderByWithAggregationInput[]
  by: Prisma.LoginLogScalarFieldEnum[] | Prisma.LoginLogScalarFieldEnum
  having?: Prisma.LoginLogScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: LoginLogCountAggregateInputType | true
  _min?: LoginLogMinAggregateInputType
  _max?: LoginLogMaxAggregateInputType
}

export type LoginLogGroupByOutputType = {
  id: string
  userId: string
  ipAddress: string
  userAgent: string | null
  loginAt: Date
  logoutAt: Date | null
  _count: LoginLogCountAggregateOutputType | null
  _min: LoginLogMinAggregateOutputType | null
  _max: LoginLogMaxAggregateOutputType | null
}

type GetLoginLogGroupByPayload<T extends LoginLogGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<LoginLogGroupByOutputType, T['by']> & {
      [P in keyof T & keyof LoginLogGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], LoginLogGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], LoginLogGroupByOutputType[P]>
    }
  >
>

export type LoginLogWhereInput = {
  AND?: Prisma.LoginLogWhereInput | Prisma.LoginLogWhereInput[]
  OR?: Prisma.LoginLogWhereInput[]
  NOT?: Prisma.LoginLogWhereInput | Prisma.LoginLogWhereInput[]
  id?: Prisma.StringFilter<'LoginLog'> | string
  userId?: Prisma.StringFilter<'LoginLog'> | string
  ipAddress?: Prisma.StringFilter<'LoginLog'> | string
  userAgent?: Prisma.StringNullableFilter<'LoginLog'> | string | null
  loginAt?: Prisma.DateTimeFilter<'LoginLog'> | Date | string
  logoutAt?: Prisma.DateTimeNullableFilter<'LoginLog'> | Date | string | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type LoginLogOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  loginAt?: Prisma.SortOrder
  logoutAt?: Prisma.SortOrderInput | Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type LoginLogWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    AND?: Prisma.LoginLogWhereInput | Prisma.LoginLogWhereInput[]
    OR?: Prisma.LoginLogWhereInput[]
    NOT?: Prisma.LoginLogWhereInput | Prisma.LoginLogWhereInput[]
    userId?: Prisma.StringFilter<'LoginLog'> | string
    ipAddress?: Prisma.StringFilter<'LoginLog'> | string
    userAgent?: Prisma.StringNullableFilter<'LoginLog'> | string | null
    loginAt?: Prisma.DateTimeFilter<'LoginLog'> | Date | string
    logoutAt?: Prisma.DateTimeNullableFilter<'LoginLog'> | Date | string | null
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id'
>

export type LoginLogOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  loginAt?: Prisma.SortOrder
  logoutAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.LoginLogCountOrderByAggregateInput
  _max?: Prisma.LoginLogMaxOrderByAggregateInput
  _min?: Prisma.LoginLogMinOrderByAggregateInput
}

export type LoginLogScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.LoginLogScalarWhereWithAggregatesInput
    | Prisma.LoginLogScalarWhereWithAggregatesInput[]
  OR?: Prisma.LoginLogScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.LoginLogScalarWhereWithAggregatesInput
    | Prisma.LoginLogScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'LoginLog'> | string
  userId?: Prisma.StringWithAggregatesFilter<'LoginLog'> | string
  ipAddress?: Prisma.StringWithAggregatesFilter<'LoginLog'> | string
  userAgent?: Prisma.StringNullableWithAggregatesFilter<'LoginLog'> | string | null
  loginAt?: Prisma.DateTimeWithAggregatesFilter<'LoginLog'> | Date | string
  logoutAt?: Prisma.DateTimeNullableWithAggregatesFilter<'LoginLog'> | Date | string | null
}

export type LoginLogCreateInput = {
  id?: string
  ipAddress: string
  userAgent?: string | null
  loginAt?: Date | string
  logoutAt?: Date | string | null
  user: Prisma.UserCreateNestedOneWithoutLoginLogsInput
}

export type LoginLogUncheckedCreateInput = {
  id?: string
  userId: string
  ipAddress: string
  userAgent?: string | null
  loginAt?: Date | string
  logoutAt?: Date | string | null
}

export type LoginLogUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.StringFieldUpdateOperationsInput | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  loginAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  logoutAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  user?: Prisma.UserUpdateOneRequiredWithoutLoginLogsNestedInput
}

export type LoginLogUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.StringFieldUpdateOperationsInput | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  loginAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  logoutAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LoginLogCreateManyInput = {
  id?: string
  userId: string
  ipAddress: string
  userAgent?: string | null
  loginAt?: Date | string
  logoutAt?: Date | string | null
}

export type LoginLogUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.StringFieldUpdateOperationsInput | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  loginAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  logoutAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LoginLogUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.StringFieldUpdateOperationsInput | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  loginAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  logoutAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LoginLogCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  loginAt?: Prisma.SortOrder
  logoutAt?: Prisma.SortOrder
}

export type LoginLogMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  loginAt?: Prisma.SortOrder
  logoutAt?: Prisma.SortOrder
}

export type LoginLogMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  loginAt?: Prisma.SortOrder
  logoutAt?: Prisma.SortOrder
}

export type LoginLogListRelationFilter = {
  every?: Prisma.LoginLogWhereInput
  some?: Prisma.LoginLogWhereInput
  none?: Prisma.LoginLogWhereInput
}

export type LoginLogOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type NullableDateTimeFieldUpdateOperationsInput = {
  set?: Date | string | null
}

export type LoginLogCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.LoginLogCreateWithoutUserInput,
        Prisma.LoginLogUncheckedCreateWithoutUserInput
      >
    | Prisma.LoginLogCreateWithoutUserInput[]
    | Prisma.LoginLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.LoginLogCreateOrConnectWithoutUserInput
    | Prisma.LoginLogCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.LoginLogCreateManyUserInputEnvelope
  connect?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
}

export type LoginLogUncheckedCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.LoginLogCreateWithoutUserInput,
        Prisma.LoginLogUncheckedCreateWithoutUserInput
      >
    | Prisma.LoginLogCreateWithoutUserInput[]
    | Prisma.LoginLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.LoginLogCreateOrConnectWithoutUserInput
    | Prisma.LoginLogCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.LoginLogCreateManyUserInputEnvelope
  connect?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
}

export type LoginLogUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.LoginLogCreateWithoutUserInput,
        Prisma.LoginLogUncheckedCreateWithoutUserInput
      >
    | Prisma.LoginLogCreateWithoutUserInput[]
    | Prisma.LoginLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.LoginLogCreateOrConnectWithoutUserInput
    | Prisma.LoginLogCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.LoginLogUpsertWithWhereUniqueWithoutUserInput
    | Prisma.LoginLogUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.LoginLogCreateManyUserInputEnvelope
  set?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  disconnect?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  delete?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  connect?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  update?:
    | Prisma.LoginLogUpdateWithWhereUniqueWithoutUserInput
    | Prisma.LoginLogUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.LoginLogUpdateManyWithWhereWithoutUserInput
    | Prisma.LoginLogUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.LoginLogScalarWhereInput | Prisma.LoginLogScalarWhereInput[]
}

export type LoginLogUncheckedUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.LoginLogCreateWithoutUserInput,
        Prisma.LoginLogUncheckedCreateWithoutUserInput
      >
    | Prisma.LoginLogCreateWithoutUserInput[]
    | Prisma.LoginLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.LoginLogCreateOrConnectWithoutUserInput
    | Prisma.LoginLogCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.LoginLogUpsertWithWhereUniqueWithoutUserInput
    | Prisma.LoginLogUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.LoginLogCreateManyUserInputEnvelope
  set?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  disconnect?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  delete?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  connect?: Prisma.LoginLogWhereUniqueInput | Prisma.LoginLogWhereUniqueInput[]
  update?:
    | Prisma.LoginLogUpdateWithWhereUniqueWithoutUserInput
    | Prisma.LoginLogUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.LoginLogUpdateManyWithWhereWithoutUserInput
    | Prisma.LoginLogUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.LoginLogScalarWhereInput | Prisma.LoginLogScalarWhereInput[]
}

export type LoginLogCreateWithoutUserInput = {
  id?: string
  ipAddress: string
  userAgent?: string | null
  loginAt?: Date | string
  logoutAt?: Date | string | null
}

export type LoginLogUncheckedCreateWithoutUserInput = {
  id?: string
  ipAddress: string
  userAgent?: string | null
  loginAt?: Date | string
  logoutAt?: Date | string | null
}

export type LoginLogCreateOrConnectWithoutUserInput = {
  where: Prisma.LoginLogWhereUniqueInput
  create: Prisma.XOR<
    Prisma.LoginLogCreateWithoutUserInput,
    Prisma.LoginLogUncheckedCreateWithoutUserInput
  >
}

export type LoginLogCreateManyUserInputEnvelope = {
  data: Prisma.LoginLogCreateManyUserInput | Prisma.LoginLogCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type LoginLogUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.LoginLogWhereUniqueInput
  update: Prisma.XOR<
    Prisma.LoginLogUpdateWithoutUserInput,
    Prisma.LoginLogUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.LoginLogCreateWithoutUserInput,
    Prisma.LoginLogUncheckedCreateWithoutUserInput
  >
}

export type LoginLogUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.LoginLogWhereUniqueInput
  data: Prisma.XOR<
    Prisma.LoginLogUpdateWithoutUserInput,
    Prisma.LoginLogUncheckedUpdateWithoutUserInput
  >
}

export type LoginLogUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.LoginLogScalarWhereInput
  data: Prisma.XOR<
    Prisma.LoginLogUpdateManyMutationInput,
    Prisma.LoginLogUncheckedUpdateManyWithoutUserInput
  >
}

export type LoginLogScalarWhereInput = {
  AND?: Prisma.LoginLogScalarWhereInput | Prisma.LoginLogScalarWhereInput[]
  OR?: Prisma.LoginLogScalarWhereInput[]
  NOT?: Prisma.LoginLogScalarWhereInput | Prisma.LoginLogScalarWhereInput[]
  id?: Prisma.StringFilter<'LoginLog'> | string
  userId?: Prisma.StringFilter<'LoginLog'> | string
  ipAddress?: Prisma.StringFilter<'LoginLog'> | string
  userAgent?: Prisma.StringNullableFilter<'LoginLog'> | string | null
  loginAt?: Prisma.DateTimeFilter<'LoginLog'> | Date | string
  logoutAt?: Prisma.DateTimeNullableFilter<'LoginLog'> | Date | string | null
}

export type LoginLogCreateManyUserInput = {
  id?: string
  ipAddress: string
  userAgent?: string | null
  loginAt?: Date | string
  logoutAt?: Date | string | null
}

export type LoginLogUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.StringFieldUpdateOperationsInput | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  loginAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  logoutAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LoginLogUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.StringFieldUpdateOperationsInput | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  loginAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  logoutAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LoginLogUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ipAddress?: Prisma.StringFieldUpdateOperationsInput | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  loginAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  logoutAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LoginLogSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    loginAt?: boolean
    logoutAt?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['loginLog']
>

export type LoginLogSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    loginAt?: boolean
    logoutAt?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['loginLog']
>

export type LoginLogSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    loginAt?: boolean
    logoutAt?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['loginLog']
>

export type LoginLogSelectScalar = {
  id?: boolean
  userId?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  loginAt?: boolean
  logoutAt?: boolean
}

export type LoginLogOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'userId' | 'ipAddress' | 'userAgent' | 'loginAt' | 'logoutAt',
  ExtArgs['result']['loginLog']
>
export type LoginLogInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type LoginLogIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type LoginLogIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $LoginLogPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'LoginLog'
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      userId: string
      ipAddress: string
      userAgent: string | null
      loginAt: Date
      logoutAt: Date | null
    },
    ExtArgs['result']['loginLog']
  >
  composites: {}
}

export type LoginLogGetPayload<S extends boolean | null | undefined | LoginLogDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$LoginLogPayload, S>

export type LoginLogCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<LoginLogFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: LoginLogCountAggregateInputType | true
}

export interface LoginLogDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['LoginLog']; meta: { name: 'LoginLog' } }
  /**
   * Find zero or one LoginLog that matches the filter.
   * @param {LoginLogFindUniqueArgs} args - Arguments to find a LoginLog
   * @example
   * // Get one LoginLog
   * const loginLog = await prisma.loginLog.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends LoginLogFindUniqueArgs>(
    args: Prisma.SelectSubset<T, LoginLogFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one LoginLog that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {LoginLogFindUniqueOrThrowArgs} args - Arguments to find a LoginLog
   * @example
   * // Get one LoginLog
   * const loginLog = await prisma.loginLog.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends LoginLogFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, LoginLogFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first LoginLog that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LoginLogFindFirstArgs} args - Arguments to find a LoginLog
   * @example
   * // Get one LoginLog
   * const loginLog = await prisma.loginLog.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends LoginLogFindFirstArgs>(
    args?: Prisma.SelectSubset<T, LoginLogFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first LoginLog that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LoginLogFindFirstOrThrowArgs} args - Arguments to find a LoginLog
   * @example
   * // Get one LoginLog
   * const loginLog = await prisma.loginLog.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends LoginLogFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, LoginLogFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more LoginLogs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LoginLogFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all LoginLogs
   * const loginLogs = await prisma.loginLog.findMany()
   *
   * // Get first 10 LoginLogs
   * const loginLogs = await prisma.loginLog.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const loginLogWithIdOnly = await prisma.loginLog.findMany({ select: { id: true } })
   *
   */
  findMany<T extends LoginLogFindManyArgs>(
    args?: Prisma.SelectSubset<T, LoginLogFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a LoginLog.
   * @param {LoginLogCreateArgs} args - Arguments to create a LoginLog.
   * @example
   * // Create one LoginLog
   * const LoginLog = await prisma.loginLog.create({
   *   data: {
   *     // ... data to create a LoginLog
   *   }
   * })
   *
   */
  create<T extends LoginLogCreateArgs>(
    args: Prisma.SelectSubset<T, LoginLogCreateArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many LoginLogs.
   * @param {LoginLogCreateManyArgs} args - Arguments to create many LoginLogs.
   * @example
   * // Create many LoginLogs
   * const loginLog = await prisma.loginLog.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends LoginLogCreateManyArgs>(
    args?: Prisma.SelectSubset<T, LoginLogCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many LoginLogs and returns the data saved in the database.
   * @param {LoginLogCreateManyAndReturnArgs} args - Arguments to create many LoginLogs.
   * @example
   * // Create many LoginLogs
   * const loginLog = await prisma.loginLog.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many LoginLogs and only return the `id`
   * const loginLogWithIdOnly = await prisma.loginLog.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends LoginLogCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, LoginLogCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a LoginLog.
   * @param {LoginLogDeleteArgs} args - Arguments to delete one LoginLog.
   * @example
   * // Delete one LoginLog
   * const LoginLog = await prisma.loginLog.delete({
   *   where: {
   *     // ... filter to delete one LoginLog
   *   }
   * })
   *
   */
  delete<T extends LoginLogDeleteArgs>(
    args: Prisma.SelectSubset<T, LoginLogDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one LoginLog.
   * @param {LoginLogUpdateArgs} args - Arguments to update one LoginLog.
   * @example
   * // Update one LoginLog
   * const loginLog = await prisma.loginLog.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends LoginLogUpdateArgs>(
    args: Prisma.SelectSubset<T, LoginLogUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more LoginLogs.
   * @param {LoginLogDeleteManyArgs} args - Arguments to filter LoginLogs to delete.
   * @example
   * // Delete a few LoginLogs
   * const { count } = await prisma.loginLog.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends LoginLogDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, LoginLogDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more LoginLogs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LoginLogUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many LoginLogs
   * const loginLog = await prisma.loginLog.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends LoginLogUpdateManyArgs>(
    args: Prisma.SelectSubset<T, LoginLogUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more LoginLogs and returns the data updated in the database.
   * @param {LoginLogUpdateManyAndReturnArgs} args - Arguments to update many LoginLogs.
   * @example
   * // Update many LoginLogs
   * const loginLog = await prisma.loginLog.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more LoginLogs and only return the `id`
   * const loginLogWithIdOnly = await prisma.loginLog.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends LoginLogUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, LoginLogUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one LoginLog.
   * @param {LoginLogUpsertArgs} args - Arguments to update or create a LoginLog.
   * @example
   * // Update or create a LoginLog
   * const loginLog = await prisma.loginLog.upsert({
   *   create: {
   *     // ... data to create a LoginLog
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the LoginLog we want to update
   *   }
   * })
   */
  upsert<T extends LoginLogUpsertArgs>(
    args: Prisma.SelectSubset<T, LoginLogUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__LoginLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$LoginLogPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of LoginLogs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LoginLogCountArgs} args - Arguments to filter LoginLogs to count.
   * @example
   * // Count the number of LoginLogs
   * const count = await prisma.loginLog.count({
   *   where: {
   *     // ... the filter for the LoginLogs we want to count
   *   }
   * })
   **/
  count<T extends LoginLogCountArgs>(
    args?: Prisma.Subset<T, LoginLogCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], LoginLogCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a LoginLog.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LoginLogAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends LoginLogAggregateArgs>(
    args: Prisma.Subset<T, LoginLogAggregateArgs>
  ): Prisma.PrismaPromise<GetLoginLogAggregateType<T>>

  /**
   * Group by LoginLog.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LoginLogGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends LoginLogGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: LoginLogGroupByArgs['orderBy'] }
      : { orderBy?: LoginLogGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, LoginLogGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetLoginLogGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the LoginLog model
   */
  readonly fields: LoginLogFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for LoginLog.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__LoginLogClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the LoginLog model
 */
export interface LoginLogFieldRefs {
  readonly id: Prisma.FieldRef<'LoginLog', 'String'>
  readonly userId: Prisma.FieldRef<'LoginLog', 'String'>
  readonly ipAddress: Prisma.FieldRef<'LoginLog', 'String'>
  readonly userAgent: Prisma.FieldRef<'LoginLog', 'String'>
  readonly loginAt: Prisma.FieldRef<'LoginLog', 'DateTime'>
  readonly logoutAt: Prisma.FieldRef<'LoginLog', 'DateTime'>
}

// Custom InputTypes
/**
 * LoginLog findUnique
 */
export type LoginLogFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * Filter, which LoginLog to fetch.
   */
  where: Prisma.LoginLogWhereUniqueInput
}

/**
 * LoginLog findUniqueOrThrow
 */
export type LoginLogFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * Filter, which LoginLog to fetch.
   */
  where: Prisma.LoginLogWhereUniqueInput
}

/**
 * LoginLog findFirst
 */
export type LoginLogFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * Filter, which LoginLog to fetch.
   */
  where?: Prisma.LoginLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of LoginLogs to fetch.
   */
  orderBy?: Prisma.LoginLogOrderByWithRelationInput | Prisma.LoginLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for LoginLogs.
   */
  cursor?: Prisma.LoginLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` LoginLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` LoginLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of LoginLogs.
   */
  distinct?: Prisma.LoginLogScalarFieldEnum | Prisma.LoginLogScalarFieldEnum[]
}

/**
 * LoginLog findFirstOrThrow
 */
export type LoginLogFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * Filter, which LoginLog to fetch.
   */
  where?: Prisma.LoginLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of LoginLogs to fetch.
   */
  orderBy?: Prisma.LoginLogOrderByWithRelationInput | Prisma.LoginLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for LoginLogs.
   */
  cursor?: Prisma.LoginLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` LoginLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` LoginLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of LoginLogs.
   */
  distinct?: Prisma.LoginLogScalarFieldEnum | Prisma.LoginLogScalarFieldEnum[]
}

/**
 * LoginLog findMany
 */
export type LoginLogFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * Filter, which LoginLogs to fetch.
   */
  where?: Prisma.LoginLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of LoginLogs to fetch.
   */
  orderBy?: Prisma.LoginLogOrderByWithRelationInput | Prisma.LoginLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing LoginLogs.
   */
  cursor?: Prisma.LoginLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` LoginLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` LoginLogs.
   */
  skip?: number
  distinct?: Prisma.LoginLogScalarFieldEnum | Prisma.LoginLogScalarFieldEnum[]
}

/**
 * LoginLog create
 */
export type LoginLogCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * The data needed to create a LoginLog.
   */
  data: Prisma.XOR<Prisma.LoginLogCreateInput, Prisma.LoginLogUncheckedCreateInput>
}

/**
 * LoginLog createMany
 */
export type LoginLogCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many LoginLogs.
   */
  data: Prisma.LoginLogCreateManyInput | Prisma.LoginLogCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * LoginLog createManyAndReturn
 */
export type LoginLogCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * The data used to create many LoginLogs.
   */
  data: Prisma.LoginLogCreateManyInput | Prisma.LoginLogCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * LoginLog update
 */
export type LoginLogUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * The data needed to update a LoginLog.
   */
  data: Prisma.XOR<Prisma.LoginLogUpdateInput, Prisma.LoginLogUncheckedUpdateInput>
  /**
   * Choose, which LoginLog to update.
   */
  where: Prisma.LoginLogWhereUniqueInput
}

/**
 * LoginLog updateMany
 */
export type LoginLogUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update LoginLogs.
   */
  data: Prisma.XOR<Prisma.LoginLogUpdateManyMutationInput, Prisma.LoginLogUncheckedUpdateManyInput>
  /**
   * Filter which LoginLogs to update
   */
  where?: Prisma.LoginLogWhereInput
  /**
   * Limit how many LoginLogs to update.
   */
  limit?: number
}

/**
 * LoginLog updateManyAndReturn
 */
export type LoginLogUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * The data used to update LoginLogs.
   */
  data: Prisma.XOR<Prisma.LoginLogUpdateManyMutationInput, Prisma.LoginLogUncheckedUpdateManyInput>
  /**
   * Filter which LoginLogs to update
   */
  where?: Prisma.LoginLogWhereInput
  /**
   * Limit how many LoginLogs to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * LoginLog upsert
 */
export type LoginLogUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * The filter to search for the LoginLog to update in case it exists.
   */
  where: Prisma.LoginLogWhereUniqueInput
  /**
   * In case the LoginLog found by the `where` argument doesn't exist, create a new LoginLog with this data.
   */
  create: Prisma.XOR<Prisma.LoginLogCreateInput, Prisma.LoginLogUncheckedCreateInput>
  /**
   * In case the LoginLog was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.LoginLogUpdateInput, Prisma.LoginLogUncheckedUpdateInput>
}

/**
 * LoginLog delete
 */
export type LoginLogDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  /**
   * Filter which LoginLog to delete.
   */
  where: Prisma.LoginLogWhereUniqueInput
}

/**
 * LoginLog deleteMany
 */
export type LoginLogDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which LoginLogs to delete
   */
  where?: Prisma.LoginLogWhereInput
  /**
   * Limit how many LoginLogs to delete.
   */
  limit?: number
}

/**
 * LoginLog without action
 */
export type LoginLogDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
}
