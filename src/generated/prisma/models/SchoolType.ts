/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `SchoolType` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model SchoolType
 *
 */
export type SchoolTypeModel = runtime.Types.Result.DefaultSelection<Prisma.$SchoolTypePayload>

export type AggregateSchoolType = {
  _count: SchoolTypeCountAggregateOutputType | null
  _avg: SchoolTypeAvgAggregateOutputType | null
  _sum: SchoolTypeSumAggregateOutputType | null
  _min: SchoolTypeMinAggregateOutputType | null
  _max: SchoolTypeMaxAggregateOutputType | null
}

export type SchoolTypeAvgAggregateOutputType = {
  id: number | null
}

export type SchoolTypeSumAggregateOutputType = {
  id: number | null
}

export type SchoolTypeMinAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  level: $Enums.SchoolLevel | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type SchoolTypeMaxAggregateOutputType = {
  id: number | null
  name: string | null
  description: string | null
  level: $Enums.SchoolLevel | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type SchoolTypeCountAggregateOutputType = {
  id: number
  name: number
  description: number
  level: number
  createdAt: number
  updatedAt: number
  _all: number
}

export type SchoolTypeAvgAggregateInputType = {
  id?: true
}

export type SchoolTypeSumAggregateInputType = {
  id?: true
}

export type SchoolTypeMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  level?: true
  createdAt?: true
  updatedAt?: true
}

export type SchoolTypeMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  level?: true
  createdAt?: true
  updatedAt?: true
}

export type SchoolTypeCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  level?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type SchoolTypeAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SchoolType to aggregate.
   */
  where?: Prisma.SchoolTypeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolTypes to fetch.
   */
  orderBy?: Prisma.SchoolTypeOrderByWithRelationInput | Prisma.SchoolTypeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.SchoolTypeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolTypes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolTypes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned SchoolTypes
   **/
  _count?: true | SchoolTypeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to average
   **/
  _avg?: SchoolTypeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to sum
   **/
  _sum?: SchoolTypeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: SchoolTypeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: SchoolTypeMaxAggregateInputType
}

export type GetSchoolTypeAggregateType<T extends SchoolTypeAggregateArgs> = {
  [P in keyof T & keyof AggregateSchoolType]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSchoolType[P]>
    : Prisma.GetScalarType<T[P], AggregateSchoolType[P]>
}

export type SchoolTypeGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SchoolTypeWhereInput
  orderBy?:
    | Prisma.SchoolTypeOrderByWithAggregationInput
    | Prisma.SchoolTypeOrderByWithAggregationInput[]
  by: Prisma.SchoolTypeScalarFieldEnum[] | Prisma.SchoolTypeScalarFieldEnum
  having?: Prisma.SchoolTypeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SchoolTypeCountAggregateInputType | true
  _avg?: SchoolTypeAvgAggregateInputType
  _sum?: SchoolTypeSumAggregateInputType
  _min?: SchoolTypeMinAggregateInputType
  _max?: SchoolTypeMaxAggregateInputType
}

export type SchoolTypeGroupByOutputType = {
  id: number
  name: string
  description: string | null
  level: $Enums.SchoolLevel
  createdAt: Date
  updatedAt: Date
  _count: SchoolTypeCountAggregateOutputType | null
  _avg: SchoolTypeAvgAggregateOutputType | null
  _sum: SchoolTypeSumAggregateOutputType | null
  _min: SchoolTypeMinAggregateOutputType | null
  _max: SchoolTypeMaxAggregateOutputType | null
}

type GetSchoolTypeGroupByPayload<T extends SchoolTypeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SchoolTypeGroupByOutputType, T['by']> & {
      [P in keyof T & keyof SchoolTypeGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], SchoolTypeGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], SchoolTypeGroupByOutputType[P]>
    }
  >
>

export type SchoolTypeWhereInput = {
  AND?: Prisma.SchoolTypeWhereInput | Prisma.SchoolTypeWhereInput[]
  OR?: Prisma.SchoolTypeWhereInput[]
  NOT?: Prisma.SchoolTypeWhereInput | Prisma.SchoolTypeWhereInput[]
  id?: Prisma.IntFilter<'SchoolType'> | number
  name?: Prisma.StringFilter<'SchoolType'> | string
  description?: Prisma.StringNullableFilter<'SchoolType'> | string | null
  level?: Prisma.EnumSchoolLevelFilter<'SchoolType'> | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeFilter<'SchoolType'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'SchoolType'> | Date | string
  schools?: Prisma.SchoolListRelationFilter
}

export type SchoolTypeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  schools?: Prisma.SchoolOrderByRelationAggregateInput
}

export type SchoolTypeWhereUniqueInput = Prisma.AtLeast<
  {
    id?: number
    AND?: Prisma.SchoolTypeWhereInput | Prisma.SchoolTypeWhereInput[]
    OR?: Prisma.SchoolTypeWhereInput[]
    NOT?: Prisma.SchoolTypeWhereInput | Prisma.SchoolTypeWhereInput[]
    name?: Prisma.StringFilter<'SchoolType'> | string
    description?: Prisma.StringNullableFilter<'SchoolType'> | string | null
    level?: Prisma.EnumSchoolLevelFilter<'SchoolType'> | $Enums.SchoolLevel
    createdAt?: Prisma.DateTimeFilter<'SchoolType'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'SchoolType'> | Date | string
    schools?: Prisma.SchoolListRelationFilter
  },
  'id'
>

export type SchoolTypeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.SchoolTypeCountOrderByAggregateInput
  _avg?: Prisma.SchoolTypeAvgOrderByAggregateInput
  _max?: Prisma.SchoolTypeMaxOrderByAggregateInput
  _min?: Prisma.SchoolTypeMinOrderByAggregateInput
  _sum?: Prisma.SchoolTypeSumOrderByAggregateInput
}

export type SchoolTypeScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.SchoolTypeScalarWhereWithAggregatesInput
    | Prisma.SchoolTypeScalarWhereWithAggregatesInput[]
  OR?: Prisma.SchoolTypeScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.SchoolTypeScalarWhereWithAggregatesInput
    | Prisma.SchoolTypeScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<'SchoolType'> | number
  name?: Prisma.StringWithAggregatesFilter<'SchoolType'> | string
  description?: Prisma.StringNullableWithAggregatesFilter<'SchoolType'> | string | null
  level?: Prisma.EnumSchoolLevelWithAggregatesFilter<'SchoolType'> | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'SchoolType'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'SchoolType'> | Date | string
}

export type SchoolTypeCreateInput = {
  name: string
  description?: string | null
  level: $Enums.SchoolLevel
  createdAt?: Date | string
  updatedAt?: Date | string
  schools?: Prisma.SchoolCreateNestedManyWithoutTypeInput
}

export type SchoolTypeUncheckedCreateInput = {
  id?: number
  name: string
  description?: string | null
  level: $Enums.SchoolLevel
  createdAt?: Date | string
  updatedAt?: Date | string
  schools?: Prisma.SchoolUncheckedCreateNestedManyWithoutTypeInput
}

export type SchoolTypeUpdateInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  level?: Prisma.EnumSchoolLevelFieldUpdateOperationsInput | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schools?: Prisma.SchoolUpdateManyWithoutTypeNestedInput
}

export type SchoolTypeUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  level?: Prisma.EnumSchoolLevelFieldUpdateOperationsInput | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schools?: Prisma.SchoolUncheckedUpdateManyWithoutTypeNestedInput
}

export type SchoolTypeCreateManyInput = {
  id?: number
  name: string
  description?: string | null
  level: $Enums.SchoolLevel
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SchoolTypeUpdateManyMutationInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  level?: Prisma.EnumSchoolLevelFieldUpdateOperationsInput | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SchoolTypeUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  level?: Prisma.EnumSchoolLevelFieldUpdateOperationsInput | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SchoolTypeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type SchoolTypeAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type SchoolTypeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type SchoolTypeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  level?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type SchoolTypeSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type SchoolTypeScalarRelationFilter = {
  is?: Prisma.SchoolTypeWhereInput
  isNot?: Prisma.SchoolTypeWhereInput
}

export type EnumSchoolLevelFieldUpdateOperationsInput = {
  set?: $Enums.SchoolLevel
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type SchoolTypeCreateNestedOneWithoutSchoolsInput = {
  create?: Prisma.XOR<
    Prisma.SchoolTypeCreateWithoutSchoolsInput,
    Prisma.SchoolTypeUncheckedCreateWithoutSchoolsInput
  >
  connectOrCreate?: Prisma.SchoolTypeCreateOrConnectWithoutSchoolsInput
  connect?: Prisma.SchoolTypeWhereUniqueInput
}

export type SchoolTypeUpdateOneRequiredWithoutSchoolsNestedInput = {
  create?: Prisma.XOR<
    Prisma.SchoolTypeCreateWithoutSchoolsInput,
    Prisma.SchoolTypeUncheckedCreateWithoutSchoolsInput
  >
  connectOrCreate?: Prisma.SchoolTypeCreateOrConnectWithoutSchoolsInput
  upsert?: Prisma.SchoolTypeUpsertWithoutSchoolsInput
  connect?: Prisma.SchoolTypeWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.SchoolTypeUpdateToOneWithWhereWithoutSchoolsInput,
      Prisma.SchoolTypeUpdateWithoutSchoolsInput
    >,
    Prisma.SchoolTypeUncheckedUpdateWithoutSchoolsInput
  >
}

export type SchoolTypeCreateWithoutSchoolsInput = {
  name: string
  description?: string | null
  level: $Enums.SchoolLevel
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SchoolTypeUncheckedCreateWithoutSchoolsInput = {
  id?: number
  name: string
  description?: string | null
  level: $Enums.SchoolLevel
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SchoolTypeCreateOrConnectWithoutSchoolsInput = {
  where: Prisma.SchoolTypeWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SchoolTypeCreateWithoutSchoolsInput,
    Prisma.SchoolTypeUncheckedCreateWithoutSchoolsInput
  >
}

export type SchoolTypeUpsertWithoutSchoolsInput = {
  update: Prisma.XOR<
    Prisma.SchoolTypeUpdateWithoutSchoolsInput,
    Prisma.SchoolTypeUncheckedUpdateWithoutSchoolsInput
  >
  create: Prisma.XOR<
    Prisma.SchoolTypeCreateWithoutSchoolsInput,
    Prisma.SchoolTypeUncheckedCreateWithoutSchoolsInput
  >
  where?: Prisma.SchoolTypeWhereInput
}

export type SchoolTypeUpdateToOneWithWhereWithoutSchoolsInput = {
  where?: Prisma.SchoolTypeWhereInput
  data: Prisma.XOR<
    Prisma.SchoolTypeUpdateWithoutSchoolsInput,
    Prisma.SchoolTypeUncheckedUpdateWithoutSchoolsInput
  >
}

export type SchoolTypeUpdateWithoutSchoolsInput = {
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  level?: Prisma.EnumSchoolLevelFieldUpdateOperationsInput | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SchoolTypeUncheckedUpdateWithoutSchoolsInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  level?: Prisma.EnumSchoolLevelFieldUpdateOperationsInput | $Enums.SchoolLevel
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

/**
 * Count Type SchoolTypeCountOutputType
 */

export type SchoolTypeCountOutputType = {
  schools: number
}

export type SchoolTypeCountOutputTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  schools?: boolean | SchoolTypeCountOutputTypeCountSchoolsArgs
}

/**
 * SchoolTypeCountOutputType without action
 */
export type SchoolTypeCountOutputTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolTypeCountOutputType
   */
  select?: Prisma.SchoolTypeCountOutputTypeSelect<ExtArgs> | null
}

/**
 * SchoolTypeCountOutputType without action
 */
export type SchoolTypeCountOutputTypeCountSchoolsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SchoolWhereInput
}

export type SchoolTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    level?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    schools?: boolean | Prisma.SchoolType$schoolsArgs<ExtArgs>
    _count?: boolean | Prisma.SchoolTypeCountOutputTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['schoolType']
>

export type SchoolTypeSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    level?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  },
  ExtArgs['result']['schoolType']
>

export type SchoolTypeSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    level?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  },
  ExtArgs['result']['schoolType']
>

export type SchoolTypeSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  level?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type SchoolTypeOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'name' | 'description' | 'level' | 'createdAt' | 'updatedAt',
  ExtArgs['result']['schoolType']
>
export type SchoolTypeInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  schools?: boolean | Prisma.SchoolType$schoolsArgs<ExtArgs>
  _count?: boolean | Prisma.SchoolTypeCountOutputTypeDefaultArgs<ExtArgs>
}
export type SchoolTypeIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {}
export type SchoolTypeIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {}

export type $SchoolTypePayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'SchoolType'
  objects: {
    schools: Prisma.$SchoolPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: number
      name: string
      description: string | null
      level: $Enums.SchoolLevel
      createdAt: Date
      updatedAt: Date
    },
    ExtArgs['result']['schoolType']
  >
  composites: {}
}

export type SchoolTypeGetPayload<S extends boolean | null | undefined | SchoolTypeDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$SchoolTypePayload, S>

export type SchoolTypeCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<SchoolTypeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: SchoolTypeCountAggregateInputType | true
}

export interface SchoolTypeDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['SchoolType']
    meta: { name: 'SchoolType' }
  }
  /**
   * Find zero or one SchoolType that matches the filter.
   * @param {SchoolTypeFindUniqueArgs} args - Arguments to find a SchoolType
   * @example
   * // Get one SchoolType
   * const schoolType = await prisma.schoolType.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SchoolTypeFindUniqueArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one SchoolType that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SchoolTypeFindUniqueOrThrowArgs} args - Arguments to find a SchoolType
   * @example
   * // Get one SchoolType
   * const schoolType = await prisma.schoolType.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SchoolTypeFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SchoolType that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolTypeFindFirstArgs} args - Arguments to find a SchoolType
   * @example
   * // Get one SchoolType
   * const schoolType = await prisma.schoolType.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SchoolTypeFindFirstArgs>(
    args?: Prisma.SelectSubset<T, SchoolTypeFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SchoolType that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolTypeFindFirstOrThrowArgs} args - Arguments to find a SchoolType
   * @example
   * // Get one SchoolType
   * const schoolType = await prisma.schoolType.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SchoolTypeFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, SchoolTypeFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more SchoolTypes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolTypeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all SchoolTypes
   * const schoolTypes = await prisma.schoolType.findMany()
   *
   * // Get first 10 SchoolTypes
   * const schoolTypes = await prisma.schoolType.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const schoolTypeWithIdOnly = await prisma.schoolType.findMany({ select: { id: true } })
   *
   */
  findMany<T extends SchoolTypeFindManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolTypeFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a SchoolType.
   * @param {SchoolTypeCreateArgs} args - Arguments to create a SchoolType.
   * @example
   * // Create one SchoolType
   * const SchoolType = await prisma.schoolType.create({
   *   data: {
   *     // ... data to create a SchoolType
   *   }
   * })
   *
   */
  create<T extends SchoolTypeCreateArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeCreateArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many SchoolTypes.
   * @param {SchoolTypeCreateManyArgs} args - Arguments to create many SchoolTypes.
   * @example
   * // Create many SchoolTypes
   * const schoolType = await prisma.schoolType.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends SchoolTypeCreateManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolTypeCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many SchoolTypes and returns the data saved in the database.
   * @param {SchoolTypeCreateManyAndReturnArgs} args - Arguments to create many SchoolTypes.
   * @example
   * // Create many SchoolTypes
   * const schoolType = await prisma.schoolType.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many SchoolTypes and only return the `id`
   * const schoolTypeWithIdOnly = await prisma.schoolType.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends SchoolTypeCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, SchoolTypeCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a SchoolType.
   * @param {SchoolTypeDeleteArgs} args - Arguments to delete one SchoolType.
   * @example
   * // Delete one SchoolType
   * const SchoolType = await prisma.schoolType.delete({
   *   where: {
   *     // ... filter to delete one SchoolType
   *   }
   * })
   *
   */
  delete<T extends SchoolTypeDeleteArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one SchoolType.
   * @param {SchoolTypeUpdateArgs} args - Arguments to update one SchoolType.
   * @example
   * // Update one SchoolType
   * const schoolType = await prisma.schoolType.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends SchoolTypeUpdateArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more SchoolTypes.
   * @param {SchoolTypeDeleteManyArgs} args - Arguments to filter SchoolTypes to delete.
   * @example
   * // Delete a few SchoolTypes
   * const { count } = await prisma.schoolType.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends SchoolTypeDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, SchoolTypeDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SchoolTypes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolTypeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many SchoolTypes
   * const schoolType = await prisma.schoolType.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends SchoolTypeUpdateManyArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SchoolTypes and returns the data updated in the database.
   * @param {SchoolTypeUpdateManyAndReturnArgs} args - Arguments to update many SchoolTypes.
   * @example
   * // Update many SchoolTypes
   * const schoolType = await prisma.schoolType.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more SchoolTypes and only return the `id`
   * const schoolTypeWithIdOnly = await prisma.schoolType.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends SchoolTypeUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one SchoolType.
   * @param {SchoolTypeUpsertArgs} args - Arguments to update or create a SchoolType.
   * @example
   * // Update or create a SchoolType
   * const schoolType = await prisma.schoolType.upsert({
   *   create: {
   *     // ... data to create a SchoolType
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the SchoolType we want to update
   *   }
   * })
   */
  upsert<T extends SchoolTypeUpsertArgs>(
    args: Prisma.SelectSubset<T, SchoolTypeUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolTypeClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolTypePayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of SchoolTypes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolTypeCountArgs} args - Arguments to filter SchoolTypes to count.
   * @example
   * // Count the number of SchoolTypes
   * const count = await prisma.schoolType.count({
   *   where: {
   *     // ... the filter for the SchoolTypes we want to count
   *   }
   * })
   **/
  count<T extends SchoolTypeCountArgs>(
    args?: Prisma.Subset<T, SchoolTypeCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SchoolTypeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a SchoolType.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolTypeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends SchoolTypeAggregateArgs>(
    args: Prisma.Subset<T, SchoolTypeAggregateArgs>
  ): Prisma.PrismaPromise<GetSchoolTypeAggregateType<T>>

  /**
   * Group by SchoolType.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SchoolTypeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends SchoolTypeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SchoolTypeGroupByArgs['orderBy'] }
      : { orderBy?: SchoolTypeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, SchoolTypeGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetSchoolTypeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SchoolType model
   */
  readonly fields: SchoolTypeFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for SchoolType.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SchoolTypeClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  schools<T extends Prisma.SchoolType$schoolsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.SchoolType$schoolsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SchoolPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the SchoolType model
 */
export interface SchoolTypeFieldRefs {
  readonly id: Prisma.FieldRef<'SchoolType', 'Int'>
  readonly name: Prisma.FieldRef<'SchoolType', 'String'>
  readonly description: Prisma.FieldRef<'SchoolType', 'String'>
  readonly level: Prisma.FieldRef<'SchoolType', 'SchoolLevel'>
  readonly createdAt: Prisma.FieldRef<'SchoolType', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'SchoolType', 'DateTime'>
}

// Custom InputTypes
/**
 * SchoolType findUnique
 */
export type SchoolTypeFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * Filter, which SchoolType to fetch.
   */
  where: Prisma.SchoolTypeWhereUniqueInput
}

/**
 * SchoolType findUniqueOrThrow
 */
export type SchoolTypeFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * Filter, which SchoolType to fetch.
   */
  where: Prisma.SchoolTypeWhereUniqueInput
}

/**
 * SchoolType findFirst
 */
export type SchoolTypeFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * Filter, which SchoolType to fetch.
   */
  where?: Prisma.SchoolTypeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolTypes to fetch.
   */
  orderBy?: Prisma.SchoolTypeOrderByWithRelationInput | Prisma.SchoolTypeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SchoolTypes.
   */
  cursor?: Prisma.SchoolTypeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolTypes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolTypes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SchoolTypes.
   */
  distinct?: Prisma.SchoolTypeScalarFieldEnum | Prisma.SchoolTypeScalarFieldEnum[]
}

/**
 * SchoolType findFirstOrThrow
 */
export type SchoolTypeFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * Filter, which SchoolType to fetch.
   */
  where?: Prisma.SchoolTypeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolTypes to fetch.
   */
  orderBy?: Prisma.SchoolTypeOrderByWithRelationInput | Prisma.SchoolTypeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SchoolTypes.
   */
  cursor?: Prisma.SchoolTypeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolTypes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolTypes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SchoolTypes.
   */
  distinct?: Prisma.SchoolTypeScalarFieldEnum | Prisma.SchoolTypeScalarFieldEnum[]
}

/**
 * SchoolType findMany
 */
export type SchoolTypeFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * Filter, which SchoolTypes to fetch.
   */
  where?: Prisma.SchoolTypeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SchoolTypes to fetch.
   */
  orderBy?: Prisma.SchoolTypeOrderByWithRelationInput | Prisma.SchoolTypeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing SchoolTypes.
   */
  cursor?: Prisma.SchoolTypeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SchoolTypes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SchoolTypes.
   */
  skip?: number
  distinct?: Prisma.SchoolTypeScalarFieldEnum | Prisma.SchoolTypeScalarFieldEnum[]
}

/**
 * SchoolType create
 */
export type SchoolTypeCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * The data needed to create a SchoolType.
   */
  data: Prisma.XOR<Prisma.SchoolTypeCreateInput, Prisma.SchoolTypeUncheckedCreateInput>
}

/**
 * SchoolType createMany
 */
export type SchoolTypeCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many SchoolTypes.
   */
  data: Prisma.SchoolTypeCreateManyInput | Prisma.SchoolTypeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * SchoolType createManyAndReturn
 */
export type SchoolTypeCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * The data used to create many SchoolTypes.
   */
  data: Prisma.SchoolTypeCreateManyInput | Prisma.SchoolTypeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * SchoolType update
 */
export type SchoolTypeUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * The data needed to update a SchoolType.
   */
  data: Prisma.XOR<Prisma.SchoolTypeUpdateInput, Prisma.SchoolTypeUncheckedUpdateInput>
  /**
   * Choose, which SchoolType to update.
   */
  where: Prisma.SchoolTypeWhereUniqueInput
}

/**
 * SchoolType updateMany
 */
export type SchoolTypeUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update SchoolTypes.
   */
  data: Prisma.XOR<
    Prisma.SchoolTypeUpdateManyMutationInput,
    Prisma.SchoolTypeUncheckedUpdateManyInput
  >
  /**
   * Filter which SchoolTypes to update
   */
  where?: Prisma.SchoolTypeWhereInput
  /**
   * Limit how many SchoolTypes to update.
   */
  limit?: number
}

/**
 * SchoolType updateManyAndReturn
 */
export type SchoolTypeUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * The data used to update SchoolTypes.
   */
  data: Prisma.XOR<
    Prisma.SchoolTypeUpdateManyMutationInput,
    Prisma.SchoolTypeUncheckedUpdateManyInput
  >
  /**
   * Filter which SchoolTypes to update
   */
  where?: Prisma.SchoolTypeWhereInput
  /**
   * Limit how many SchoolTypes to update.
   */
  limit?: number
}

/**
 * SchoolType upsert
 */
export type SchoolTypeUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * The filter to search for the SchoolType to update in case it exists.
   */
  where: Prisma.SchoolTypeWhereUniqueInput
  /**
   * In case the SchoolType found by the `where` argument doesn't exist, create a new SchoolType with this data.
   */
  create: Prisma.XOR<Prisma.SchoolTypeCreateInput, Prisma.SchoolTypeUncheckedCreateInput>
  /**
   * In case the SchoolType was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SchoolTypeUpdateInput, Prisma.SchoolTypeUncheckedUpdateInput>
}

/**
 * SchoolType delete
 */
export type SchoolTypeDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
  /**
   * Filter which SchoolType to delete.
   */
  where: Prisma.SchoolTypeWhereUniqueInput
}

/**
 * SchoolType deleteMany
 */
export type SchoolTypeDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SchoolTypes to delete
   */
  where?: Prisma.SchoolTypeWhereInput
  /**
   * Limit how many SchoolTypes to delete.
   */
  limit?: number
}

/**
 * SchoolType.schools
 */
export type SchoolType$schoolsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  where?: Prisma.SchoolWhereInput
  orderBy?: Prisma.SchoolOrderByWithRelationInput | Prisma.SchoolOrderByWithRelationInput[]
  cursor?: Prisma.SchoolWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SchoolScalarFieldEnum | Prisma.SchoolScalarFieldEnum[]
}

/**
 * SchoolType without action
 */
export type SchoolTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolType
   */
  select?: Prisma.SchoolTypeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolType
   */
  omit?: Prisma.SchoolTypeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolTypeInclude<ExtArgs> | null
}
