/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Token` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Token
 *
 */
export type TokenModel = runtime.Types.Result.DefaultSelection<Prisma.$TokenPayload>

export type AggregateToken = {
  _count: TokenCountAggregateOutputType | null
  _min: TokenMinAggregateOutputType | null
  _max: TokenMaxAggregateOutputType | null
}

export type TokenMinAggregateOutputType = {
  id: string | null
  token: string | null
  type: $Enums.TokenTypes | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: string | null
}

export type TokenMaxAggregateOutputType = {
  id: string | null
  token: string | null
  type: $Enums.TokenTypes | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  userId: string | null
}

export type TokenCountAggregateOutputType = {
  id: number
  token: number
  type: number
  expiresAt: number
  createdAt: number
  updatedAt: number
  userId: number
  _all: number
}

export type TokenMinAggregateInputType = {
  id?: true
  token?: true
  type?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type TokenMaxAggregateInputType = {
  id?: true
  token?: true
  type?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
}

export type TokenCountAggregateInputType = {
  id?: true
  token?: true
  type?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  userId?: true
  _all?: true
}

export type TokenAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Token to aggregate.
   */
  where?: Prisma.TokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Tokens to fetch.
   */
  orderBy?: Prisma.TokenOrderByWithRelationInput | Prisma.TokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.TokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Tokens.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Tokens
   **/
  _count?: true | TokenCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: TokenMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: TokenMaxAggregateInputType
}

export type GetTokenAggregateType<T extends TokenAggregateArgs> = {
  [P in keyof T & keyof AggregateToken]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateToken[P]>
    : Prisma.GetScalarType<T[P], AggregateToken[P]>
}

export type TokenGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.TokenWhereInput
  orderBy?: Prisma.TokenOrderByWithAggregationInput | Prisma.TokenOrderByWithAggregationInput[]
  by: Prisma.TokenScalarFieldEnum[] | Prisma.TokenScalarFieldEnum
  having?: Prisma.TokenScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TokenCountAggregateInputType | true
  _min?: TokenMinAggregateInputType
  _max?: TokenMaxAggregateInputType
}

export type TokenGroupByOutputType = {
  id: string
  token: string
  type: $Enums.TokenTypes
  expiresAt: Date
  createdAt: Date
  updatedAt: Date
  userId: string
  _count: TokenCountAggregateOutputType | null
  _min: TokenMinAggregateOutputType | null
  _max: TokenMaxAggregateOutputType | null
}

type GetTokenGroupByPayload<T extends TokenGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TokenGroupByOutputType, T['by']> & {
      [P in keyof T & keyof TokenGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], TokenGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], TokenGroupByOutputType[P]>
    }
  >
>

export type TokenWhereInput = {
  AND?: Prisma.TokenWhereInput | Prisma.TokenWhereInput[]
  OR?: Prisma.TokenWhereInput[]
  NOT?: Prisma.TokenWhereInput | Prisma.TokenWhereInput[]
  id?: Prisma.StringFilter<'Token'> | string
  token?: Prisma.StringFilter<'Token'> | string
  type?: Prisma.EnumTokenTypesFilter<'Token'> | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeFilter<'Token'> | Date | string
  createdAt?: Prisma.DateTimeFilter<'Token'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Token'> | Date | string
  userId?: Prisma.StringFilter<'Token'> | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type TokenOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  type?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type TokenWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    token?: string
    userId?: string
    userId_type?: Prisma.TokenUserIdTypeCompoundUniqueInput
    AND?: Prisma.TokenWhereInput | Prisma.TokenWhereInput[]
    OR?: Prisma.TokenWhereInput[]
    NOT?: Prisma.TokenWhereInput | Prisma.TokenWhereInput[]
    type?: Prisma.EnumTokenTypesFilter<'Token'> | $Enums.TokenTypes
    expiresAt?: Prisma.DateTimeFilter<'Token'> | Date | string
    createdAt?: Prisma.DateTimeFilter<'Token'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'Token'> | Date | string
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id' | 'token' | 'userId' | 'userId_type'
>

export type TokenOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  type?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.TokenCountOrderByAggregateInput
  _max?: Prisma.TokenMaxOrderByAggregateInput
  _min?: Prisma.TokenMinOrderByAggregateInput
}

export type TokenScalarWhereWithAggregatesInput = {
  AND?: Prisma.TokenScalarWhereWithAggregatesInput | Prisma.TokenScalarWhereWithAggregatesInput[]
  OR?: Prisma.TokenScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TokenScalarWhereWithAggregatesInput | Prisma.TokenScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Token'> | string
  token?: Prisma.StringWithAggregatesFilter<'Token'> | string
  type?: Prisma.EnumTokenTypesWithAggregatesFilter<'Token'> | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<'Token'> | Date | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'Token'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'Token'> | Date | string
  userId?: Prisma.StringWithAggregatesFilter<'Token'> | string
}

export type TokenCreateInput = {
  id?: string
  token: string
  type: $Enums.TokenTypes
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutTokenInput
}

export type TokenUncheckedCreateInput = {
  id?: string
  token: string
  type: $Enums.TokenTypes
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: string
}

export type TokenUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumTokenTypesFieldUpdateOperationsInput | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutTokenNestedInput
}

export type TokenUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumTokenTypesFieldUpdateOperationsInput | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TokenCreateManyInput = {
  id?: string
  token: string
  type: $Enums.TokenTypes
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: string
}

export type TokenUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumTokenTypesFieldUpdateOperationsInput | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TokenUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumTokenTypesFieldUpdateOperationsInput | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TokenUserIdTypeCompoundUniqueInput = {
  userId: string
  type: $Enums.TokenTypes
}

export type TokenCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  type?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type TokenMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  type?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type TokenMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  token?: Prisma.SortOrder
  type?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type TokenNullableScalarRelationFilter = {
  is?: Prisma.TokenWhereInput | null
  isNot?: Prisma.TokenWhereInput | null
}

export type EnumTokenTypesFieldUpdateOperationsInput = {
  set?: $Enums.TokenTypes
}

export type TokenCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.TokenCreateWithoutUserInput,
    Prisma.TokenUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TokenCreateOrConnectWithoutUserInput
  connect?: Prisma.TokenWhereUniqueInput
}

export type TokenUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.TokenCreateWithoutUserInput,
    Prisma.TokenUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TokenCreateOrConnectWithoutUserInput
  connect?: Prisma.TokenWhereUniqueInput
}

export type TokenUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.TokenCreateWithoutUserInput,
    Prisma.TokenUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TokenCreateOrConnectWithoutUserInput
  upsert?: Prisma.TokenUpsertWithoutUserInput
  disconnect?: Prisma.TokenWhereInput | boolean
  delete?: Prisma.TokenWhereInput | boolean
  connect?: Prisma.TokenWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.TokenUpdateToOneWithWhereWithoutUserInput,
      Prisma.TokenUpdateWithoutUserInput
    >,
    Prisma.TokenUncheckedUpdateWithoutUserInput
  >
}

export type TokenUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.TokenCreateWithoutUserInput,
    Prisma.TokenUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TokenCreateOrConnectWithoutUserInput
  upsert?: Prisma.TokenUpsertWithoutUserInput
  disconnect?: Prisma.TokenWhereInput | boolean
  delete?: Prisma.TokenWhereInput | boolean
  connect?: Prisma.TokenWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.TokenUpdateToOneWithWhereWithoutUserInput,
      Prisma.TokenUpdateWithoutUserInput
    >,
    Prisma.TokenUncheckedUpdateWithoutUserInput
  >
}

export type TokenCreateWithoutUserInput = {
  id?: string
  token: string
  type: $Enums.TokenTypes
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TokenUncheckedCreateWithoutUserInput = {
  id?: string
  token: string
  type: $Enums.TokenTypes
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TokenCreateOrConnectWithoutUserInput = {
  where: Prisma.TokenWhereUniqueInput
  create: Prisma.XOR<
    Prisma.TokenCreateWithoutUserInput,
    Prisma.TokenUncheckedCreateWithoutUserInput
  >
}

export type TokenUpsertWithoutUserInput = {
  update: Prisma.XOR<
    Prisma.TokenUpdateWithoutUserInput,
    Prisma.TokenUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.TokenCreateWithoutUserInput,
    Prisma.TokenUncheckedCreateWithoutUserInput
  >
  where?: Prisma.TokenWhereInput
}

export type TokenUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.TokenWhereInput
  data: Prisma.XOR<Prisma.TokenUpdateWithoutUserInput, Prisma.TokenUncheckedUpdateWithoutUserInput>
}

export type TokenUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumTokenTypesFieldUpdateOperationsInput | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TokenUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumTokenTypesFieldUpdateOperationsInput | $Enums.TokenTypes
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TokenSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    token?: boolean
    type?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['token']
>

export type TokenSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    token?: boolean
    type?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['token']
>

export type TokenSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    token?: boolean
    type?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['token']
>

export type TokenSelectScalar = {
  id?: boolean
  token?: boolean
  type?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  userId?: boolean
}

export type TokenOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'token' | 'type' | 'expiresAt' | 'createdAt' | 'updatedAt' | 'userId',
  ExtArgs['result']['token']
>
export type TokenInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type TokenIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type TokenIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $TokenPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Token'
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      token: string
      type: $Enums.TokenTypes
      expiresAt: Date
      createdAt: Date
      updatedAt: Date
      userId: string
    },
    ExtArgs['result']['token']
  >
  composites: {}
}

export type TokenGetPayload<S extends boolean | null | undefined | TokenDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$TokenPayload, S>

export type TokenCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<TokenFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: TokenCountAggregateInputType | true
}

export interface TokenDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Token']; meta: { name: 'Token' } }
  /**
   * Find zero or one Token that matches the filter.
   * @param {TokenFindUniqueArgs} args - Arguments to find a Token
   * @example
   * // Get one Token
   * const token = await prisma.token.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TokenFindUniqueArgs>(
    args: Prisma.SelectSubset<T, TokenFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<
      Prisma.$TokenPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Token that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TokenFindUniqueOrThrowArgs} args - Arguments to find a Token
   * @example
   * // Get one Token
   * const token = await prisma.token.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TokenFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, TokenFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<
      Prisma.$TokenPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Token that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TokenFindFirstArgs} args - Arguments to find a Token
   * @example
   * // Get one Token
   * const token = await prisma.token.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TokenFindFirstArgs>(
    args?: Prisma.SelectSubset<T, TokenFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<
      Prisma.$TokenPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Token that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TokenFindFirstOrThrowArgs} args - Arguments to find a Token
   * @example
   * // Get one Token
   * const token = await prisma.token.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TokenFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, TokenFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<
      Prisma.$TokenPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Tokens that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TokenFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Tokens
   * const tokens = await prisma.token.findMany()
   *
   * // Get first 10 Tokens
   * const tokens = await prisma.token.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const tokenWithIdOnly = await prisma.token.findMany({ select: { id: true } })
   *
   */
  findMany<T extends TokenFindManyArgs>(
    args?: Prisma.SelectSubset<T, TokenFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<Prisma.$TokenPayload<ExtArgs>, T, 'findMany', GlobalOmitOptions>
  >

  /**
   * Create a Token.
   * @param {TokenCreateArgs} args - Arguments to create a Token.
   * @example
   * // Create one Token
   * const Token = await prisma.token.create({
   *   data: {
   *     // ... data to create a Token
   *   }
   * })
   *
   */
  create<T extends TokenCreateArgs>(
    args: Prisma.SelectSubset<T, TokenCreateArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<Prisma.$TokenPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Tokens.
   * @param {TokenCreateManyArgs} args - Arguments to create many Tokens.
   * @example
   * // Create many Tokens
   * const token = await prisma.token.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends TokenCreateManyArgs>(
    args?: Prisma.SelectSubset<T, TokenCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Tokens and returns the data saved in the database.
   * @param {TokenCreateManyAndReturnArgs} args - Arguments to create many Tokens.
   * @example
   * // Create many Tokens
   * const token = await prisma.token.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Tokens and only return the `id`
   * const tokenWithIdOnly = await prisma.token.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends TokenCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, TokenCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$TokenPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Token.
   * @param {TokenDeleteArgs} args - Arguments to delete one Token.
   * @example
   * // Delete one Token
   * const Token = await prisma.token.delete({
   *   where: {
   *     // ... filter to delete one Token
   *   }
   * })
   *
   */
  delete<T extends TokenDeleteArgs>(
    args: Prisma.SelectSubset<T, TokenDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<Prisma.$TokenPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Token.
   * @param {TokenUpdateArgs} args - Arguments to update one Token.
   * @example
   * // Update one Token
   * const token = await prisma.token.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends TokenUpdateArgs>(
    args: Prisma.SelectSubset<T, TokenUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<Prisma.$TokenPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Tokens.
   * @param {TokenDeleteManyArgs} args - Arguments to filter Tokens to delete.
   * @example
   * // Delete a few Tokens
   * const { count } = await prisma.token.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends TokenDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, TokenDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tokens.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TokenUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Tokens
   * const token = await prisma.token.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends TokenUpdateManyArgs>(
    args: Prisma.SelectSubset<T, TokenUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tokens and returns the data updated in the database.
   * @param {TokenUpdateManyAndReturnArgs} args - Arguments to update many Tokens.
   * @example
   * // Update many Tokens
   * const token = await prisma.token.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Tokens and only return the `id`
   * const tokenWithIdOnly = await prisma.token.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends TokenUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, TokenUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$TokenPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Token.
   * @param {TokenUpsertArgs} args - Arguments to update or create a Token.
   * @example
   * // Update or create a Token
   * const token = await prisma.token.upsert({
   *   create: {
   *     // ... data to create a Token
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Token we want to update
   *   }
   * })
   */
  upsert<T extends TokenUpsertArgs>(
    args: Prisma.SelectSubset<T, TokenUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<Prisma.$TokenPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Tokens.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TokenCountArgs} args - Arguments to filter Tokens to count.
   * @example
   * // Count the number of Tokens
   * const count = await prisma.token.count({
   *   where: {
   *     // ... the filter for the Tokens we want to count
   *   }
   * })
   **/
  count<T extends TokenCountArgs>(
    args?: Prisma.Subset<T, TokenCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TokenCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Token.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TokenAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends TokenAggregateArgs>(
    args: Prisma.Subset<T, TokenAggregateArgs>
  ): Prisma.PrismaPromise<GetTokenAggregateType<T>>

  /**
   * Group by Token.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TokenGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends TokenGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TokenGroupByArgs['orderBy'] }
      : { orderBy?: TokenGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, TokenGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetTokenGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Token model
   */
  readonly fields: TokenFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Token.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TokenClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Token model
 */
export interface TokenFieldRefs {
  readonly id: Prisma.FieldRef<'Token', 'String'>
  readonly token: Prisma.FieldRef<'Token', 'String'>
  readonly type: Prisma.FieldRef<'Token', 'TokenTypes'>
  readonly expiresAt: Prisma.FieldRef<'Token', 'DateTime'>
  readonly createdAt: Prisma.FieldRef<'Token', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'Token', 'DateTime'>
  readonly userId: Prisma.FieldRef<'Token', 'String'>
}

// Custom InputTypes
/**
 * Token findUnique
 */
export type TokenFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * Filter, which Token to fetch.
   */
  where: Prisma.TokenWhereUniqueInput
}

/**
 * Token findUniqueOrThrow
 */
export type TokenFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * Filter, which Token to fetch.
   */
  where: Prisma.TokenWhereUniqueInput
}

/**
 * Token findFirst
 */
export type TokenFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * Filter, which Token to fetch.
   */
  where?: Prisma.TokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Tokens to fetch.
   */
  orderBy?: Prisma.TokenOrderByWithRelationInput | Prisma.TokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Tokens.
   */
  cursor?: Prisma.TokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Tokens.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Tokens.
   */
  distinct?: Prisma.TokenScalarFieldEnum | Prisma.TokenScalarFieldEnum[]
}

/**
 * Token findFirstOrThrow
 */
export type TokenFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * Filter, which Token to fetch.
   */
  where?: Prisma.TokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Tokens to fetch.
   */
  orderBy?: Prisma.TokenOrderByWithRelationInput | Prisma.TokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Tokens.
   */
  cursor?: Prisma.TokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Tokens.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Tokens.
   */
  distinct?: Prisma.TokenScalarFieldEnum | Prisma.TokenScalarFieldEnum[]
}

/**
 * Token findMany
 */
export type TokenFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * Filter, which Tokens to fetch.
   */
  where?: Prisma.TokenWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Tokens to fetch.
   */
  orderBy?: Prisma.TokenOrderByWithRelationInput | Prisma.TokenOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Tokens.
   */
  cursor?: Prisma.TokenWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Tokens from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Tokens.
   */
  skip?: number
  distinct?: Prisma.TokenScalarFieldEnum | Prisma.TokenScalarFieldEnum[]
}

/**
 * Token create
 */
export type TokenCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * The data needed to create a Token.
   */
  data: Prisma.XOR<Prisma.TokenCreateInput, Prisma.TokenUncheckedCreateInput>
}

/**
 * Token createMany
 */
export type TokenCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Tokens.
   */
  data: Prisma.TokenCreateManyInput | Prisma.TokenCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Token createManyAndReturn
 */
export type TokenCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * The data used to create many Tokens.
   */
  data: Prisma.TokenCreateManyInput | Prisma.TokenCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Token update
 */
export type TokenUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * The data needed to update a Token.
   */
  data: Prisma.XOR<Prisma.TokenUpdateInput, Prisma.TokenUncheckedUpdateInput>
  /**
   * Choose, which Token to update.
   */
  where: Prisma.TokenWhereUniqueInput
}

/**
 * Token updateMany
 */
export type TokenUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Tokens.
   */
  data: Prisma.XOR<Prisma.TokenUpdateManyMutationInput, Prisma.TokenUncheckedUpdateManyInput>
  /**
   * Filter which Tokens to update
   */
  where?: Prisma.TokenWhereInput
  /**
   * Limit how many Tokens to update.
   */
  limit?: number
}

/**
 * Token updateManyAndReturn
 */
export type TokenUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * The data used to update Tokens.
   */
  data: Prisma.XOR<Prisma.TokenUpdateManyMutationInput, Prisma.TokenUncheckedUpdateManyInput>
  /**
   * Filter which Tokens to update
   */
  where?: Prisma.TokenWhereInput
  /**
   * Limit how many Tokens to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Token upsert
 */
export type TokenUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * The filter to search for the Token to update in case it exists.
   */
  where: Prisma.TokenWhereUniqueInput
  /**
   * In case the Token found by the `where` argument doesn't exist, create a new Token with this data.
   */
  create: Prisma.XOR<Prisma.TokenCreateInput, Prisma.TokenUncheckedCreateInput>
  /**
   * In case the Token was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TokenUpdateInput, Prisma.TokenUncheckedUpdateInput>
}

/**
 * Token delete
 */
export type TokenDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  /**
   * Filter which Token to delete.
   */
  where: Prisma.TokenWhereUniqueInput
}

/**
 * Token deleteMany
 */
export type TokenDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Tokens to delete
   */
  where?: Prisma.TokenWhereInput
  /**
   * Limit how many Tokens to delete.
   */
  limit?: number
}

/**
 * Token without action
 */
export type TokenDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
}
