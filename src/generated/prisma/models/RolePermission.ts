/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `RolePermission` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model RolePermission
 *
 */
export type RolePermissionModel =
  runtime.Types.Result.DefaultSelection<Prisma.$RolePermissionPayload>

export type AggregateRolePermission = {
  _count: RolePermissionCountAggregateOutputType | null
  _min: RolePermissionMinAggregateOutputType | null
  _max: RolePermissionMaxAggregateOutputType | null
}

export type RolePermissionMinAggregateOutputType = {
  id: string | null
  roleId: string | null
  permissionId: string | null
}

export type RolePermissionMaxAggregateOutputType = {
  id: string | null
  roleId: string | null
  permissionId: string | null
}

export type RolePermissionCountAggregateOutputType = {
  id: number
  roleId: number
  permissionId: number
  _all: number
}

export type RolePermissionMinAggregateInputType = {
  id?: true
  roleId?: true
  permissionId?: true
}

export type RolePermissionMaxAggregateInputType = {
  id?: true
  roleId?: true
  permissionId?: true
}

export type RolePermissionCountAggregateInputType = {
  id?: true
  roleId?: true
  permissionId?: true
  _all?: true
}

export type RolePermissionAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which RolePermission to aggregate.
   */
  where?: Prisma.RolePermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of RolePermissions to fetch.
   */
  orderBy?:
    | Prisma.RolePermissionOrderByWithRelationInput
    | Prisma.RolePermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.RolePermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` RolePermissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` RolePermissions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned RolePermissions
   **/
  _count?: true | RolePermissionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: RolePermissionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: RolePermissionMaxAggregateInputType
}

export type GetRolePermissionAggregateType<T extends RolePermissionAggregateArgs> = {
  [P in keyof T & keyof AggregateRolePermission]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRolePermission[P]>
    : Prisma.GetScalarType<T[P], AggregateRolePermission[P]>
}

export type RolePermissionGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.RolePermissionWhereInput
  orderBy?:
    | Prisma.RolePermissionOrderByWithAggregationInput
    | Prisma.RolePermissionOrderByWithAggregationInput[]
  by: Prisma.RolePermissionScalarFieldEnum[] | Prisma.RolePermissionScalarFieldEnum
  having?: Prisma.RolePermissionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RolePermissionCountAggregateInputType | true
  _min?: RolePermissionMinAggregateInputType
  _max?: RolePermissionMaxAggregateInputType
}

export type RolePermissionGroupByOutputType = {
  id: string
  roleId: string
  permissionId: string
  _count: RolePermissionCountAggregateOutputType | null
  _min: RolePermissionMinAggregateOutputType | null
  _max: RolePermissionMaxAggregateOutputType | null
}

type GetRolePermissionGroupByPayload<T extends RolePermissionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RolePermissionGroupByOutputType, T['by']> & {
      [P in keyof T & keyof RolePermissionGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], RolePermissionGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], RolePermissionGroupByOutputType[P]>
    }
  >
>

export type RolePermissionWhereInput = {
  AND?: Prisma.RolePermissionWhereInput | Prisma.RolePermissionWhereInput[]
  OR?: Prisma.RolePermissionWhereInput[]
  NOT?: Prisma.RolePermissionWhereInput | Prisma.RolePermissionWhereInput[]
  id?: Prisma.StringFilter<'RolePermission'> | string
  roleId?: Prisma.StringFilter<'RolePermission'> | string
  permissionId?: Prisma.StringFilter<'RolePermission'> | string
  role?: Prisma.XOR<Prisma.RoleScalarRelationFilter, Prisma.RoleWhereInput>
  permission?: Prisma.XOR<Prisma.PermissionScalarRelationFilter, Prisma.PermissionWhereInput>
}

export type RolePermissionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  permissionId?: Prisma.SortOrder
  role?: Prisma.RoleOrderByWithRelationInput
  permission?: Prisma.PermissionOrderByWithRelationInput
}

export type RolePermissionWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    roleId_permissionId?: Prisma.RolePermissionRoleIdPermissionIdCompoundUniqueInput
    AND?: Prisma.RolePermissionWhereInput | Prisma.RolePermissionWhereInput[]
    OR?: Prisma.RolePermissionWhereInput[]
    NOT?: Prisma.RolePermissionWhereInput | Prisma.RolePermissionWhereInput[]
    roleId?: Prisma.StringFilter<'RolePermission'> | string
    permissionId?: Prisma.StringFilter<'RolePermission'> | string
    role?: Prisma.XOR<Prisma.RoleScalarRelationFilter, Prisma.RoleWhereInput>
    permission?: Prisma.XOR<Prisma.PermissionScalarRelationFilter, Prisma.PermissionWhereInput>
  },
  'id' | 'roleId_permissionId'
>

export type RolePermissionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  permissionId?: Prisma.SortOrder
  _count?: Prisma.RolePermissionCountOrderByAggregateInput
  _max?: Prisma.RolePermissionMaxOrderByAggregateInput
  _min?: Prisma.RolePermissionMinOrderByAggregateInput
}

export type RolePermissionScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.RolePermissionScalarWhereWithAggregatesInput
    | Prisma.RolePermissionScalarWhereWithAggregatesInput[]
  OR?: Prisma.RolePermissionScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.RolePermissionScalarWhereWithAggregatesInput
    | Prisma.RolePermissionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'RolePermission'> | string
  roleId?: Prisma.StringWithAggregatesFilter<'RolePermission'> | string
  permissionId?: Prisma.StringWithAggregatesFilter<'RolePermission'> | string
}

export type RolePermissionCreateInput = {
  id?: string
  role: Prisma.RoleCreateNestedOneWithoutRolePermissionsInput
  permission: Prisma.PermissionCreateNestedOneWithoutRolePermissionsInput
}

export type RolePermissionUncheckedCreateInput = {
  id?: string
  roleId: string
  permissionId: string
}

export type RolePermissionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.RoleUpdateOneRequiredWithoutRolePermissionsNestedInput
  permission?: Prisma.PermissionUpdateOneRequiredWithoutRolePermissionsNestedInput
}

export type RolePermissionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  permissionId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type RolePermissionCreateManyInput = {
  id?: string
  roleId: string
  permissionId: string
}

export type RolePermissionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
}

export type RolePermissionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  permissionId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type RolePermissionListRelationFilter = {
  every?: Prisma.RolePermissionWhereInput
  some?: Prisma.RolePermissionWhereInput
  none?: Prisma.RolePermissionWhereInput
}

export type RolePermissionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type RolePermissionRoleIdPermissionIdCompoundUniqueInput = {
  roleId: string
  permissionId: string
}

export type RolePermissionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  permissionId?: Prisma.SortOrder
}

export type RolePermissionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  permissionId?: Prisma.SortOrder
}

export type RolePermissionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  permissionId?: Prisma.SortOrder
}

export type RolePermissionCreateNestedManyWithoutRoleInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutRoleInput,
        Prisma.RolePermissionUncheckedCreateWithoutRoleInput
      >
    | Prisma.RolePermissionCreateWithoutRoleInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput[]
  createMany?: Prisma.RolePermissionCreateManyRoleInputEnvelope
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
}

export type RolePermissionUncheckedCreateNestedManyWithoutRoleInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutRoleInput,
        Prisma.RolePermissionUncheckedCreateWithoutRoleInput
      >
    | Prisma.RolePermissionCreateWithoutRoleInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput[]
  createMany?: Prisma.RolePermissionCreateManyRoleInputEnvelope
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
}

export type RolePermissionUpdateManyWithoutRoleNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutRoleInput,
        Prisma.RolePermissionUncheckedCreateWithoutRoleInput
      >
    | Prisma.RolePermissionCreateWithoutRoleInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput[]
  upsert?:
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutRoleInput
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutRoleInput[]
  createMany?: Prisma.RolePermissionCreateManyRoleInputEnvelope
  set?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  disconnect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  delete?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  update?:
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutRoleInput
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutRoleInput[]
  updateMany?:
    | Prisma.RolePermissionUpdateManyWithWhereWithoutRoleInput
    | Prisma.RolePermissionUpdateManyWithWhereWithoutRoleInput[]
  deleteMany?: Prisma.RolePermissionScalarWhereInput | Prisma.RolePermissionScalarWhereInput[]
}

export type RolePermissionUncheckedUpdateManyWithoutRoleNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutRoleInput,
        Prisma.RolePermissionUncheckedCreateWithoutRoleInput
      >
    | Prisma.RolePermissionCreateWithoutRoleInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput
    | Prisma.RolePermissionCreateOrConnectWithoutRoleInput[]
  upsert?:
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutRoleInput
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutRoleInput[]
  createMany?: Prisma.RolePermissionCreateManyRoleInputEnvelope
  set?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  disconnect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  delete?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  update?:
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutRoleInput
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutRoleInput[]
  updateMany?:
    | Prisma.RolePermissionUpdateManyWithWhereWithoutRoleInput
    | Prisma.RolePermissionUpdateManyWithWhereWithoutRoleInput[]
  deleteMany?: Prisma.RolePermissionScalarWhereInput | Prisma.RolePermissionScalarWhereInput[]
}

export type RolePermissionCreateNestedManyWithoutPermissionInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutPermissionInput,
        Prisma.RolePermissionUncheckedCreateWithoutPermissionInput
      >
    | Prisma.RolePermissionCreateWithoutPermissionInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutPermissionInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput[]
  createMany?: Prisma.RolePermissionCreateManyPermissionInputEnvelope
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
}

export type RolePermissionUncheckedCreateNestedManyWithoutPermissionInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutPermissionInput,
        Prisma.RolePermissionUncheckedCreateWithoutPermissionInput
      >
    | Prisma.RolePermissionCreateWithoutPermissionInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutPermissionInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput[]
  createMany?: Prisma.RolePermissionCreateManyPermissionInputEnvelope
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
}

export type RolePermissionUpdateManyWithoutPermissionNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutPermissionInput,
        Prisma.RolePermissionUncheckedCreateWithoutPermissionInput
      >
    | Prisma.RolePermissionCreateWithoutPermissionInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutPermissionInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput[]
  upsert?:
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutPermissionInput
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutPermissionInput[]
  createMany?: Prisma.RolePermissionCreateManyPermissionInputEnvelope
  set?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  disconnect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  delete?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  update?:
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutPermissionInput
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutPermissionInput[]
  updateMany?:
    | Prisma.RolePermissionUpdateManyWithWhereWithoutPermissionInput
    | Prisma.RolePermissionUpdateManyWithWhereWithoutPermissionInput[]
  deleteMany?: Prisma.RolePermissionScalarWhereInput | Prisma.RolePermissionScalarWhereInput[]
}

export type RolePermissionUncheckedUpdateManyWithoutPermissionNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.RolePermissionCreateWithoutPermissionInput,
        Prisma.RolePermissionUncheckedCreateWithoutPermissionInput
      >
    | Prisma.RolePermissionCreateWithoutPermissionInput[]
    | Prisma.RolePermissionUncheckedCreateWithoutPermissionInput[]
  connectOrCreate?:
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput
    | Prisma.RolePermissionCreateOrConnectWithoutPermissionInput[]
  upsert?:
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutPermissionInput
    | Prisma.RolePermissionUpsertWithWhereUniqueWithoutPermissionInput[]
  createMany?: Prisma.RolePermissionCreateManyPermissionInputEnvelope
  set?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  disconnect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  delete?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  connect?: Prisma.RolePermissionWhereUniqueInput | Prisma.RolePermissionWhereUniqueInput[]
  update?:
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutPermissionInput
    | Prisma.RolePermissionUpdateWithWhereUniqueWithoutPermissionInput[]
  updateMany?:
    | Prisma.RolePermissionUpdateManyWithWhereWithoutPermissionInput
    | Prisma.RolePermissionUpdateManyWithWhereWithoutPermissionInput[]
  deleteMany?: Prisma.RolePermissionScalarWhereInput | Prisma.RolePermissionScalarWhereInput[]
}

export type RolePermissionCreateWithoutRoleInput = {
  id?: string
  permission: Prisma.PermissionCreateNestedOneWithoutRolePermissionsInput
}

export type RolePermissionUncheckedCreateWithoutRoleInput = {
  id?: string
  permissionId: string
}

export type RolePermissionCreateOrConnectWithoutRoleInput = {
  where: Prisma.RolePermissionWhereUniqueInput
  create: Prisma.XOR<
    Prisma.RolePermissionCreateWithoutRoleInput,
    Prisma.RolePermissionUncheckedCreateWithoutRoleInput
  >
}

export type RolePermissionCreateManyRoleInputEnvelope = {
  data: Prisma.RolePermissionCreateManyRoleInput | Prisma.RolePermissionCreateManyRoleInput[]
  skipDuplicates?: boolean
}

export type RolePermissionUpsertWithWhereUniqueWithoutRoleInput = {
  where: Prisma.RolePermissionWhereUniqueInput
  update: Prisma.XOR<
    Prisma.RolePermissionUpdateWithoutRoleInput,
    Prisma.RolePermissionUncheckedUpdateWithoutRoleInput
  >
  create: Prisma.XOR<
    Prisma.RolePermissionCreateWithoutRoleInput,
    Prisma.RolePermissionUncheckedCreateWithoutRoleInput
  >
}

export type RolePermissionUpdateWithWhereUniqueWithoutRoleInput = {
  where: Prisma.RolePermissionWhereUniqueInput
  data: Prisma.XOR<
    Prisma.RolePermissionUpdateWithoutRoleInput,
    Prisma.RolePermissionUncheckedUpdateWithoutRoleInput
  >
}

export type RolePermissionUpdateManyWithWhereWithoutRoleInput = {
  where: Prisma.RolePermissionScalarWhereInput
  data: Prisma.XOR<
    Prisma.RolePermissionUpdateManyMutationInput,
    Prisma.RolePermissionUncheckedUpdateManyWithoutRoleInput
  >
}

export type RolePermissionScalarWhereInput = {
  AND?: Prisma.RolePermissionScalarWhereInput | Prisma.RolePermissionScalarWhereInput[]
  OR?: Prisma.RolePermissionScalarWhereInput[]
  NOT?: Prisma.RolePermissionScalarWhereInput | Prisma.RolePermissionScalarWhereInput[]
  id?: Prisma.StringFilter<'RolePermission'> | string
  roleId?: Prisma.StringFilter<'RolePermission'> | string
  permissionId?: Prisma.StringFilter<'RolePermission'> | string
}

export type RolePermissionCreateWithoutPermissionInput = {
  id?: string
  role: Prisma.RoleCreateNestedOneWithoutRolePermissionsInput
}

export type RolePermissionUncheckedCreateWithoutPermissionInput = {
  id?: string
  roleId: string
}

export type RolePermissionCreateOrConnectWithoutPermissionInput = {
  where: Prisma.RolePermissionWhereUniqueInput
  create: Prisma.XOR<
    Prisma.RolePermissionCreateWithoutPermissionInput,
    Prisma.RolePermissionUncheckedCreateWithoutPermissionInput
  >
}

export type RolePermissionCreateManyPermissionInputEnvelope = {
  data:
    | Prisma.RolePermissionCreateManyPermissionInput
    | Prisma.RolePermissionCreateManyPermissionInput[]
  skipDuplicates?: boolean
}

export type RolePermissionUpsertWithWhereUniqueWithoutPermissionInput = {
  where: Prisma.RolePermissionWhereUniqueInput
  update: Prisma.XOR<
    Prisma.RolePermissionUpdateWithoutPermissionInput,
    Prisma.RolePermissionUncheckedUpdateWithoutPermissionInput
  >
  create: Prisma.XOR<
    Prisma.RolePermissionCreateWithoutPermissionInput,
    Prisma.RolePermissionUncheckedCreateWithoutPermissionInput
  >
}

export type RolePermissionUpdateWithWhereUniqueWithoutPermissionInput = {
  where: Prisma.RolePermissionWhereUniqueInput
  data: Prisma.XOR<
    Prisma.RolePermissionUpdateWithoutPermissionInput,
    Prisma.RolePermissionUncheckedUpdateWithoutPermissionInput
  >
}

export type RolePermissionUpdateManyWithWhereWithoutPermissionInput = {
  where: Prisma.RolePermissionScalarWhereInput
  data: Prisma.XOR<
    Prisma.RolePermissionUpdateManyMutationInput,
    Prisma.RolePermissionUncheckedUpdateManyWithoutPermissionInput
  >
}

export type RolePermissionCreateManyRoleInput = {
  id?: string
  permissionId: string
}

export type RolePermissionUpdateWithoutRoleInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  permission?: Prisma.PermissionUpdateOneRequiredWithoutRolePermissionsNestedInput
}

export type RolePermissionUncheckedUpdateWithoutRoleInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  permissionId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type RolePermissionUncheckedUpdateManyWithoutRoleInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  permissionId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type RolePermissionCreateManyPermissionInput = {
  id?: string
  roleId: string
}

export type RolePermissionUpdateWithoutPermissionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.RoleUpdateOneRequiredWithoutRolePermissionsNestedInput
}

export type RolePermissionUncheckedUpdateWithoutPermissionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type RolePermissionUncheckedUpdateManyWithoutPermissionInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type RolePermissionSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    roleId?: boolean
    permissionId?: boolean
    role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
    permission?: boolean | Prisma.PermissionDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['rolePermission']
>

export type RolePermissionSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    roleId?: boolean
    permissionId?: boolean
    role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
    permission?: boolean | Prisma.PermissionDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['rolePermission']
>

export type RolePermissionSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    roleId?: boolean
    permissionId?: boolean
    role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
    permission?: boolean | Prisma.PermissionDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['rolePermission']
>

export type RolePermissionSelectScalar = {
  id?: boolean
  roleId?: boolean
  permissionId?: boolean
}

export type RolePermissionOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'roleId' | 'permissionId',
  ExtArgs['result']['rolePermission']
>
export type RolePermissionInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
  permission?: boolean | Prisma.PermissionDefaultArgs<ExtArgs>
}
export type RolePermissionIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
  permission?: boolean | Prisma.PermissionDefaultArgs<ExtArgs>
}
export type RolePermissionIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
  permission?: boolean | Prisma.PermissionDefaultArgs<ExtArgs>
}

export type $RolePermissionPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'RolePermission'
  objects: {
    role: Prisma.$RolePayload<ExtArgs>
    permission: Prisma.$PermissionPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      roleId: string
      permissionId: string
    },
    ExtArgs['result']['rolePermission']
  >
  composites: {}
}

export type RolePermissionGetPayload<
  S extends boolean | null | undefined | RolePermissionDefaultArgs,
> = runtime.Types.Result.GetResult<Prisma.$RolePermissionPayload, S>

export type RolePermissionCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<RolePermissionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: RolePermissionCountAggregateInputType | true
}

export interface RolePermissionDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['RolePermission']
    meta: { name: 'RolePermission' }
  }
  /**
   * Find zero or one RolePermission that matches the filter.
   * @param {RolePermissionFindUniqueArgs} args - Arguments to find a RolePermission
   * @example
   * // Get one RolePermission
   * const rolePermission = await prisma.rolePermission.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends RolePermissionFindUniqueArgs>(
    args: Prisma.SelectSubset<T, RolePermissionFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one RolePermission that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {RolePermissionFindUniqueOrThrowArgs} args - Arguments to find a RolePermission
   * @example
   * // Get one RolePermission
   * const rolePermission = await prisma.rolePermission.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends RolePermissionFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, RolePermissionFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first RolePermission that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RolePermissionFindFirstArgs} args - Arguments to find a RolePermission
   * @example
   * // Get one RolePermission
   * const rolePermission = await prisma.rolePermission.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends RolePermissionFindFirstArgs>(
    args?: Prisma.SelectSubset<T, RolePermissionFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first RolePermission that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RolePermissionFindFirstOrThrowArgs} args - Arguments to find a RolePermission
   * @example
   * // Get one RolePermission
   * const rolePermission = await prisma.rolePermission.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends RolePermissionFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, RolePermissionFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more RolePermissions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RolePermissionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all RolePermissions
   * const rolePermissions = await prisma.rolePermission.findMany()
   *
   * // Get first 10 RolePermissions
   * const rolePermissions = await prisma.rolePermission.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const rolePermissionWithIdOnly = await prisma.rolePermission.findMany({ select: { id: true } })
   *
   */
  findMany<T extends RolePermissionFindManyArgs>(
    args?: Prisma.SelectSubset<T, RolePermissionFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a RolePermission.
   * @param {RolePermissionCreateArgs} args - Arguments to create a RolePermission.
   * @example
   * // Create one RolePermission
   * const RolePermission = await prisma.rolePermission.create({
   *   data: {
   *     // ... data to create a RolePermission
   *   }
   * })
   *
   */
  create<T extends RolePermissionCreateArgs>(
    args: Prisma.SelectSubset<T, RolePermissionCreateArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many RolePermissions.
   * @param {RolePermissionCreateManyArgs} args - Arguments to create many RolePermissions.
   * @example
   * // Create many RolePermissions
   * const rolePermission = await prisma.rolePermission.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends RolePermissionCreateManyArgs>(
    args?: Prisma.SelectSubset<T, RolePermissionCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many RolePermissions and returns the data saved in the database.
   * @param {RolePermissionCreateManyAndReturnArgs} args - Arguments to create many RolePermissions.
   * @example
   * // Create many RolePermissions
   * const rolePermission = await prisma.rolePermission.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many RolePermissions and only return the `id`
   * const rolePermissionWithIdOnly = await prisma.rolePermission.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends RolePermissionCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, RolePermissionCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a RolePermission.
   * @param {RolePermissionDeleteArgs} args - Arguments to delete one RolePermission.
   * @example
   * // Delete one RolePermission
   * const RolePermission = await prisma.rolePermission.delete({
   *   where: {
   *     // ... filter to delete one RolePermission
   *   }
   * })
   *
   */
  delete<T extends RolePermissionDeleteArgs>(
    args: Prisma.SelectSubset<T, RolePermissionDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one RolePermission.
   * @param {RolePermissionUpdateArgs} args - Arguments to update one RolePermission.
   * @example
   * // Update one RolePermission
   * const rolePermission = await prisma.rolePermission.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends RolePermissionUpdateArgs>(
    args: Prisma.SelectSubset<T, RolePermissionUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more RolePermissions.
   * @param {RolePermissionDeleteManyArgs} args - Arguments to filter RolePermissions to delete.
   * @example
   * // Delete a few RolePermissions
   * const { count } = await prisma.rolePermission.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends RolePermissionDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, RolePermissionDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RolePermissions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RolePermissionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many RolePermissions
   * const rolePermission = await prisma.rolePermission.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends RolePermissionUpdateManyArgs>(
    args: Prisma.SelectSubset<T, RolePermissionUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RolePermissions and returns the data updated in the database.
   * @param {RolePermissionUpdateManyAndReturnArgs} args - Arguments to update many RolePermissions.
   * @example
   * // Update many RolePermissions
   * const rolePermission = await prisma.rolePermission.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more RolePermissions and only return the `id`
   * const rolePermissionWithIdOnly = await prisma.rolePermission.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends RolePermissionUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, RolePermissionUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one RolePermission.
   * @param {RolePermissionUpsertArgs} args - Arguments to update or create a RolePermission.
   * @example
   * // Update or create a RolePermission
   * const rolePermission = await prisma.rolePermission.upsert({
   *   create: {
   *     // ... data to create a RolePermission
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the RolePermission we want to update
   *   }
   * })
   */
  upsert<T extends RolePermissionUpsertArgs>(
    args: Prisma.SelectSubset<T, RolePermissionUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__RolePermissionClient<
    runtime.Types.Result.GetResult<
      Prisma.$RolePermissionPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of RolePermissions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RolePermissionCountArgs} args - Arguments to filter RolePermissions to count.
   * @example
   * // Count the number of RolePermissions
   * const count = await prisma.rolePermission.count({
   *   where: {
   *     // ... the filter for the RolePermissions we want to count
   *   }
   * })
   **/
  count<T extends RolePermissionCountArgs>(
    args?: Prisma.Subset<T, RolePermissionCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RolePermissionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a RolePermission.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RolePermissionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends RolePermissionAggregateArgs>(
    args: Prisma.Subset<T, RolePermissionAggregateArgs>
  ): Prisma.PrismaPromise<GetRolePermissionAggregateType<T>>

  /**
   * Group by RolePermission.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RolePermissionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends RolePermissionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: RolePermissionGroupByArgs['orderBy'] }
      : { orderBy?: RolePermissionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, RolePermissionGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetRolePermissionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the RolePermission model
   */
  readonly fields: RolePermissionFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for RolePermission.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__RolePermissionClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  role<T extends Prisma.RoleDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.RoleDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__RoleClient<
    | runtime.Types.Result.GetResult<
        Prisma.$RolePayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  permission<T extends Prisma.PermissionDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.PermissionDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__PermissionClient<
    | runtime.Types.Result.GetResult<
        Prisma.$PermissionPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the RolePermission model
 */
export interface RolePermissionFieldRefs {
  readonly id: Prisma.FieldRef<'RolePermission', 'String'>
  readonly roleId: Prisma.FieldRef<'RolePermission', 'String'>
  readonly permissionId: Prisma.FieldRef<'RolePermission', 'String'>
}

// Custom InputTypes
/**
 * RolePermission findUnique
 */
export type RolePermissionFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * Filter, which RolePermission to fetch.
   */
  where: Prisma.RolePermissionWhereUniqueInput
}

/**
 * RolePermission findUniqueOrThrow
 */
export type RolePermissionFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * Filter, which RolePermission to fetch.
   */
  where: Prisma.RolePermissionWhereUniqueInput
}

/**
 * RolePermission findFirst
 */
export type RolePermissionFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * Filter, which RolePermission to fetch.
   */
  where?: Prisma.RolePermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of RolePermissions to fetch.
   */
  orderBy?:
    | Prisma.RolePermissionOrderByWithRelationInput
    | Prisma.RolePermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for RolePermissions.
   */
  cursor?: Prisma.RolePermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` RolePermissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` RolePermissions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of RolePermissions.
   */
  distinct?: Prisma.RolePermissionScalarFieldEnum | Prisma.RolePermissionScalarFieldEnum[]
}

/**
 * RolePermission findFirstOrThrow
 */
export type RolePermissionFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * Filter, which RolePermission to fetch.
   */
  where?: Prisma.RolePermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of RolePermissions to fetch.
   */
  orderBy?:
    | Prisma.RolePermissionOrderByWithRelationInput
    | Prisma.RolePermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for RolePermissions.
   */
  cursor?: Prisma.RolePermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` RolePermissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` RolePermissions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of RolePermissions.
   */
  distinct?: Prisma.RolePermissionScalarFieldEnum | Prisma.RolePermissionScalarFieldEnum[]
}

/**
 * RolePermission findMany
 */
export type RolePermissionFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * Filter, which RolePermissions to fetch.
   */
  where?: Prisma.RolePermissionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of RolePermissions to fetch.
   */
  orderBy?:
    | Prisma.RolePermissionOrderByWithRelationInput
    | Prisma.RolePermissionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing RolePermissions.
   */
  cursor?: Prisma.RolePermissionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` RolePermissions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` RolePermissions.
   */
  skip?: number
  distinct?: Prisma.RolePermissionScalarFieldEnum | Prisma.RolePermissionScalarFieldEnum[]
}

/**
 * RolePermission create
 */
export type RolePermissionCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * The data needed to create a RolePermission.
   */
  data: Prisma.XOR<Prisma.RolePermissionCreateInput, Prisma.RolePermissionUncheckedCreateInput>
}

/**
 * RolePermission createMany
 */
export type RolePermissionCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many RolePermissions.
   */
  data: Prisma.RolePermissionCreateManyInput | Prisma.RolePermissionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * RolePermission createManyAndReturn
 */
export type RolePermissionCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * The data used to create many RolePermissions.
   */
  data: Prisma.RolePermissionCreateManyInput | Prisma.RolePermissionCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * RolePermission update
 */
export type RolePermissionUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * The data needed to update a RolePermission.
   */
  data: Prisma.XOR<Prisma.RolePermissionUpdateInput, Prisma.RolePermissionUncheckedUpdateInput>
  /**
   * Choose, which RolePermission to update.
   */
  where: Prisma.RolePermissionWhereUniqueInput
}

/**
 * RolePermission updateMany
 */
export type RolePermissionUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update RolePermissions.
   */
  data: Prisma.XOR<
    Prisma.RolePermissionUpdateManyMutationInput,
    Prisma.RolePermissionUncheckedUpdateManyInput
  >
  /**
   * Filter which RolePermissions to update
   */
  where?: Prisma.RolePermissionWhereInput
  /**
   * Limit how many RolePermissions to update.
   */
  limit?: number
}

/**
 * RolePermission updateManyAndReturn
 */
export type RolePermissionUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * The data used to update RolePermissions.
   */
  data: Prisma.XOR<
    Prisma.RolePermissionUpdateManyMutationInput,
    Prisma.RolePermissionUncheckedUpdateManyInput
  >
  /**
   * Filter which RolePermissions to update
   */
  where?: Prisma.RolePermissionWhereInput
  /**
   * Limit how many RolePermissions to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * RolePermission upsert
 */
export type RolePermissionUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * The filter to search for the RolePermission to update in case it exists.
   */
  where: Prisma.RolePermissionWhereUniqueInput
  /**
   * In case the RolePermission found by the `where` argument doesn't exist, create a new RolePermission with this data.
   */
  create: Prisma.XOR<Prisma.RolePermissionCreateInput, Prisma.RolePermissionUncheckedCreateInput>
  /**
   * In case the RolePermission was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.RolePermissionUpdateInput, Prisma.RolePermissionUncheckedUpdateInput>
}

/**
 * RolePermission delete
 */
export type RolePermissionDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
  /**
   * Filter which RolePermission to delete.
   */
  where: Prisma.RolePermissionWhereUniqueInput
}

/**
 * RolePermission deleteMany
 */
export type RolePermissionDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which RolePermissions to delete
   */
  where?: Prisma.RolePermissionWhereInput
  /**
   * Limit how many RolePermissions to delete.
   */
  limit?: number
}

/**
 * RolePermission without action
 */
export type RolePermissionDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the RolePermission
   */
  select?: Prisma.RolePermissionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RolePermission
   */
  omit?: Prisma.RolePermissionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RolePermissionInclude<ExtArgs> | null
}
