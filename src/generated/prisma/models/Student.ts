/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Student` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Student
 *
 */
export type StudentModel = runtime.Types.Result.DefaultSelection<Prisma.$StudentPayload>

export type AggregateStudent = {
  _count: StudentCountAggregateOutputType | null
  _min: StudentMinAggregateOutputType | null
  _max: StudentMaxAggregateOutputType | null
}

export type StudentMinAggregateOutputType = {
  id: string | null
  userId: string | null
  studentId: string | null
  emergencyContact: string | null
  enrollmentDate: Date | null
  schoolId: string | null
}

export type StudentMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  studentId: string | null
  emergencyContact: string | null
  enrollmentDate: Date | null
  schoolId: string | null
}

export type StudentCountAggregateOutputType = {
  id: number
  userId: number
  studentId: number
  emergencyContact: number
  enrollmentDate: number
  schoolId: number
  _all: number
}

export type StudentMinAggregateInputType = {
  id?: true
  userId?: true
  studentId?: true
  emergencyContact?: true
  enrollmentDate?: true
  schoolId?: true
}

export type StudentMaxAggregateInputType = {
  id?: true
  userId?: true
  studentId?: true
  emergencyContact?: true
  enrollmentDate?: true
  schoolId?: true
}

export type StudentCountAggregateInputType = {
  id?: true
  userId?: true
  studentId?: true
  emergencyContact?: true
  enrollmentDate?: true
  schoolId?: true
  _all?: true
}

export type StudentAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Student to aggregate.
   */
  where?: Prisma.StudentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Students to fetch.
   */
  orderBy?: Prisma.StudentOrderByWithRelationInput | Prisma.StudentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.StudentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Students from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Students.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Students
   **/
  _count?: true | StudentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: StudentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: StudentMaxAggregateInputType
}

export type GetStudentAggregateType<T extends StudentAggregateArgs> = {
  [P in keyof T & keyof AggregateStudent]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateStudent[P]>
    : Prisma.GetScalarType<T[P], AggregateStudent[P]>
}

export type StudentGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.StudentWhereInput
  orderBy?: Prisma.StudentOrderByWithAggregationInput | Prisma.StudentOrderByWithAggregationInput[]
  by: Prisma.StudentScalarFieldEnum[] | Prisma.StudentScalarFieldEnum
  having?: Prisma.StudentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: StudentCountAggregateInputType | true
  _min?: StudentMinAggregateInputType
  _max?: StudentMaxAggregateInputType
}

export type StudentGroupByOutputType = {
  id: string
  userId: string
  studentId: string
  emergencyContact: string | null
  enrollmentDate: Date
  schoolId: string | null
  _count: StudentCountAggregateOutputType | null
  _min: StudentMinAggregateOutputType | null
  _max: StudentMaxAggregateOutputType | null
}

type GetStudentGroupByPayload<T extends StudentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<StudentGroupByOutputType, T['by']> & {
      [P in keyof T & keyof StudentGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], StudentGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], StudentGroupByOutputType[P]>
    }
  >
>

export type StudentWhereInput = {
  AND?: Prisma.StudentWhereInput | Prisma.StudentWhereInput[]
  OR?: Prisma.StudentWhereInput[]
  NOT?: Prisma.StudentWhereInput | Prisma.StudentWhereInput[]
  id?: Prisma.StringFilter<'Student'> | string
  userId?: Prisma.StringFilter<'Student'> | string
  studentId?: Prisma.StringFilter<'Student'> | string
  emergencyContact?: Prisma.StringNullableFilter<'Student'> | string | null
  enrollmentDate?: Prisma.DateTimeFilter<'Student'> | Date | string
  schoolId?: Prisma.StringNullableFilter<'Student'> | string | null
  school?: Prisma.XOR<Prisma.SchoolNullableScalarRelationFilter, Prisma.SchoolWhereInput> | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type StudentOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  studentId?: Prisma.SortOrder
  emergencyContact?: Prisma.SortOrderInput | Prisma.SortOrder
  enrollmentDate?: Prisma.SortOrder
  schoolId?: Prisma.SortOrderInput | Prisma.SortOrder
  school?: Prisma.SchoolOrderByWithRelationInput
  user?: Prisma.UserOrderByWithRelationInput
}

export type StudentWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    userId?: string
    studentId?: string
    AND?: Prisma.StudentWhereInput | Prisma.StudentWhereInput[]
    OR?: Prisma.StudentWhereInput[]
    NOT?: Prisma.StudentWhereInput | Prisma.StudentWhereInput[]
    emergencyContact?: Prisma.StringNullableFilter<'Student'> | string | null
    enrollmentDate?: Prisma.DateTimeFilter<'Student'> | Date | string
    schoolId?: Prisma.StringNullableFilter<'Student'> | string | null
    school?: Prisma.XOR<Prisma.SchoolNullableScalarRelationFilter, Prisma.SchoolWhereInput> | null
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id' | 'userId' | 'studentId'
>

export type StudentOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  studentId?: Prisma.SortOrder
  emergencyContact?: Prisma.SortOrderInput | Prisma.SortOrder
  enrollmentDate?: Prisma.SortOrder
  schoolId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.StudentCountOrderByAggregateInput
  _max?: Prisma.StudentMaxOrderByAggregateInput
  _min?: Prisma.StudentMinOrderByAggregateInput
}

export type StudentScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.StudentScalarWhereWithAggregatesInput
    | Prisma.StudentScalarWhereWithAggregatesInput[]
  OR?: Prisma.StudentScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.StudentScalarWhereWithAggregatesInput
    | Prisma.StudentScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Student'> | string
  userId?: Prisma.StringWithAggregatesFilter<'Student'> | string
  studentId?: Prisma.StringWithAggregatesFilter<'Student'> | string
  emergencyContact?: Prisma.StringNullableWithAggregatesFilter<'Student'> | string | null
  enrollmentDate?: Prisma.DateTimeWithAggregatesFilter<'Student'> | Date | string
  schoolId?: Prisma.StringNullableWithAggregatesFilter<'Student'> | string | null
}

export type StudentCreateInput = {
  id?: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
  school?: Prisma.SchoolCreateNestedOneWithoutStudentsInput
  user: Prisma.UserCreateNestedOneWithoutStudentProfileInput
}

export type StudentUncheckedCreateInput = {
  id?: string
  userId: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
  schoolId?: string | null
}

export type StudentUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneWithoutStudentsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutStudentProfileNestedInput
}

export type StudentUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type StudentCreateManyInput = {
  id?: string
  userId: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
  schoolId?: string | null
}

export type StudentUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StudentUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type StudentListRelationFilter = {
  every?: Prisma.StudentWhereInput
  some?: Prisma.StudentWhereInput
  none?: Prisma.StudentWhereInput
}

export type StudentOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type StudentNullableScalarRelationFilter = {
  is?: Prisma.StudentWhereInput | null
  isNot?: Prisma.StudentWhereInput | null
}

export type StudentCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  studentId?: Prisma.SortOrder
  emergencyContact?: Prisma.SortOrder
  enrollmentDate?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type StudentMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  studentId?: Prisma.SortOrder
  emergencyContact?: Prisma.SortOrder
  enrollmentDate?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type StudentMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  studentId?: Prisma.SortOrder
  emergencyContact?: Prisma.SortOrder
  enrollmentDate?: Prisma.SortOrder
  schoolId?: Prisma.SortOrder
}

export type StudentCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.StudentCreateWithoutSchoolInput,
        Prisma.StudentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.StudentCreateWithoutSchoolInput[]
    | Prisma.StudentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.StudentCreateOrConnectWithoutSchoolInput
    | Prisma.StudentCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.StudentCreateManySchoolInputEnvelope
  connect?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
}

export type StudentUncheckedCreateNestedManyWithoutSchoolInput = {
  create?:
    | Prisma.XOR<
        Prisma.StudentCreateWithoutSchoolInput,
        Prisma.StudentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.StudentCreateWithoutSchoolInput[]
    | Prisma.StudentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.StudentCreateOrConnectWithoutSchoolInput
    | Prisma.StudentCreateOrConnectWithoutSchoolInput[]
  createMany?: Prisma.StudentCreateManySchoolInputEnvelope
  connect?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
}

export type StudentUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.StudentCreateWithoutSchoolInput,
        Prisma.StudentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.StudentCreateWithoutSchoolInput[]
    | Prisma.StudentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.StudentCreateOrConnectWithoutSchoolInput
    | Prisma.StudentCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.StudentUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.StudentUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.StudentCreateManySchoolInputEnvelope
  set?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  disconnect?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  delete?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  connect?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  update?:
    | Prisma.StudentUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.StudentUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.StudentUpdateManyWithWhereWithoutSchoolInput
    | Prisma.StudentUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.StudentScalarWhereInput | Prisma.StudentScalarWhereInput[]
}

export type StudentUncheckedUpdateManyWithoutSchoolNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.StudentCreateWithoutSchoolInput,
        Prisma.StudentUncheckedCreateWithoutSchoolInput
      >
    | Prisma.StudentCreateWithoutSchoolInput[]
    | Prisma.StudentUncheckedCreateWithoutSchoolInput[]
  connectOrCreate?:
    | Prisma.StudentCreateOrConnectWithoutSchoolInput
    | Prisma.StudentCreateOrConnectWithoutSchoolInput[]
  upsert?:
    | Prisma.StudentUpsertWithWhereUniqueWithoutSchoolInput
    | Prisma.StudentUpsertWithWhereUniqueWithoutSchoolInput[]
  createMany?: Prisma.StudentCreateManySchoolInputEnvelope
  set?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  disconnect?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  delete?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  connect?: Prisma.StudentWhereUniqueInput | Prisma.StudentWhereUniqueInput[]
  update?:
    | Prisma.StudentUpdateWithWhereUniqueWithoutSchoolInput
    | Prisma.StudentUpdateWithWhereUniqueWithoutSchoolInput[]
  updateMany?:
    | Prisma.StudentUpdateManyWithWhereWithoutSchoolInput
    | Prisma.StudentUpdateManyWithWhereWithoutSchoolInput[]
  deleteMany?: Prisma.StudentScalarWhereInput | Prisma.StudentScalarWhereInput[]
}

export type StudentCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.StudentCreateWithoutUserInput,
    Prisma.StudentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StudentCreateOrConnectWithoutUserInput
  connect?: Prisma.StudentWhereUniqueInput
}

export type StudentUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.StudentCreateWithoutUserInput,
    Prisma.StudentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StudentCreateOrConnectWithoutUserInput
  connect?: Prisma.StudentWhereUniqueInput
}

export type StudentUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.StudentCreateWithoutUserInput,
    Prisma.StudentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StudentCreateOrConnectWithoutUserInput
  upsert?: Prisma.StudentUpsertWithoutUserInput
  disconnect?: Prisma.StudentWhereInput | boolean
  delete?: Prisma.StudentWhereInput | boolean
  connect?: Prisma.StudentWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.StudentUpdateToOneWithWhereWithoutUserInput,
      Prisma.StudentUpdateWithoutUserInput
    >,
    Prisma.StudentUncheckedUpdateWithoutUserInput
  >
}

export type StudentUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.StudentCreateWithoutUserInput,
    Prisma.StudentUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.StudentCreateOrConnectWithoutUserInput
  upsert?: Prisma.StudentUpsertWithoutUserInput
  disconnect?: Prisma.StudentWhereInput | boolean
  delete?: Prisma.StudentWhereInput | boolean
  connect?: Prisma.StudentWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.StudentUpdateToOneWithWhereWithoutUserInput,
      Prisma.StudentUpdateWithoutUserInput
    >,
    Prisma.StudentUncheckedUpdateWithoutUserInput
  >
}

export type StudentCreateWithoutSchoolInput = {
  id?: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
  user: Prisma.UserCreateNestedOneWithoutStudentProfileInput
}

export type StudentUncheckedCreateWithoutSchoolInput = {
  id?: string
  userId: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
}

export type StudentCreateOrConnectWithoutSchoolInput = {
  where: Prisma.StudentWhereUniqueInput
  create: Prisma.XOR<
    Prisma.StudentCreateWithoutSchoolInput,
    Prisma.StudentUncheckedCreateWithoutSchoolInput
  >
}

export type StudentCreateManySchoolInputEnvelope = {
  data: Prisma.StudentCreateManySchoolInput | Prisma.StudentCreateManySchoolInput[]
  skipDuplicates?: boolean
}

export type StudentUpsertWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.StudentWhereUniqueInput
  update: Prisma.XOR<
    Prisma.StudentUpdateWithoutSchoolInput,
    Prisma.StudentUncheckedUpdateWithoutSchoolInput
  >
  create: Prisma.XOR<
    Prisma.StudentCreateWithoutSchoolInput,
    Prisma.StudentUncheckedCreateWithoutSchoolInput
  >
}

export type StudentUpdateWithWhereUniqueWithoutSchoolInput = {
  where: Prisma.StudentWhereUniqueInput
  data: Prisma.XOR<
    Prisma.StudentUpdateWithoutSchoolInput,
    Prisma.StudentUncheckedUpdateWithoutSchoolInput
  >
}

export type StudentUpdateManyWithWhereWithoutSchoolInput = {
  where: Prisma.StudentScalarWhereInput
  data: Prisma.XOR<
    Prisma.StudentUpdateManyMutationInput,
    Prisma.StudentUncheckedUpdateManyWithoutSchoolInput
  >
}

export type StudentScalarWhereInput = {
  AND?: Prisma.StudentScalarWhereInput | Prisma.StudentScalarWhereInput[]
  OR?: Prisma.StudentScalarWhereInput[]
  NOT?: Prisma.StudentScalarWhereInput | Prisma.StudentScalarWhereInput[]
  id?: Prisma.StringFilter<'Student'> | string
  userId?: Prisma.StringFilter<'Student'> | string
  studentId?: Prisma.StringFilter<'Student'> | string
  emergencyContact?: Prisma.StringNullableFilter<'Student'> | string | null
  enrollmentDate?: Prisma.DateTimeFilter<'Student'> | Date | string
  schoolId?: Prisma.StringNullableFilter<'Student'> | string | null
}

export type StudentCreateWithoutUserInput = {
  id?: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
  school?: Prisma.SchoolCreateNestedOneWithoutStudentsInput
}

export type StudentUncheckedCreateWithoutUserInput = {
  id?: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
  schoolId?: string | null
}

export type StudentCreateOrConnectWithoutUserInput = {
  where: Prisma.StudentWhereUniqueInput
  create: Prisma.XOR<
    Prisma.StudentCreateWithoutUserInput,
    Prisma.StudentUncheckedCreateWithoutUserInput
  >
}

export type StudentUpsertWithoutUserInput = {
  update: Prisma.XOR<
    Prisma.StudentUpdateWithoutUserInput,
    Prisma.StudentUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.StudentCreateWithoutUserInput,
    Prisma.StudentUncheckedCreateWithoutUserInput
  >
  where?: Prisma.StudentWhereInput
}

export type StudentUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.StudentWhereInput
  data: Prisma.XOR<
    Prisma.StudentUpdateWithoutUserInput,
    Prisma.StudentUncheckedUpdateWithoutUserInput
  >
}

export type StudentUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  school?: Prisma.SchoolUpdateOneWithoutStudentsNestedInput
}

export type StudentUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  schoolId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type StudentCreateManySchoolInput = {
  id?: string
  userId: string
  studentId: string
  emergencyContact?: string | null
  enrollmentDate?: Date | string
}

export type StudentUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutStudentProfileNestedInput
}

export type StudentUncheckedUpdateWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StudentUncheckedUpdateManyWithoutSchoolInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  studentId?: Prisma.StringFieldUpdateOperationsInput | string
  emergencyContact?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  enrollmentDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StudentSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    studentId?: boolean
    emergencyContact?: boolean
    enrollmentDate?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.Student$schoolArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['student']
>

export type StudentSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    studentId?: boolean
    emergencyContact?: boolean
    enrollmentDate?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.Student$schoolArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['student']
>

export type StudentSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    studentId?: boolean
    emergencyContact?: boolean
    enrollmentDate?: boolean
    schoolId?: boolean
    school?: boolean | Prisma.Student$schoolArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['student']
>

export type StudentSelectScalar = {
  id?: boolean
  userId?: boolean
  studentId?: boolean
  emergencyContact?: boolean
  enrollmentDate?: boolean
  schoolId?: boolean
}

export type StudentOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'userId' | 'studentId' | 'emergencyContact' | 'enrollmentDate' | 'schoolId',
  ExtArgs['result']['student']
>
export type StudentInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.Student$schoolArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type StudentIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.Student$schoolArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type StudentIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  school?: boolean | Prisma.Student$schoolArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $StudentPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Student'
  objects: {
    school: Prisma.$SchoolPayload<ExtArgs> | null
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      userId: string
      studentId: string
      emergencyContact: string | null
      enrollmentDate: Date
      schoolId: string | null
    },
    ExtArgs['result']['student']
  >
  composites: {}
}

export type StudentGetPayload<S extends boolean | null | undefined | StudentDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$StudentPayload, S>

export type StudentCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<StudentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: StudentCountAggregateInputType | true
}

export interface StudentDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Student']; meta: { name: 'Student' } }
  /**
   * Find zero or one Student that matches the filter.
   * @param {StudentFindUniqueArgs} args - Arguments to find a Student
   * @example
   * // Get one Student
   * const student = await prisma.student.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends StudentFindUniqueArgs>(
    args: Prisma.SelectSubset<T, StudentFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Student that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {StudentFindUniqueOrThrowArgs} args - Arguments to find a Student
   * @example
   * // Get one Student
   * const student = await prisma.student.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends StudentFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, StudentFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Student that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StudentFindFirstArgs} args - Arguments to find a Student
   * @example
   * // Get one Student
   * const student = await prisma.student.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends StudentFindFirstArgs>(
    args?: Prisma.SelectSubset<T, StudentFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Student that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StudentFindFirstOrThrowArgs} args - Arguments to find a Student
   * @example
   * // Get one Student
   * const student = await prisma.student.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends StudentFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, StudentFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Students that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StudentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Students
   * const students = await prisma.student.findMany()
   *
   * // Get first 10 Students
   * const students = await prisma.student.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const studentWithIdOnly = await prisma.student.findMany({ select: { id: true } })
   *
   */
  findMany<T extends StudentFindManyArgs>(
    args?: Prisma.SelectSubset<T, StudentFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a Student.
   * @param {StudentCreateArgs} args - Arguments to create a Student.
   * @example
   * // Create one Student
   * const Student = await prisma.student.create({
   *   data: {
   *     // ... data to create a Student
   *   }
   * })
   *
   */
  create<T extends StudentCreateArgs>(
    args: Prisma.SelectSubset<T, StudentCreateArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Students.
   * @param {StudentCreateManyArgs} args - Arguments to create many Students.
   * @example
   * // Create many Students
   * const student = await prisma.student.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends StudentCreateManyArgs>(
    args?: Prisma.SelectSubset<T, StudentCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Students and returns the data saved in the database.
   * @param {StudentCreateManyAndReturnArgs} args - Arguments to create many Students.
   * @example
   * // Create many Students
   * const student = await prisma.student.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Students and only return the `id`
   * const studentWithIdOnly = await prisma.student.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends StudentCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, StudentCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Student.
   * @param {StudentDeleteArgs} args - Arguments to delete one Student.
   * @example
   * // Delete one Student
   * const Student = await prisma.student.delete({
   *   where: {
   *     // ... filter to delete one Student
   *   }
   * })
   *
   */
  delete<T extends StudentDeleteArgs>(
    args: Prisma.SelectSubset<T, StudentDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Student.
   * @param {StudentUpdateArgs} args - Arguments to update one Student.
   * @example
   * // Update one Student
   * const student = await prisma.student.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends StudentUpdateArgs>(
    args: Prisma.SelectSubset<T, StudentUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Students.
   * @param {StudentDeleteManyArgs} args - Arguments to filter Students to delete.
   * @example
   * // Delete a few Students
   * const { count } = await prisma.student.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends StudentDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, StudentDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Students.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StudentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Students
   * const student = await prisma.student.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends StudentUpdateManyArgs>(
    args: Prisma.SelectSubset<T, StudentUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Students and returns the data updated in the database.
   * @param {StudentUpdateManyAndReturnArgs} args - Arguments to update many Students.
   * @example
   * // Update many Students
   * const student = await prisma.student.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Students and only return the `id`
   * const studentWithIdOnly = await prisma.student.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends StudentUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, StudentUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Student.
   * @param {StudentUpsertArgs} args - Arguments to update or create a Student.
   * @example
   * // Update or create a Student
   * const student = await prisma.student.upsert({
   *   create: {
   *     // ... data to create a Student
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Student we want to update
   *   }
   * })
   */
  upsert<T extends StudentUpsertArgs>(
    args: Prisma.SelectSubset<T, StudentUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Students.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StudentCountArgs} args - Arguments to filter Students to count.
   * @example
   * // Count the number of Students
   * const count = await prisma.student.count({
   *   where: {
   *     // ... the filter for the Students we want to count
   *   }
   * })
   **/
  count<T extends StudentCountArgs>(
    args?: Prisma.Subset<T, StudentCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], StudentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Student.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StudentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends StudentAggregateArgs>(
    args: Prisma.Subset<T, StudentAggregateArgs>
  ): Prisma.PrismaPromise<GetStudentAggregateType<T>>

  /**
   * Group by Student.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StudentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends StudentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: StudentGroupByArgs['orderBy'] }
      : { orderBy?: StudentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, StudentGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetStudentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Student model
   */
  readonly fields: StudentFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Student.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__StudentClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  school<T extends Prisma.Student$schoolArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.Student$schoolArgs<ExtArgs>>
  ): Prisma.Prisma__SchoolClient<
    runtime.Types.Result.GetResult<
      Prisma.$SchoolPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Student model
 */
export interface StudentFieldRefs {
  readonly id: Prisma.FieldRef<'Student', 'String'>
  readonly userId: Prisma.FieldRef<'Student', 'String'>
  readonly studentId: Prisma.FieldRef<'Student', 'String'>
  readonly emergencyContact: Prisma.FieldRef<'Student', 'String'>
  readonly enrollmentDate: Prisma.FieldRef<'Student', 'DateTime'>
  readonly schoolId: Prisma.FieldRef<'Student', 'String'>
}

// Custom InputTypes
/**
 * Student findUnique
 */
export type StudentFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * Filter, which Student to fetch.
   */
  where: Prisma.StudentWhereUniqueInput
}

/**
 * Student findUniqueOrThrow
 */
export type StudentFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * Filter, which Student to fetch.
   */
  where: Prisma.StudentWhereUniqueInput
}

/**
 * Student findFirst
 */
export type StudentFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * Filter, which Student to fetch.
   */
  where?: Prisma.StudentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Students to fetch.
   */
  orderBy?: Prisma.StudentOrderByWithRelationInput | Prisma.StudentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Students.
   */
  cursor?: Prisma.StudentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Students from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Students.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Students.
   */
  distinct?: Prisma.StudentScalarFieldEnum | Prisma.StudentScalarFieldEnum[]
}

/**
 * Student findFirstOrThrow
 */
export type StudentFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * Filter, which Student to fetch.
   */
  where?: Prisma.StudentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Students to fetch.
   */
  orderBy?: Prisma.StudentOrderByWithRelationInput | Prisma.StudentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Students.
   */
  cursor?: Prisma.StudentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Students from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Students.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Students.
   */
  distinct?: Prisma.StudentScalarFieldEnum | Prisma.StudentScalarFieldEnum[]
}

/**
 * Student findMany
 */
export type StudentFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * Filter, which Students to fetch.
   */
  where?: Prisma.StudentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Students to fetch.
   */
  orderBy?: Prisma.StudentOrderByWithRelationInput | Prisma.StudentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Students.
   */
  cursor?: Prisma.StudentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Students from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Students.
   */
  skip?: number
  distinct?: Prisma.StudentScalarFieldEnum | Prisma.StudentScalarFieldEnum[]
}

/**
 * Student create
 */
export type StudentCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * The data needed to create a Student.
   */
  data: Prisma.XOR<Prisma.StudentCreateInput, Prisma.StudentUncheckedCreateInput>
}

/**
 * Student createMany
 */
export type StudentCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Students.
   */
  data: Prisma.StudentCreateManyInput | Prisma.StudentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Student createManyAndReturn
 */
export type StudentCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * The data used to create many Students.
   */
  data: Prisma.StudentCreateManyInput | Prisma.StudentCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Student update
 */
export type StudentUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * The data needed to update a Student.
   */
  data: Prisma.XOR<Prisma.StudentUpdateInput, Prisma.StudentUncheckedUpdateInput>
  /**
   * Choose, which Student to update.
   */
  where: Prisma.StudentWhereUniqueInput
}

/**
 * Student updateMany
 */
export type StudentUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Students.
   */
  data: Prisma.XOR<Prisma.StudentUpdateManyMutationInput, Prisma.StudentUncheckedUpdateManyInput>
  /**
   * Filter which Students to update
   */
  where?: Prisma.StudentWhereInput
  /**
   * Limit how many Students to update.
   */
  limit?: number
}

/**
 * Student updateManyAndReturn
 */
export type StudentUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * The data used to update Students.
   */
  data: Prisma.XOR<Prisma.StudentUpdateManyMutationInput, Prisma.StudentUncheckedUpdateManyInput>
  /**
   * Filter which Students to update
   */
  where?: Prisma.StudentWhereInput
  /**
   * Limit how many Students to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Student upsert
 */
export type StudentUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * The filter to search for the Student to update in case it exists.
   */
  where: Prisma.StudentWhereUniqueInput
  /**
   * In case the Student found by the `where` argument doesn't exist, create a new Student with this data.
   */
  create: Prisma.XOR<Prisma.StudentCreateInput, Prisma.StudentUncheckedCreateInput>
  /**
   * In case the Student was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.StudentUpdateInput, Prisma.StudentUncheckedUpdateInput>
}

/**
 * Student delete
 */
export type StudentDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  /**
   * Filter which Student to delete.
   */
  where: Prisma.StudentWhereUniqueInput
}

/**
 * Student deleteMany
 */
export type StudentDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Students to delete
   */
  where?: Prisma.StudentWhereInput
  /**
   * Limit how many Students to delete.
   */
  limit?: number
}

/**
 * Student.school
 */
export type Student$schoolArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the School
   */
  select?: Prisma.SchoolSelect<ExtArgs> | null
  /**
   * Omit specific fields from the School
   */
  omit?: Prisma.SchoolOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolInclude<ExtArgs> | null
  where?: Prisma.SchoolWhereInput
}

/**
 * Student without action
 */
export type StudentDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
}
