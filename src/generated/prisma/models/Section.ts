/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Section` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Section
 *
 */
export type SectionModel = runtime.Types.Result.DefaultSelection<Prisma.$SectionPayload>

export type AggregateSection = {
  _count: SectionCountAggregateOutputType | null
  _min: SectionMinAggregateOutputType | null
  _max: SectionMaxAggregateOutputType | null
}

export type SectionMinAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  createdAt: Date | null
  updatedAt: Date | null
  classId: string | null
}

export type SectionMaxAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  createdAt: Date | null
  updatedAt: Date | null
  classId: string | null
}

export type SectionCountAggregateOutputType = {
  id: number
  name: number
  description: number
  createdAt: number
  updatedAt: number
  classId: number
  _all: number
}

export type SectionMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  classId?: true
}

export type SectionMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  classId?: true
}

export type SectionCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  createdAt?: true
  updatedAt?: true
  classId?: true
  _all?: true
}

export type SectionAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Section to aggregate.
   */
  where?: Prisma.SectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Sections to fetch.
   */
  orderBy?: Prisma.SectionOrderByWithRelationInput | Prisma.SectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.SectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Sections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Sections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Sections
   **/
  _count?: true | SectionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: SectionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: SectionMaxAggregateInputType
}

export type GetSectionAggregateType<T extends SectionAggregateArgs> = {
  [P in keyof T & keyof AggregateSection]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSection[P]>
    : Prisma.GetScalarType<T[P], AggregateSection[P]>
}

export type SectionGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SectionWhereInput
  orderBy?: Prisma.SectionOrderByWithAggregationInput | Prisma.SectionOrderByWithAggregationInput[]
  by: Prisma.SectionScalarFieldEnum[] | Prisma.SectionScalarFieldEnum
  having?: Prisma.SectionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SectionCountAggregateInputType | true
  _min?: SectionMinAggregateInputType
  _max?: SectionMaxAggregateInputType
}

export type SectionGroupByOutputType = {
  id: string
  name: string
  description: string
  createdAt: Date
  updatedAt: Date
  classId: string
  _count: SectionCountAggregateOutputType | null
  _min: SectionMinAggregateOutputType | null
  _max: SectionMaxAggregateOutputType | null
}

type GetSectionGroupByPayload<T extends SectionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SectionGroupByOutputType, T['by']> & {
      [P in keyof T & keyof SectionGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], SectionGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], SectionGroupByOutputType[P]>
    }
  >
>

export type SectionWhereInput = {
  AND?: Prisma.SectionWhereInput | Prisma.SectionWhereInput[]
  OR?: Prisma.SectionWhereInput[]
  NOT?: Prisma.SectionWhereInput | Prisma.SectionWhereInput[]
  id?: Prisma.StringFilter<'Section'> | string
  name?: Prisma.StringFilter<'Section'> | string
  description?: Prisma.StringFilter<'Section'> | string
  createdAt?: Prisma.DateTimeFilter<'Section'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Section'> | Date | string
  classId?: Prisma.StringFilter<'Section'> | string
  class?: Prisma.XOR<Prisma.ClassScalarRelationFilter, Prisma.ClassWhereInput>
}

export type SectionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  classId?: Prisma.SortOrder
  class?: Prisma.ClassOrderByWithRelationInput
}

export type SectionWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    AND?: Prisma.SectionWhereInput | Prisma.SectionWhereInput[]
    OR?: Prisma.SectionWhereInput[]
    NOT?: Prisma.SectionWhereInput | Prisma.SectionWhereInput[]
    name?: Prisma.StringFilter<'Section'> | string
    description?: Prisma.StringFilter<'Section'> | string
    createdAt?: Prisma.DateTimeFilter<'Section'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'Section'> | Date | string
    classId?: Prisma.StringFilter<'Section'> | string
    class?: Prisma.XOR<Prisma.ClassScalarRelationFilter, Prisma.ClassWhereInput>
  },
  'id'
>

export type SectionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  classId?: Prisma.SortOrder
  _count?: Prisma.SectionCountOrderByAggregateInput
  _max?: Prisma.SectionMaxOrderByAggregateInput
  _min?: Prisma.SectionMinOrderByAggregateInput
}

export type SectionScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.SectionScalarWhereWithAggregatesInput
    | Prisma.SectionScalarWhereWithAggregatesInput[]
  OR?: Prisma.SectionScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.SectionScalarWhereWithAggregatesInput
    | Prisma.SectionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Section'> | string
  name?: Prisma.StringWithAggregatesFilter<'Section'> | string
  description?: Prisma.StringWithAggregatesFilter<'Section'> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'Section'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'Section'> | Date | string
  classId?: Prisma.StringWithAggregatesFilter<'Section'> | string
}

export type SectionCreateInput = {
  id?: string
  name: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  class: Prisma.ClassCreateNestedOneWithoutSectionsInput
}

export type SectionUncheckedCreateInput = {
  id?: string
  name: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  classId: string
}

export type SectionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  class?: Prisma.ClassUpdateOneRequiredWithoutSectionsNestedInput
}

export type SectionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  classId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SectionCreateManyInput = {
  id?: string
  name: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
  classId: string
}

export type SectionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SectionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  classId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SectionListRelationFilter = {
  every?: Prisma.SectionWhereInput
  some?: Prisma.SectionWhereInput
  none?: Prisma.SectionWhereInput
}

export type SectionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SectionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  classId?: Prisma.SortOrder
}

export type SectionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  classId?: Prisma.SortOrder
}

export type SectionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  classId?: Prisma.SortOrder
}

export type SectionCreateNestedManyWithoutClassInput = {
  create?:
    | Prisma.XOR<
        Prisma.SectionCreateWithoutClassInput,
        Prisma.SectionUncheckedCreateWithoutClassInput
      >
    | Prisma.SectionCreateWithoutClassInput[]
    | Prisma.SectionUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SectionCreateOrConnectWithoutClassInput
    | Prisma.SectionCreateOrConnectWithoutClassInput[]
  createMany?: Prisma.SectionCreateManyClassInputEnvelope
  connect?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
}

export type SectionUncheckedCreateNestedManyWithoutClassInput = {
  create?:
    | Prisma.XOR<
        Prisma.SectionCreateWithoutClassInput,
        Prisma.SectionUncheckedCreateWithoutClassInput
      >
    | Prisma.SectionCreateWithoutClassInput[]
    | Prisma.SectionUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SectionCreateOrConnectWithoutClassInput
    | Prisma.SectionCreateOrConnectWithoutClassInput[]
  createMany?: Prisma.SectionCreateManyClassInputEnvelope
  connect?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
}

export type SectionUpdateManyWithoutClassNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SectionCreateWithoutClassInput,
        Prisma.SectionUncheckedCreateWithoutClassInput
      >
    | Prisma.SectionCreateWithoutClassInput[]
    | Prisma.SectionUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SectionCreateOrConnectWithoutClassInput
    | Prisma.SectionCreateOrConnectWithoutClassInput[]
  upsert?:
    | Prisma.SectionUpsertWithWhereUniqueWithoutClassInput
    | Prisma.SectionUpsertWithWhereUniqueWithoutClassInput[]
  createMany?: Prisma.SectionCreateManyClassInputEnvelope
  set?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  disconnect?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  delete?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  connect?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  update?:
    | Prisma.SectionUpdateWithWhereUniqueWithoutClassInput
    | Prisma.SectionUpdateWithWhereUniqueWithoutClassInput[]
  updateMany?:
    | Prisma.SectionUpdateManyWithWhereWithoutClassInput
    | Prisma.SectionUpdateManyWithWhereWithoutClassInput[]
  deleteMany?: Prisma.SectionScalarWhereInput | Prisma.SectionScalarWhereInput[]
}

export type SectionUncheckedUpdateManyWithoutClassNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SectionCreateWithoutClassInput,
        Prisma.SectionUncheckedCreateWithoutClassInput
      >
    | Prisma.SectionCreateWithoutClassInput[]
    | Prisma.SectionUncheckedCreateWithoutClassInput[]
  connectOrCreate?:
    | Prisma.SectionCreateOrConnectWithoutClassInput
    | Prisma.SectionCreateOrConnectWithoutClassInput[]
  upsert?:
    | Prisma.SectionUpsertWithWhereUniqueWithoutClassInput
    | Prisma.SectionUpsertWithWhereUniqueWithoutClassInput[]
  createMany?: Prisma.SectionCreateManyClassInputEnvelope
  set?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  disconnect?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  delete?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  connect?: Prisma.SectionWhereUniqueInput | Prisma.SectionWhereUniqueInput[]
  update?:
    | Prisma.SectionUpdateWithWhereUniqueWithoutClassInput
    | Prisma.SectionUpdateWithWhereUniqueWithoutClassInput[]
  updateMany?:
    | Prisma.SectionUpdateManyWithWhereWithoutClassInput
    | Prisma.SectionUpdateManyWithWhereWithoutClassInput[]
  deleteMany?: Prisma.SectionScalarWhereInput | Prisma.SectionScalarWhereInput[]
}

export type SectionCreateWithoutClassInput = {
  id?: string
  name: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SectionUncheckedCreateWithoutClassInput = {
  id?: string
  name: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SectionCreateOrConnectWithoutClassInput = {
  where: Prisma.SectionWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SectionCreateWithoutClassInput,
    Prisma.SectionUncheckedCreateWithoutClassInput
  >
}

export type SectionCreateManyClassInputEnvelope = {
  data: Prisma.SectionCreateManyClassInput | Prisma.SectionCreateManyClassInput[]
  skipDuplicates?: boolean
}

export type SectionUpsertWithWhereUniqueWithoutClassInput = {
  where: Prisma.SectionWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SectionUpdateWithoutClassInput,
    Prisma.SectionUncheckedUpdateWithoutClassInput
  >
  create: Prisma.XOR<
    Prisma.SectionCreateWithoutClassInput,
    Prisma.SectionUncheckedCreateWithoutClassInput
  >
}

export type SectionUpdateWithWhereUniqueWithoutClassInput = {
  where: Prisma.SectionWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SectionUpdateWithoutClassInput,
    Prisma.SectionUncheckedUpdateWithoutClassInput
  >
}

export type SectionUpdateManyWithWhereWithoutClassInput = {
  where: Prisma.SectionScalarWhereInput
  data: Prisma.XOR<
    Prisma.SectionUpdateManyMutationInput,
    Prisma.SectionUncheckedUpdateManyWithoutClassInput
  >
}

export type SectionScalarWhereInput = {
  AND?: Prisma.SectionScalarWhereInput | Prisma.SectionScalarWhereInput[]
  OR?: Prisma.SectionScalarWhereInput[]
  NOT?: Prisma.SectionScalarWhereInput | Prisma.SectionScalarWhereInput[]
  id?: Prisma.StringFilter<'Section'> | string
  name?: Prisma.StringFilter<'Section'> | string
  description?: Prisma.StringFilter<'Section'> | string
  createdAt?: Prisma.DateTimeFilter<'Section'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'Section'> | Date | string
  classId?: Prisma.StringFilter<'Section'> | string
}

export type SectionCreateManyClassInput = {
  id?: string
  name: string
  description: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SectionUpdateWithoutClassInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SectionUncheckedUpdateWithoutClassInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SectionUncheckedUpdateManyWithoutClassInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SectionSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    classId?: boolean
    class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['section']
>

export type SectionSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    classId?: boolean
    class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['section']
>

export type SectionSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    name?: boolean
    description?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    classId?: boolean
    class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['section']
>

export type SectionSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  classId?: boolean
}

export type SectionOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'name' | 'description' | 'createdAt' | 'updatedAt' | 'classId',
  ExtArgs['result']['section']
>
export type SectionInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
}
export type SectionIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
}
export type SectionIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  class?: boolean | Prisma.ClassDefaultArgs<ExtArgs>
}

export type $SectionPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Section'
  objects: {
    class: Prisma.$ClassPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      name: string
      description: string
      createdAt: Date
      updatedAt: Date
      classId: string
    },
    ExtArgs['result']['section']
  >
  composites: {}
}

export type SectionGetPayload<S extends boolean | null | undefined | SectionDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$SectionPayload, S>

export type SectionCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<SectionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: SectionCountAggregateInputType | true
}

export interface SectionDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Section']; meta: { name: 'Section' } }
  /**
   * Find zero or one Section that matches the filter.
   * @param {SectionFindUniqueArgs} args - Arguments to find a Section
   * @example
   * // Get one Section
   * const section = await prisma.section.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SectionFindUniqueArgs>(
    args: Prisma.SelectSubset<T, SectionFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<
      Prisma.$SectionPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Section that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SectionFindUniqueOrThrowArgs} args - Arguments to find a Section
   * @example
   * // Get one Section
   * const section = await prisma.section.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SectionFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, SectionFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<
      Prisma.$SectionPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Section that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SectionFindFirstArgs} args - Arguments to find a Section
   * @example
   * // Get one Section
   * const section = await prisma.section.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SectionFindFirstArgs>(
    args?: Prisma.SelectSubset<T, SectionFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<
      Prisma.$SectionPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Section that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SectionFindFirstOrThrowArgs} args - Arguments to find a Section
   * @example
   * // Get one Section
   * const section = await prisma.section.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SectionFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, SectionFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<
      Prisma.$SectionPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Sections that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SectionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Sections
   * const sections = await prisma.section.findMany()
   *
   * // Get first 10 Sections
   * const sections = await prisma.section.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const sectionWithIdOnly = await prisma.section.findMany({ select: { id: true } })
   *
   */
  findMany<T extends SectionFindManyArgs>(
    args?: Prisma.SelectSubset<T, SectionFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SectionPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a Section.
   * @param {SectionCreateArgs} args - Arguments to create a Section.
   * @example
   * // Create one Section
   * const Section = await prisma.section.create({
   *   data: {
   *     // ... data to create a Section
   *   }
   * })
   *
   */
  create<T extends SectionCreateArgs>(
    args: Prisma.SelectSubset<T, SectionCreateArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<Prisma.$SectionPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Sections.
   * @param {SectionCreateManyArgs} args - Arguments to create many Sections.
   * @example
   * // Create many Sections
   * const section = await prisma.section.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends SectionCreateManyArgs>(
    args?: Prisma.SelectSubset<T, SectionCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Sections and returns the data saved in the database.
   * @param {SectionCreateManyAndReturnArgs} args - Arguments to create many Sections.
   * @example
   * // Create many Sections
   * const section = await prisma.section.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Sections and only return the `id`
   * const sectionWithIdOnly = await prisma.section.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends SectionCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, SectionCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SectionPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Section.
   * @param {SectionDeleteArgs} args - Arguments to delete one Section.
   * @example
   * // Delete one Section
   * const Section = await prisma.section.delete({
   *   where: {
   *     // ... filter to delete one Section
   *   }
   * })
   *
   */
  delete<T extends SectionDeleteArgs>(
    args: Prisma.SelectSubset<T, SectionDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<Prisma.$SectionPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Section.
   * @param {SectionUpdateArgs} args - Arguments to update one Section.
   * @example
   * // Update one Section
   * const section = await prisma.section.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends SectionUpdateArgs>(
    args: Prisma.SelectSubset<T, SectionUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<Prisma.$SectionPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Sections.
   * @param {SectionDeleteManyArgs} args - Arguments to filter Sections to delete.
   * @example
   * // Delete a few Sections
   * const { count } = await prisma.section.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends SectionDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, SectionDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Sections.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SectionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Sections
   * const section = await prisma.section.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends SectionUpdateManyArgs>(
    args: Prisma.SelectSubset<T, SectionUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Sections and returns the data updated in the database.
   * @param {SectionUpdateManyAndReturnArgs} args - Arguments to update many Sections.
   * @example
   * // Update many Sections
   * const section = await prisma.section.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Sections and only return the `id`
   * const sectionWithIdOnly = await prisma.section.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends SectionUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, SectionUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SectionPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Section.
   * @param {SectionUpsertArgs} args - Arguments to update or create a Section.
   * @example
   * // Update or create a Section
   * const section = await prisma.section.upsert({
   *   create: {
   *     // ... data to create a Section
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Section we want to update
   *   }
   * })
   */
  upsert<T extends SectionUpsertArgs>(
    args: Prisma.SelectSubset<T, SectionUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__SectionClient<
    runtime.Types.Result.GetResult<Prisma.$SectionPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Sections.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SectionCountArgs} args - Arguments to filter Sections to count.
   * @example
   * // Count the number of Sections
   * const count = await prisma.section.count({
   *   where: {
   *     // ... the filter for the Sections we want to count
   *   }
   * })
   **/
  count<T extends SectionCountArgs>(
    args?: Prisma.Subset<T, SectionCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SectionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Section.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SectionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends SectionAggregateArgs>(
    args: Prisma.Subset<T, SectionAggregateArgs>
  ): Prisma.PrismaPromise<GetSectionAggregateType<T>>

  /**
   * Group by Section.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SectionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends SectionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SectionGroupByArgs['orderBy'] }
      : { orderBy?: SectionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, SectionGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetSectionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Section model
   */
  readonly fields: SectionFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Section.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SectionClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  class<T extends Prisma.ClassDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.ClassDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__ClassClient<
    | runtime.Types.Result.GetResult<
        Prisma.$ClassPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Section model
 */
export interface SectionFieldRefs {
  readonly id: Prisma.FieldRef<'Section', 'String'>
  readonly name: Prisma.FieldRef<'Section', 'String'>
  readonly description: Prisma.FieldRef<'Section', 'String'>
  readonly createdAt: Prisma.FieldRef<'Section', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'Section', 'DateTime'>
  readonly classId: Prisma.FieldRef<'Section', 'String'>
}

// Custom InputTypes
/**
 * Section findUnique
 */
export type SectionFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * Filter, which Section to fetch.
   */
  where: Prisma.SectionWhereUniqueInput
}

/**
 * Section findUniqueOrThrow
 */
export type SectionFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * Filter, which Section to fetch.
   */
  where: Prisma.SectionWhereUniqueInput
}

/**
 * Section findFirst
 */
export type SectionFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * Filter, which Section to fetch.
   */
  where?: Prisma.SectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Sections to fetch.
   */
  orderBy?: Prisma.SectionOrderByWithRelationInput | Prisma.SectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Sections.
   */
  cursor?: Prisma.SectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Sections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Sections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Sections.
   */
  distinct?: Prisma.SectionScalarFieldEnum | Prisma.SectionScalarFieldEnum[]
}

/**
 * Section findFirstOrThrow
 */
export type SectionFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * Filter, which Section to fetch.
   */
  where?: Prisma.SectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Sections to fetch.
   */
  orderBy?: Prisma.SectionOrderByWithRelationInput | Prisma.SectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Sections.
   */
  cursor?: Prisma.SectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Sections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Sections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Sections.
   */
  distinct?: Prisma.SectionScalarFieldEnum | Prisma.SectionScalarFieldEnum[]
}

/**
 * Section findMany
 */
export type SectionFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * Filter, which Sections to fetch.
   */
  where?: Prisma.SectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Sections to fetch.
   */
  orderBy?: Prisma.SectionOrderByWithRelationInput | Prisma.SectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Sections.
   */
  cursor?: Prisma.SectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Sections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Sections.
   */
  skip?: number
  distinct?: Prisma.SectionScalarFieldEnum | Prisma.SectionScalarFieldEnum[]
}

/**
 * Section create
 */
export type SectionCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * The data needed to create a Section.
   */
  data: Prisma.XOR<Prisma.SectionCreateInput, Prisma.SectionUncheckedCreateInput>
}

/**
 * Section createMany
 */
export type SectionCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Sections.
   */
  data: Prisma.SectionCreateManyInput | Prisma.SectionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Section createManyAndReturn
 */
export type SectionCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * The data used to create many Sections.
   */
  data: Prisma.SectionCreateManyInput | Prisma.SectionCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Section update
 */
export type SectionUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * The data needed to update a Section.
   */
  data: Prisma.XOR<Prisma.SectionUpdateInput, Prisma.SectionUncheckedUpdateInput>
  /**
   * Choose, which Section to update.
   */
  where: Prisma.SectionWhereUniqueInput
}

/**
 * Section updateMany
 */
export type SectionUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Sections.
   */
  data: Prisma.XOR<Prisma.SectionUpdateManyMutationInput, Prisma.SectionUncheckedUpdateManyInput>
  /**
   * Filter which Sections to update
   */
  where?: Prisma.SectionWhereInput
  /**
   * Limit how many Sections to update.
   */
  limit?: number
}

/**
 * Section updateManyAndReturn
 */
export type SectionUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * The data used to update Sections.
   */
  data: Prisma.XOR<Prisma.SectionUpdateManyMutationInput, Prisma.SectionUncheckedUpdateManyInput>
  /**
   * Filter which Sections to update
   */
  where?: Prisma.SectionWhereInput
  /**
   * Limit how many Sections to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Section upsert
 */
export type SectionUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * The filter to search for the Section to update in case it exists.
   */
  where: Prisma.SectionWhereUniqueInput
  /**
   * In case the Section found by the `where` argument doesn't exist, create a new Section with this data.
   */
  create: Prisma.XOR<Prisma.SectionCreateInput, Prisma.SectionUncheckedCreateInput>
  /**
   * In case the Section was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SectionUpdateInput, Prisma.SectionUncheckedUpdateInput>
}

/**
 * Section delete
 */
export type SectionDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
  /**
   * Filter which Section to delete.
   */
  where: Prisma.SectionWhereUniqueInput
}

/**
 * Section deleteMany
 */
export type SectionDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Sections to delete
   */
  where?: Prisma.SectionWhereInput
  /**
   * Limit how many Sections to delete.
   */
  limit?: number
}

/**
 * Section without action
 */
export type SectionDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Section
   */
  select?: Prisma.SectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Section
   */
  omit?: Prisma.SectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SectionInclude<ExtArgs> | null
}
