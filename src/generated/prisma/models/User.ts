/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `User` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model User
 *
 */
export type UserModel = runtime.Types.Result.DefaultSelection<Prisma.$UserPayload>

export type AggregateUser = {
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

export type UserMinAggregateOutputType = {
  id: string | null
  email: string | null
  username: string | null
  firstName: string | null
  middleName: string | null
  lastName: string | null
  otherName: string | null
  gender: $Enums.Gender | null
  dateOfBirth: Date | null
  phone: string | null
  otherPhone: string | null
  nationality: string | null
  avatar: string | null
  city: string | null
  barangay: string | null
  cra: string | null
  pobProvince: string | null
  pobCity: string | null
  religiousAffiliation: string | null
  isVerified: boolean | null
  isActive: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  password: string | null
  lastLogin: Date | null
  roleId: string | null
}

export type UserMaxAggregateOutputType = {
  id: string | null
  email: string | null
  username: string | null
  firstName: string | null
  middleName: string | null
  lastName: string | null
  otherName: string | null
  gender: $Enums.Gender | null
  dateOfBirth: Date | null
  phone: string | null
  otherPhone: string | null
  nationality: string | null
  avatar: string | null
  city: string | null
  barangay: string | null
  cra: string | null
  pobProvince: string | null
  pobCity: string | null
  religiousAffiliation: string | null
  isVerified: boolean | null
  isActive: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  password: string | null
  lastLogin: Date | null
  roleId: string | null
}

export type UserCountAggregateOutputType = {
  id: number
  email: number
  username: number
  firstName: number
  middleName: number
  lastName: number
  otherName: number
  gender: number
  dateOfBirth: number
  phone: number
  otherPhone: number
  nationality: number
  avatar: number
  city: number
  barangay: number
  cra: number
  pobProvince: number
  pobCity: number
  religiousAffiliation: number
  isVerified: number
  isActive: number
  createdAt: number
  updatedAt: number
  password: number
  lastLogin: number
  roleId: number
  _all: number
}

export type UserMinAggregateInputType = {
  id?: true
  email?: true
  username?: true
  firstName?: true
  middleName?: true
  lastName?: true
  otherName?: true
  gender?: true
  dateOfBirth?: true
  phone?: true
  otherPhone?: true
  nationality?: true
  avatar?: true
  city?: true
  barangay?: true
  cra?: true
  pobProvince?: true
  pobCity?: true
  religiousAffiliation?: true
  isVerified?: true
  isActive?: true
  createdAt?: true
  updatedAt?: true
  password?: true
  lastLogin?: true
  roleId?: true
}

export type UserMaxAggregateInputType = {
  id?: true
  email?: true
  username?: true
  firstName?: true
  middleName?: true
  lastName?: true
  otherName?: true
  gender?: true
  dateOfBirth?: true
  phone?: true
  otherPhone?: true
  nationality?: true
  avatar?: true
  city?: true
  barangay?: true
  cra?: true
  pobProvince?: true
  pobCity?: true
  religiousAffiliation?: true
  isVerified?: true
  isActive?: true
  createdAt?: true
  updatedAt?: true
  password?: true
  lastLogin?: true
  roleId?: true
}

export type UserCountAggregateInputType = {
  id?: true
  email?: true
  username?: true
  firstName?: true
  middleName?: true
  lastName?: true
  otherName?: true
  gender?: true
  dateOfBirth?: true
  phone?: true
  otherPhone?: true
  nationality?: true
  avatar?: true
  city?: true
  barangay?: true
  cra?: true
  pobProvince?: true
  pobCity?: true
  religiousAffiliation?: true
  isVerified?: true
  isActive?: true
  createdAt?: true
  updatedAt?: true
  password?: true
  lastLogin?: true
  roleId?: true
  _all?: true
}

export type UserAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which User to aggregate.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Users
   **/
  _count?: true | UserCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: UserMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: UserMaxAggregateInputType
}

export type GetUserAggregateType<T extends UserAggregateArgs> = {
  [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser[P]>
    : Prisma.GetScalarType<T[P], AggregateUser[P]>
}

export type UserGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.UserWhereInput
  orderBy?: Prisma.UserOrderByWithAggregationInput | Prisma.UserOrderByWithAggregationInput[]
  by: Prisma.UserScalarFieldEnum[] | Prisma.UserScalarFieldEnum
  having?: Prisma.UserScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UserCountAggregateInputType | true
  _min?: UserMinAggregateInputType
  _max?: UserMaxAggregateInputType
}

export type UserGroupByOutputType = {
  id: string
  email: string
  username: string
  firstName: string
  middleName: string | null
  lastName: string
  otherName: string | null
  gender: $Enums.Gender
  dateOfBirth: Date | null
  phone: string
  otherPhone: string | null
  nationality: string | null
  avatar: string | null
  city: string
  barangay: string | null
  cra: string | null
  pobProvince: string | null
  pobCity: string | null
  religiousAffiliation: string | null
  isVerified: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  password: string
  lastLogin: Date | null
  roleId: string
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UserGroupByOutputType, T['by']> & {
      [P in keyof T & keyof UserGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
    }
  >
>

export type UserWhereInput = {
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  id?: Prisma.StringFilter<'User'> | string
  email?: Prisma.StringFilter<'User'> | string
  username?: Prisma.StringFilter<'User'> | string
  firstName?: Prisma.StringFilter<'User'> | string
  middleName?: Prisma.StringNullableFilter<'User'> | string | null
  lastName?: Prisma.StringFilter<'User'> | string
  otherName?: Prisma.StringNullableFilter<'User'> | string | null
  gender?: Prisma.EnumGenderFilter<'User'> | $Enums.Gender
  dateOfBirth?: Prisma.DateTimeNullableFilter<'User'> | Date | string | null
  phone?: Prisma.StringFilter<'User'> | string
  otherPhone?: Prisma.StringNullableFilter<'User'> | string | null
  nationality?: Prisma.StringNullableFilter<'User'> | string | null
  avatar?: Prisma.StringNullableFilter<'User'> | string | null
  city?: Prisma.StringFilter<'User'> | string
  barangay?: Prisma.StringNullableFilter<'User'> | string | null
  cra?: Prisma.StringNullableFilter<'User'> | string | null
  pobProvince?: Prisma.StringNullableFilter<'User'> | string | null
  pobCity?: Prisma.StringNullableFilter<'User'> | string | null
  religiousAffiliation?: Prisma.StringNullableFilter<'User'> | string | null
  isVerified?: Prisma.BoolFilter<'User'> | boolean
  isActive?: Prisma.BoolFilter<'User'> | boolean
  createdAt?: Prisma.DateTimeFilter<'User'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'User'> | Date | string
  password?: Prisma.StringFilter<'User'> | string
  lastLogin?: Prisma.DateTimeNullableFilter<'User'> | Date | string | null
  roleId?: Prisma.StringFilter<'User'> | string
  role?: Prisma.XOR<Prisma.RoleScalarRelationFilter, Prisma.RoleWhereInput>
  token?: Prisma.XOR<Prisma.TokenNullableScalarRelationFilter, Prisma.TokenWhereInput> | null
  studentProfile?: Prisma.XOR<
    Prisma.StudentNullableScalarRelationFilter,
    Prisma.StudentWhereInput
  > | null
  teacherProfile?: Prisma.XOR<
    Prisma.TeacherNullableScalarRelationFilter,
    Prisma.TeacherWhereInput
  > | null
  parentProfile?: Prisma.XOR<
    Prisma.ParentNullableScalarRelationFilter,
    Prisma.ParentWhereInput
  > | null
  adminProfile?: Prisma.XOR<Prisma.AdminNullableScalarRelationFilter, Prisma.AdminWhereInput> | null
  staffProfile?: Prisma.XOR<Prisma.StaffNullableScalarRelationFilter, Prisma.StaffWhereInput> | null
  schools?: Prisma.SchoolUserListRelationFilter
  departments?: Prisma.DepartmentStaffListRelationFilter
  loginLogs?: Prisma.LoginLogListRelationFilter
  activityLogs?: Prisma.ActivityLogListRelationFilter
}

export type UserOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  username?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  middleName?: Prisma.SortOrderInput | Prisma.SortOrder
  lastName?: Prisma.SortOrder
  otherName?: Prisma.SortOrderInput | Prisma.SortOrder
  gender?: Prisma.SortOrder
  dateOfBirth?: Prisma.SortOrderInput | Prisma.SortOrder
  phone?: Prisma.SortOrder
  otherPhone?: Prisma.SortOrderInput | Prisma.SortOrder
  nationality?: Prisma.SortOrderInput | Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  city?: Prisma.SortOrder
  barangay?: Prisma.SortOrderInput | Prisma.SortOrder
  cra?: Prisma.SortOrderInput | Prisma.SortOrder
  pobProvince?: Prisma.SortOrderInput | Prisma.SortOrder
  pobCity?: Prisma.SortOrderInput | Prisma.SortOrder
  religiousAffiliation?: Prisma.SortOrderInput | Prisma.SortOrder
  isVerified?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  password?: Prisma.SortOrder
  lastLogin?: Prisma.SortOrderInput | Prisma.SortOrder
  roleId?: Prisma.SortOrder
  role?: Prisma.RoleOrderByWithRelationInput
  token?: Prisma.TokenOrderByWithRelationInput
  studentProfile?: Prisma.StudentOrderByWithRelationInput
  teacherProfile?: Prisma.TeacherOrderByWithRelationInput
  parentProfile?: Prisma.ParentOrderByWithRelationInput
  adminProfile?: Prisma.AdminOrderByWithRelationInput
  staffProfile?: Prisma.StaffOrderByWithRelationInput
  schools?: Prisma.SchoolUserOrderByRelationAggregateInput
  departments?: Prisma.DepartmentStaffOrderByRelationAggregateInput
  loginLogs?: Prisma.LoginLogOrderByRelationAggregateInput
  activityLogs?: Prisma.ActivityLogOrderByRelationAggregateInput
}

export type UserWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    email?: string
    username?: string
    AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
    OR?: Prisma.UserWhereInput[]
    NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
    firstName?: Prisma.StringFilter<'User'> | string
    middleName?: Prisma.StringNullableFilter<'User'> | string | null
    lastName?: Prisma.StringFilter<'User'> | string
    otherName?: Prisma.StringNullableFilter<'User'> | string | null
    gender?: Prisma.EnumGenderFilter<'User'> | $Enums.Gender
    dateOfBirth?: Prisma.DateTimeNullableFilter<'User'> | Date | string | null
    phone?: Prisma.StringFilter<'User'> | string
    otherPhone?: Prisma.StringNullableFilter<'User'> | string | null
    nationality?: Prisma.StringNullableFilter<'User'> | string | null
    avatar?: Prisma.StringNullableFilter<'User'> | string | null
    city?: Prisma.StringFilter<'User'> | string
    barangay?: Prisma.StringNullableFilter<'User'> | string | null
    cra?: Prisma.StringNullableFilter<'User'> | string | null
    pobProvince?: Prisma.StringNullableFilter<'User'> | string | null
    pobCity?: Prisma.StringNullableFilter<'User'> | string | null
    religiousAffiliation?: Prisma.StringNullableFilter<'User'> | string | null
    isVerified?: Prisma.BoolFilter<'User'> | boolean
    isActive?: Prisma.BoolFilter<'User'> | boolean
    createdAt?: Prisma.DateTimeFilter<'User'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'User'> | Date | string
    password?: Prisma.StringFilter<'User'> | string
    lastLogin?: Prisma.DateTimeNullableFilter<'User'> | Date | string | null
    roleId?: Prisma.StringFilter<'User'> | string
    role?: Prisma.XOR<Prisma.RoleScalarRelationFilter, Prisma.RoleWhereInput>
    token?: Prisma.XOR<Prisma.TokenNullableScalarRelationFilter, Prisma.TokenWhereInput> | null
    studentProfile?: Prisma.XOR<
      Prisma.StudentNullableScalarRelationFilter,
      Prisma.StudentWhereInput
    > | null
    teacherProfile?: Prisma.XOR<
      Prisma.TeacherNullableScalarRelationFilter,
      Prisma.TeacherWhereInput
    > | null
    parentProfile?: Prisma.XOR<
      Prisma.ParentNullableScalarRelationFilter,
      Prisma.ParentWhereInput
    > | null
    adminProfile?: Prisma.XOR<
      Prisma.AdminNullableScalarRelationFilter,
      Prisma.AdminWhereInput
    > | null
    staffProfile?: Prisma.XOR<
      Prisma.StaffNullableScalarRelationFilter,
      Prisma.StaffWhereInput
    > | null
    schools?: Prisma.SchoolUserListRelationFilter
    departments?: Prisma.DepartmentStaffListRelationFilter
    loginLogs?: Prisma.LoginLogListRelationFilter
    activityLogs?: Prisma.ActivityLogListRelationFilter
  },
  'id' | 'email' | 'username'
>

export type UserOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  username?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  middleName?: Prisma.SortOrderInput | Prisma.SortOrder
  lastName?: Prisma.SortOrder
  otherName?: Prisma.SortOrderInput | Prisma.SortOrder
  gender?: Prisma.SortOrder
  dateOfBirth?: Prisma.SortOrderInput | Prisma.SortOrder
  phone?: Prisma.SortOrder
  otherPhone?: Prisma.SortOrderInput | Prisma.SortOrder
  nationality?: Prisma.SortOrderInput | Prisma.SortOrder
  avatar?: Prisma.SortOrderInput | Prisma.SortOrder
  city?: Prisma.SortOrder
  barangay?: Prisma.SortOrderInput | Prisma.SortOrder
  cra?: Prisma.SortOrderInput | Prisma.SortOrder
  pobProvince?: Prisma.SortOrderInput | Prisma.SortOrder
  pobCity?: Prisma.SortOrderInput | Prisma.SortOrder
  religiousAffiliation?: Prisma.SortOrderInput | Prisma.SortOrder
  isVerified?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  password?: Prisma.SortOrder
  lastLogin?: Prisma.SortOrderInput | Prisma.SortOrder
  roleId?: Prisma.SortOrder
  _count?: Prisma.UserCountOrderByAggregateInput
  _max?: Prisma.UserMaxOrderByAggregateInput
  _min?: Prisma.UserMinOrderByAggregateInput
}

export type UserScalarWhereWithAggregatesInput = {
  AND?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  OR?: Prisma.UserScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'User'> | string
  email?: Prisma.StringWithAggregatesFilter<'User'> | string
  username?: Prisma.StringWithAggregatesFilter<'User'> | string
  firstName?: Prisma.StringWithAggregatesFilter<'User'> | string
  middleName?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  lastName?: Prisma.StringWithAggregatesFilter<'User'> | string
  otherName?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  gender?: Prisma.EnumGenderWithAggregatesFilter<'User'> | $Enums.Gender
  dateOfBirth?: Prisma.DateTimeNullableWithAggregatesFilter<'User'> | Date | string | null
  phone?: Prisma.StringWithAggregatesFilter<'User'> | string
  otherPhone?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  nationality?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  avatar?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  city?: Prisma.StringWithAggregatesFilter<'User'> | string
  barangay?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  cra?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  pobProvince?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  pobCity?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  religiousAffiliation?: Prisma.StringNullableWithAggregatesFilter<'User'> | string | null
  isVerified?: Prisma.BoolWithAggregatesFilter<'User'> | boolean
  isActive?: Prisma.BoolWithAggregatesFilter<'User'> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'User'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'User'> | Date | string
  password?: Prisma.StringWithAggregatesFilter<'User'> | string
  lastLogin?: Prisma.DateTimeNullableWithAggregatesFilter<'User'> | Date | string | null
  roleId?: Prisma.StringWithAggregatesFilter<'User'> | string
}

export type UserCreateInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateManyInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
}

export type UserUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type UserUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type UserScalarRelationFilter = {
  is?: Prisma.UserWhereInput
  isNot?: Prisma.UserWhereInput
}

export type UserListRelationFilter = {
  every?: Prisma.UserWhereInput
  some?: Prisma.UserWhereInput
  none?: Prisma.UserWhereInput
}

export type UserOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type UserCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  username?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  middleName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  otherName?: Prisma.SortOrder
  gender?: Prisma.SortOrder
  dateOfBirth?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  otherPhone?: Prisma.SortOrder
  nationality?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  city?: Prisma.SortOrder
  barangay?: Prisma.SortOrder
  cra?: Prisma.SortOrder
  pobProvince?: Prisma.SortOrder
  pobCity?: Prisma.SortOrder
  religiousAffiliation?: Prisma.SortOrder
  isVerified?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  password?: Prisma.SortOrder
  lastLogin?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
}

export type UserMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  username?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  middleName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  otherName?: Prisma.SortOrder
  gender?: Prisma.SortOrder
  dateOfBirth?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  otherPhone?: Prisma.SortOrder
  nationality?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  city?: Prisma.SortOrder
  barangay?: Prisma.SortOrder
  cra?: Prisma.SortOrder
  pobProvince?: Prisma.SortOrder
  pobCity?: Prisma.SortOrder
  religiousAffiliation?: Prisma.SortOrder
  isVerified?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  password?: Prisma.SortOrder
  lastLogin?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
}

export type UserMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  username?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  middleName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  otherName?: Prisma.SortOrder
  gender?: Prisma.SortOrder
  dateOfBirth?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  otherPhone?: Prisma.SortOrder
  nationality?: Prisma.SortOrder
  avatar?: Prisma.SortOrder
  city?: Prisma.SortOrder
  barangay?: Prisma.SortOrder
  cra?: Prisma.SortOrder
  pobProvince?: Prisma.SortOrder
  pobCity?: Prisma.SortOrder
  religiousAffiliation?: Prisma.SortOrder
  isVerified?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  password?: Prisma.SortOrder
  lastLogin?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
}

export type UserCreateNestedOneWithoutLoginLogsInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutLoginLogsInput,
    Prisma.UserUncheckedCreateWithoutLoginLogsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutLoginLogsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutLoginLogsNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutLoginLogsInput,
    Prisma.UserUncheckedCreateWithoutLoginLogsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutLoginLogsInput
  upsert?: Prisma.UserUpsertWithoutLoginLogsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutLoginLogsInput,
      Prisma.UserUpdateWithoutLoginLogsInput
    >,
    Prisma.UserUncheckedUpdateWithoutLoginLogsInput
  >
}

export type UserCreateNestedOneWithoutActivityLogsInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutActivityLogsInput,
    Prisma.UserUncheckedCreateWithoutActivityLogsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutActivityLogsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutActivityLogsNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutActivityLogsInput,
    Prisma.UserUncheckedCreateWithoutActivityLogsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutActivityLogsInput
  upsert?: Prisma.UserUpsertWithoutActivityLogsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutActivityLogsInput,
      Prisma.UserUpdateWithoutActivityLogsInput
    >,
    Prisma.UserUncheckedUpdateWithoutActivityLogsInput
  >
}

export type UserCreateNestedManyWithoutRoleInput = {
  create?:
    | Prisma.XOR<Prisma.UserCreateWithoutRoleInput, Prisma.UserUncheckedCreateWithoutRoleInput>
    | Prisma.UserCreateWithoutRoleInput[]
    | Prisma.UserUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.UserCreateOrConnectWithoutRoleInput
    | Prisma.UserCreateOrConnectWithoutRoleInput[]
  createMany?: Prisma.UserCreateManyRoleInputEnvelope
  connect?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
}

export type UserUncheckedCreateNestedManyWithoutRoleInput = {
  create?:
    | Prisma.XOR<Prisma.UserCreateWithoutRoleInput, Prisma.UserUncheckedCreateWithoutRoleInput>
    | Prisma.UserCreateWithoutRoleInput[]
    | Prisma.UserUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.UserCreateOrConnectWithoutRoleInput
    | Prisma.UserCreateOrConnectWithoutRoleInput[]
  createMany?: Prisma.UserCreateManyRoleInputEnvelope
  connect?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
}

export type UserUpdateManyWithoutRoleNestedInput = {
  create?:
    | Prisma.XOR<Prisma.UserCreateWithoutRoleInput, Prisma.UserUncheckedCreateWithoutRoleInput>
    | Prisma.UserCreateWithoutRoleInput[]
    | Prisma.UserUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.UserCreateOrConnectWithoutRoleInput
    | Prisma.UserCreateOrConnectWithoutRoleInput[]
  upsert?:
    | Prisma.UserUpsertWithWhereUniqueWithoutRoleInput
    | Prisma.UserUpsertWithWhereUniqueWithoutRoleInput[]
  createMany?: Prisma.UserCreateManyRoleInputEnvelope
  set?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  disconnect?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  delete?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  connect?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  update?:
    | Prisma.UserUpdateWithWhereUniqueWithoutRoleInput
    | Prisma.UserUpdateWithWhereUniqueWithoutRoleInput[]
  updateMany?:
    | Prisma.UserUpdateManyWithWhereWithoutRoleInput
    | Prisma.UserUpdateManyWithWhereWithoutRoleInput[]
  deleteMany?: Prisma.UserScalarWhereInput | Prisma.UserScalarWhereInput[]
}

export type UserUncheckedUpdateManyWithoutRoleNestedInput = {
  create?:
    | Prisma.XOR<Prisma.UserCreateWithoutRoleInput, Prisma.UserUncheckedCreateWithoutRoleInput>
    | Prisma.UserCreateWithoutRoleInput[]
    | Prisma.UserUncheckedCreateWithoutRoleInput[]
  connectOrCreate?:
    | Prisma.UserCreateOrConnectWithoutRoleInput
    | Prisma.UserCreateOrConnectWithoutRoleInput[]
  upsert?:
    | Prisma.UserUpsertWithWhereUniqueWithoutRoleInput
    | Prisma.UserUpsertWithWhereUniqueWithoutRoleInput[]
  createMany?: Prisma.UserCreateManyRoleInputEnvelope
  set?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  disconnect?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  delete?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  connect?: Prisma.UserWhereUniqueInput | Prisma.UserWhereUniqueInput[]
  update?:
    | Prisma.UserUpdateWithWhereUniqueWithoutRoleInput
    | Prisma.UserUpdateWithWhereUniqueWithoutRoleInput[]
  updateMany?:
    | Prisma.UserUpdateManyWithWhereWithoutRoleInput
    | Prisma.UserUpdateManyWithWhereWithoutRoleInput[]
  deleteMany?: Prisma.UserScalarWhereInput | Prisma.UserScalarWhereInput[]
}

export type UserCreateNestedOneWithoutSchoolsInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutSchoolsInput,
    Prisma.UserUncheckedCreateWithoutSchoolsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSchoolsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutSchoolsNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutSchoolsInput,
    Prisma.UserUncheckedCreateWithoutSchoolsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSchoolsInput
  upsert?: Prisma.UserUpsertWithoutSchoolsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutSchoolsInput,
      Prisma.UserUpdateWithoutSchoolsInput
    >,
    Prisma.UserUncheckedUpdateWithoutSchoolsInput
  >
}

export type UserCreateNestedOneWithoutDepartmentsInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutDepartmentsInput,
    Prisma.UserUncheckedCreateWithoutDepartmentsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutDepartmentsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutDepartmentsNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutDepartmentsInput,
    Prisma.UserUncheckedCreateWithoutDepartmentsInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutDepartmentsInput
  upsert?: Prisma.UserUpsertWithoutDepartmentsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutDepartmentsInput,
      Prisma.UserUpdateWithoutDepartmentsInput
    >,
    Prisma.UserUncheckedUpdateWithoutDepartmentsInput
  >
}

export type UserCreateNestedOneWithoutTokenInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutTokenInput,
    Prisma.UserUncheckedCreateWithoutTokenInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTokenInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutTokenNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutTokenInput,
    Prisma.UserUncheckedCreateWithoutTokenInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTokenInput
  upsert?: Prisma.UserUpsertWithoutTokenInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutTokenInput,
      Prisma.UserUpdateWithoutTokenInput
    >,
    Prisma.UserUncheckedUpdateWithoutTokenInput
  >
}

export type EnumGenderFieldUpdateOperationsInput = {
  set?: $Enums.Gender
}

export type UserCreateNestedOneWithoutAdminProfileInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutAdminProfileInput,
    Prisma.UserUncheckedCreateWithoutAdminProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAdminProfileInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutAdminProfileNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutAdminProfileInput,
    Prisma.UserUncheckedCreateWithoutAdminProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAdminProfileInput
  upsert?: Prisma.UserUpsertWithoutAdminProfileInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutAdminProfileInput,
      Prisma.UserUpdateWithoutAdminProfileInput
    >,
    Prisma.UserUncheckedUpdateWithoutAdminProfileInput
  >
}

export type UserCreateNestedOneWithoutTeacherProfileInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutTeacherProfileInput,
    Prisma.UserUncheckedCreateWithoutTeacherProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTeacherProfileInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutTeacherProfileNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutTeacherProfileInput,
    Prisma.UserUncheckedCreateWithoutTeacherProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTeacherProfileInput
  upsert?: Prisma.UserUpsertWithoutTeacherProfileInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutTeacherProfileInput,
      Prisma.UserUpdateWithoutTeacherProfileInput
    >,
    Prisma.UserUncheckedUpdateWithoutTeacherProfileInput
  >
}

export type UserCreateNestedOneWithoutStaffProfileInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutStaffProfileInput,
    Prisma.UserUncheckedCreateWithoutStaffProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutStaffProfileInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutStaffProfileNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutStaffProfileInput,
    Prisma.UserUncheckedCreateWithoutStaffProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutStaffProfileInput
  upsert?: Prisma.UserUpsertWithoutStaffProfileInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutStaffProfileInput,
      Prisma.UserUpdateWithoutStaffProfileInput
    >,
    Prisma.UserUncheckedUpdateWithoutStaffProfileInput
  >
}

export type UserCreateNestedOneWithoutStudentProfileInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutStudentProfileInput,
    Prisma.UserUncheckedCreateWithoutStudentProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutStudentProfileInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutStudentProfileNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutStudentProfileInput,
    Prisma.UserUncheckedCreateWithoutStudentProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutStudentProfileInput
  upsert?: Prisma.UserUpsertWithoutStudentProfileInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutStudentProfileInput,
      Prisma.UserUpdateWithoutStudentProfileInput
    >,
    Prisma.UserUncheckedUpdateWithoutStudentProfileInput
  >
}

export type UserCreateNestedOneWithoutParentProfileInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutParentProfileInput,
    Prisma.UserUncheckedCreateWithoutParentProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutParentProfileInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutParentProfileNestedInput = {
  create?: Prisma.XOR<
    Prisma.UserCreateWithoutParentProfileInput,
    Prisma.UserUncheckedCreateWithoutParentProfileInput
  >
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutParentProfileInput
  upsert?: Prisma.UserUpsertWithoutParentProfileInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.UserUpdateToOneWithWhereWithoutParentProfileInput,
      Prisma.UserUpdateWithoutParentProfileInput
    >,
    Prisma.UserUncheckedUpdateWithoutParentProfileInput
  >
}

export type UserCreateWithoutLoginLogsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutLoginLogsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutLoginLogsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutLoginLogsInput,
    Prisma.UserUncheckedCreateWithoutLoginLogsInput
  >
}

export type UserUpsertWithoutLoginLogsInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutLoginLogsInput,
    Prisma.UserUncheckedUpdateWithoutLoginLogsInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutLoginLogsInput,
    Prisma.UserUncheckedCreateWithoutLoginLogsInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutLoginLogsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutLoginLogsInput,
    Prisma.UserUncheckedUpdateWithoutLoginLogsInput
  >
}

export type UserUpdateWithoutLoginLogsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutLoginLogsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutActivityLogsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutActivityLogsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutActivityLogsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutActivityLogsInput,
    Prisma.UserUncheckedCreateWithoutActivityLogsInput
  >
}

export type UserUpsertWithoutActivityLogsInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutActivityLogsInput,
    Prisma.UserUncheckedUpdateWithoutActivityLogsInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutActivityLogsInput,
    Prisma.UserUncheckedCreateWithoutActivityLogsInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutActivityLogsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutActivityLogsInput,
    Prisma.UserUncheckedUpdateWithoutActivityLogsInput
  >
}

export type UserUpdateWithoutActivityLogsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutActivityLogsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutRoleInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutRoleInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutRoleInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutRoleInput, Prisma.UserUncheckedCreateWithoutRoleInput>
}

export type UserCreateManyRoleInputEnvelope = {
  data: Prisma.UserCreateManyRoleInput | Prisma.UserCreateManyRoleInput[]
  skipDuplicates?: boolean
}

export type UserUpsertWithWhereUniqueWithoutRoleInput = {
  where: Prisma.UserWhereUniqueInput
  update: Prisma.XOR<Prisma.UserUpdateWithoutRoleInput, Prisma.UserUncheckedUpdateWithoutRoleInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutRoleInput, Prisma.UserUncheckedCreateWithoutRoleInput>
}

export type UserUpdateWithWhereUniqueWithoutRoleInput = {
  where: Prisma.UserWhereUniqueInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutRoleInput, Prisma.UserUncheckedUpdateWithoutRoleInput>
}

export type UserUpdateManyWithWhereWithoutRoleInput = {
  where: Prisma.UserScalarWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateManyMutationInput,
    Prisma.UserUncheckedUpdateManyWithoutRoleInput
  >
}

export type UserScalarWhereInput = {
  AND?: Prisma.UserScalarWhereInput | Prisma.UserScalarWhereInput[]
  OR?: Prisma.UserScalarWhereInput[]
  NOT?: Prisma.UserScalarWhereInput | Prisma.UserScalarWhereInput[]
  id?: Prisma.StringFilter<'User'> | string
  email?: Prisma.StringFilter<'User'> | string
  username?: Prisma.StringFilter<'User'> | string
  firstName?: Prisma.StringFilter<'User'> | string
  middleName?: Prisma.StringNullableFilter<'User'> | string | null
  lastName?: Prisma.StringFilter<'User'> | string
  otherName?: Prisma.StringNullableFilter<'User'> | string | null
  gender?: Prisma.EnumGenderFilter<'User'> | $Enums.Gender
  dateOfBirth?: Prisma.DateTimeNullableFilter<'User'> | Date | string | null
  phone?: Prisma.StringFilter<'User'> | string
  otherPhone?: Prisma.StringNullableFilter<'User'> | string | null
  nationality?: Prisma.StringNullableFilter<'User'> | string | null
  avatar?: Prisma.StringNullableFilter<'User'> | string | null
  city?: Prisma.StringFilter<'User'> | string
  barangay?: Prisma.StringNullableFilter<'User'> | string | null
  cra?: Prisma.StringNullableFilter<'User'> | string | null
  pobProvince?: Prisma.StringNullableFilter<'User'> | string | null
  pobCity?: Prisma.StringNullableFilter<'User'> | string | null
  religiousAffiliation?: Prisma.StringNullableFilter<'User'> | string | null
  isVerified?: Prisma.BoolFilter<'User'> | boolean
  isActive?: Prisma.BoolFilter<'User'> | boolean
  createdAt?: Prisma.DateTimeFilter<'User'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'User'> | Date | string
  password?: Prisma.StringFilter<'User'> | string
  lastLogin?: Prisma.DateTimeNullableFilter<'User'> | Date | string | null
  roleId?: Prisma.StringFilter<'User'> | string
}

export type UserCreateWithoutSchoolsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutSchoolsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutSchoolsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutSchoolsInput,
    Prisma.UserUncheckedCreateWithoutSchoolsInput
  >
}

export type UserUpsertWithoutSchoolsInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutSchoolsInput,
    Prisma.UserUncheckedUpdateWithoutSchoolsInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutSchoolsInput,
    Prisma.UserUncheckedCreateWithoutSchoolsInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutSchoolsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutSchoolsInput,
    Prisma.UserUncheckedUpdateWithoutSchoolsInput
  >
}

export type UserUpdateWithoutSchoolsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutSchoolsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutDepartmentsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutDepartmentsInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutDepartmentsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutDepartmentsInput,
    Prisma.UserUncheckedCreateWithoutDepartmentsInput
  >
}

export type UserUpsertWithoutDepartmentsInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutDepartmentsInput,
    Prisma.UserUncheckedUpdateWithoutDepartmentsInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutDepartmentsInput,
    Prisma.UserUncheckedCreateWithoutDepartmentsInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutDepartmentsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutDepartmentsInput,
    Prisma.UserUncheckedUpdateWithoutDepartmentsInput
  >
}

export type UserUpdateWithoutDepartmentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutDepartmentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutTokenInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutTokenInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutTokenInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutTokenInput,
    Prisma.UserUncheckedCreateWithoutTokenInput
  >
}

export type UserUpsertWithoutTokenInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutTokenInput,
    Prisma.UserUncheckedUpdateWithoutTokenInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutTokenInput,
    Prisma.UserUncheckedCreateWithoutTokenInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutTokenInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutTokenInput, Prisma.UserUncheckedUpdateWithoutTokenInput>
}

export type UserUpdateWithoutTokenInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutTokenInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutAdminProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutAdminProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutAdminProfileInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutAdminProfileInput,
    Prisma.UserUncheckedCreateWithoutAdminProfileInput
  >
}

export type UserUpsertWithoutAdminProfileInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutAdminProfileInput,
    Prisma.UserUncheckedUpdateWithoutAdminProfileInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutAdminProfileInput,
    Prisma.UserUncheckedCreateWithoutAdminProfileInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAdminProfileInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutAdminProfileInput,
    Prisma.UserUncheckedUpdateWithoutAdminProfileInput
  >
}

export type UserUpdateWithoutAdminProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutAdminProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutTeacherProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutTeacherProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutTeacherProfileInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutTeacherProfileInput,
    Prisma.UserUncheckedCreateWithoutTeacherProfileInput
  >
}

export type UserUpsertWithoutTeacherProfileInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutTeacherProfileInput,
    Prisma.UserUncheckedUpdateWithoutTeacherProfileInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutTeacherProfileInput,
    Prisma.UserUncheckedCreateWithoutTeacherProfileInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutTeacherProfileInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutTeacherProfileInput,
    Prisma.UserUncheckedUpdateWithoutTeacherProfileInput
  >
}

export type UserUpdateWithoutTeacherProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutTeacherProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutStaffProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutStaffProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutStaffProfileInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutStaffProfileInput,
    Prisma.UserUncheckedCreateWithoutStaffProfileInput
  >
}

export type UserUpsertWithoutStaffProfileInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutStaffProfileInput,
    Prisma.UserUncheckedUpdateWithoutStaffProfileInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutStaffProfileInput,
    Prisma.UserUncheckedCreateWithoutStaffProfileInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutStaffProfileInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutStaffProfileInput,
    Prisma.UserUncheckedUpdateWithoutStaffProfileInput
  >
}

export type UserUpdateWithoutStaffProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutStaffProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutStudentProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutStudentProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  parentProfile?: Prisma.ParentUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutStudentProfileInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutStudentProfileInput,
    Prisma.UserUncheckedCreateWithoutStudentProfileInput
  >
}

export type UserUpsertWithoutStudentProfileInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutStudentProfileInput,
    Prisma.UserUncheckedUpdateWithoutStudentProfileInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutStudentProfileInput,
    Prisma.UserUncheckedCreateWithoutStudentProfileInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutStudentProfileInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutStudentProfileInput,
    Prisma.UserUncheckedUpdateWithoutStudentProfileInput
  >
}

export type UserUpdateWithoutStudentProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutStudentProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutParentProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  role: Prisma.RoleCreateNestedOneWithoutUsersInput
  token?: Prisma.TokenCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutParentProfileInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
  roleId: string
  token?: Prisma.TokenUncheckedCreateNestedOneWithoutUserInput
  studentProfile?: Prisma.StudentUncheckedCreateNestedOneWithoutUserInput
  teacherProfile?: Prisma.TeacherUncheckedCreateNestedOneWithoutUserInput
  adminProfile?: Prisma.AdminUncheckedCreateNestedOneWithoutUserInput
  staffProfile?: Prisma.StaffUncheckedCreateNestedOneWithoutUserInput
  schools?: Prisma.SchoolUserUncheckedCreateNestedManyWithoutUserInput
  departments?: Prisma.DepartmentStaffUncheckedCreateNestedManyWithoutUserInput
  loginLogs?: Prisma.LoginLogUncheckedCreateNestedManyWithoutUserInput
  activityLogs?: Prisma.ActivityLogUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutParentProfileInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<
    Prisma.UserCreateWithoutParentProfileInput,
    Prisma.UserUncheckedCreateWithoutParentProfileInput
  >
}

export type UserUpsertWithoutParentProfileInput = {
  update: Prisma.XOR<
    Prisma.UserUpdateWithoutParentProfileInput,
    Prisma.UserUncheckedUpdateWithoutParentProfileInput
  >
  create: Prisma.XOR<
    Prisma.UserCreateWithoutParentProfileInput,
    Prisma.UserUncheckedCreateWithoutParentProfileInput
  >
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutParentProfileInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<
    Prisma.UserUpdateWithoutParentProfileInput,
    Prisma.UserUncheckedUpdateWithoutParentProfileInput
  >
}

export type UserUpdateWithoutParentProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  role?: Prisma.RoleUpdateOneRequiredWithoutUsersNestedInput
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutParentProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roleId?: Prisma.StringFieldUpdateOperationsInput | string
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateManyRoleInput = {
  id?: string
  email: string
  username: string
  firstName: string
  middleName?: string | null
  lastName: string
  otherName?: string | null
  gender: $Enums.Gender
  dateOfBirth?: Date | string | null
  phone: string
  otherPhone?: string | null
  nationality?: string | null
  avatar?: string | null
  city: string
  barangay?: string | null
  cra?: string | null
  pobProvince?: string | null
  pobCity?: string | null
  religiousAffiliation?: string | null
  isVerified?: boolean
  isActive?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  password: string
  lastLogin?: Date | string | null
}

export type UserUpdateWithoutRoleInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  token?: Prisma.TokenUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutRoleInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  token?: Prisma.TokenUncheckedUpdateOneWithoutUserNestedInput
  studentProfile?: Prisma.StudentUncheckedUpdateOneWithoutUserNestedInput
  teacherProfile?: Prisma.TeacherUncheckedUpdateOneWithoutUserNestedInput
  parentProfile?: Prisma.ParentUncheckedUpdateOneWithoutUserNestedInput
  adminProfile?: Prisma.AdminUncheckedUpdateOneWithoutUserNestedInput
  staffProfile?: Prisma.StaffUncheckedUpdateOneWithoutUserNestedInput
  schools?: Prisma.SchoolUserUncheckedUpdateManyWithoutUserNestedInput
  departments?: Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput
  loginLogs?: Prisma.LoginLogUncheckedUpdateManyWithoutUserNestedInput
  activityLogs?: Prisma.ActivityLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateManyWithoutRoleInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  username?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  middleName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  otherName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  gender?: Prisma.EnumGenderFieldUpdateOperationsInput | $Enums.Gender
  dateOfBirth?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  otherPhone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nationality?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  avatar?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  city?: Prisma.StringFieldUpdateOperationsInput | string
  barangay?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cra?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobProvince?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  pobCity?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  religiousAffiliation?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  lastLogin?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

/**
 * Count Type UserCountOutputType
 */

export type UserCountOutputType = {
  schools: number
  departments: number
  loginLogs: number
  activityLogs: number
}

export type UserCountOutputTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  schools?: boolean | UserCountOutputTypeCountSchoolsArgs
  departments?: boolean | UserCountOutputTypeCountDepartmentsArgs
  loginLogs?: boolean | UserCountOutputTypeCountLoginLogsArgs
  activityLogs?: boolean | UserCountOutputTypeCountActivityLogsArgs
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the UserCountOutputType
   */
  select?: Prisma.UserCountOutputTypeSelect<ExtArgs> | null
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountSchoolsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SchoolUserWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountDepartmentsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.DepartmentStaffWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountLoginLogsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.LoginLogWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountActivityLogsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.ActivityLogWhereInput
}

export type UserSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    email?: boolean
    username?: boolean
    firstName?: boolean
    middleName?: boolean
    lastName?: boolean
    otherName?: boolean
    gender?: boolean
    dateOfBirth?: boolean
    phone?: boolean
    otherPhone?: boolean
    nationality?: boolean
    avatar?: boolean
    city?: boolean
    barangay?: boolean
    cra?: boolean
    pobProvince?: boolean
    pobCity?: boolean
    religiousAffiliation?: boolean
    isVerified?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    password?: boolean
    lastLogin?: boolean
    roleId?: boolean
    role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
    token?: boolean | Prisma.User$tokenArgs<ExtArgs>
    studentProfile?: boolean | Prisma.User$studentProfileArgs<ExtArgs>
    teacherProfile?: boolean | Prisma.User$teacherProfileArgs<ExtArgs>
    parentProfile?: boolean | Prisma.User$parentProfileArgs<ExtArgs>
    adminProfile?: boolean | Prisma.User$adminProfileArgs<ExtArgs>
    staffProfile?: boolean | Prisma.User$staffProfileArgs<ExtArgs>
    schools?: boolean | Prisma.User$schoolsArgs<ExtArgs>
    departments?: boolean | Prisma.User$departmentsArgs<ExtArgs>
    loginLogs?: boolean | Prisma.User$loginLogsArgs<ExtArgs>
    activityLogs?: boolean | Prisma.User$activityLogsArgs<ExtArgs>
    _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['user']
>

export type UserSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    email?: boolean
    username?: boolean
    firstName?: boolean
    middleName?: boolean
    lastName?: boolean
    otherName?: boolean
    gender?: boolean
    dateOfBirth?: boolean
    phone?: boolean
    otherPhone?: boolean
    nationality?: boolean
    avatar?: boolean
    city?: boolean
    barangay?: boolean
    cra?: boolean
    pobProvince?: boolean
    pobCity?: boolean
    religiousAffiliation?: boolean
    isVerified?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    password?: boolean
    lastLogin?: boolean
    roleId?: boolean
    role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['user']
>

export type UserSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    email?: boolean
    username?: boolean
    firstName?: boolean
    middleName?: boolean
    lastName?: boolean
    otherName?: boolean
    gender?: boolean
    dateOfBirth?: boolean
    phone?: boolean
    otherPhone?: boolean
    nationality?: boolean
    avatar?: boolean
    city?: boolean
    barangay?: boolean
    cra?: boolean
    pobProvince?: boolean
    pobCity?: boolean
    religiousAffiliation?: boolean
    isVerified?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    password?: boolean
    lastLogin?: boolean
    roleId?: boolean
    role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['user']
>

export type UserSelectScalar = {
  id?: boolean
  email?: boolean
  username?: boolean
  firstName?: boolean
  middleName?: boolean
  lastName?: boolean
  otherName?: boolean
  gender?: boolean
  dateOfBirth?: boolean
  phone?: boolean
  otherPhone?: boolean
  nationality?: boolean
  avatar?: boolean
  city?: boolean
  barangay?: boolean
  cra?: boolean
  pobProvince?: boolean
  pobCity?: boolean
  religiousAffiliation?: boolean
  isVerified?: boolean
  isActive?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  password?: boolean
  lastLogin?: boolean
  roleId?: boolean
}

export type UserOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  | 'id'
  | 'email'
  | 'username'
  | 'firstName'
  | 'middleName'
  | 'lastName'
  | 'otherName'
  | 'gender'
  | 'dateOfBirth'
  | 'phone'
  | 'otherPhone'
  | 'nationality'
  | 'avatar'
  | 'city'
  | 'barangay'
  | 'cra'
  | 'pobProvince'
  | 'pobCity'
  | 'religiousAffiliation'
  | 'isVerified'
  | 'isActive'
  | 'createdAt'
  | 'updatedAt'
  | 'password'
  | 'lastLogin'
  | 'roleId',
  ExtArgs['result']['user']
>
export type UserInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
  token?: boolean | Prisma.User$tokenArgs<ExtArgs>
  studentProfile?: boolean | Prisma.User$studentProfileArgs<ExtArgs>
  teacherProfile?: boolean | Prisma.User$teacherProfileArgs<ExtArgs>
  parentProfile?: boolean | Prisma.User$parentProfileArgs<ExtArgs>
  adminProfile?: boolean | Prisma.User$adminProfileArgs<ExtArgs>
  staffProfile?: boolean | Prisma.User$staffProfileArgs<ExtArgs>
  schools?: boolean | Prisma.User$schoolsArgs<ExtArgs>
  departments?: boolean | Prisma.User$departmentsArgs<ExtArgs>
  loginLogs?: boolean | Prisma.User$loginLogsArgs<ExtArgs>
  activityLogs?: boolean | Prisma.User$activityLogsArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}
export type UserIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
}
export type UserIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
}

export type $UserPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'User'
  objects: {
    role: Prisma.$RolePayload<ExtArgs>
    token: Prisma.$TokenPayload<ExtArgs> | null
    studentProfile: Prisma.$StudentPayload<ExtArgs> | null
    teacherProfile: Prisma.$TeacherPayload<ExtArgs> | null
    parentProfile: Prisma.$ParentPayload<ExtArgs> | null
    adminProfile: Prisma.$AdminPayload<ExtArgs> | null
    staffProfile: Prisma.$StaffPayload<ExtArgs> | null
    schools: Prisma.$SchoolUserPayload<ExtArgs>[]
    departments: Prisma.$DepartmentStaffPayload<ExtArgs>[]
    loginLogs: Prisma.$LoginLogPayload<ExtArgs>[]
    activityLogs: Prisma.$ActivityLogPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      email: string
      username: string
      firstName: string
      middleName: string | null
      lastName: string
      otherName: string | null
      gender: $Enums.Gender
      dateOfBirth: Date | null
      phone: string
      otherPhone: string | null
      nationality: string | null
      avatar: string | null
      city: string
      barangay: string | null
      cra: string | null
      pobProvince: string | null
      pobCity: string | null
      religiousAffiliation: string | null
      isVerified: boolean
      isActive: boolean
      createdAt: Date
      updatedAt: Date
      password: string
      lastLogin: Date | null
      roleId: string
    },
    ExtArgs['result']['user']
  >
  composites: {}
}

export type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$UserPayload, S>

export type UserCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: UserCountAggregateInputType | true
}

export interface UserDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User']; meta: { name: 'User' } }
  /**
   * Find zero or one User that matches the filter.
   * @param {UserFindUniqueArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UserFindUniqueArgs>(
    args: Prisma.SelectSubset<T, UserFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<
      Prisma.$UserPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one User that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<
      Prisma.$UserPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first User that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UserFindFirstArgs>(
    args?: Prisma.SelectSubset<T, UserFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<
      Prisma.$UserPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first User that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<
      Prisma.$UserPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Users that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Users
   * const users = await prisma.user.findMany()
   *
   * // Get first 10 Users
   * const users = await prisma.user.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
   *
   */
  findMany<T extends UserFindManyArgs>(
    args?: Prisma.SelectSubset<T, UserFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, 'findMany', GlobalOmitOptions>
  >

  /**
   * Create a User.
   * @param {UserCreateArgs} args - Arguments to create a User.
   * @example
   * // Create one User
   * const User = await prisma.user.create({
   *   data: {
   *     // ... data to create a User
   *   }
   * })
   *
   */
  create<T extends UserCreateArgs>(
    args: Prisma.SelectSubset<T, UserCreateArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Users.
   * @param {UserCreateManyArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends UserCreateManyArgs>(
    args?: Prisma.SelectSubset<T, UserCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Users and returns the data saved in the database.
   * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Users and only return the `id`
   * const userWithIdOnly = await prisma.user.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends UserCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$UserPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a User.
   * @param {UserDeleteArgs} args - Arguments to delete one User.
   * @example
   * // Delete one User
   * const User = await prisma.user.delete({
   *   where: {
   *     // ... filter to delete one User
   *   }
   * })
   *
   */
  delete<T extends UserDeleteArgs>(
    args: Prisma.SelectSubset<T, UserDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one User.
   * @param {UserUpdateArgs} args - Arguments to update one User.
   * @example
   * // Update one User
   * const user = await prisma.user.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends UserUpdateArgs>(
    args: Prisma.SelectSubset<T, UserUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Users.
   * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
   * @example
   * // Delete a few Users
   * const { count } = await prisma.user.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends UserDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, UserDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends UserUpdateManyArgs>(
    args: Prisma.SelectSubset<T, UserUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users and returns the data updated in the database.
   * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Users and only return the `id`
   * const userWithIdOnly = await prisma.user.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$UserPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one User.
   * @param {UserUpsertArgs} args - Arguments to update or create a User.
   * @example
   * // Update or create a User
   * const user = await prisma.user.upsert({
   *   create: {
   *     // ... data to create a User
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User we want to update
   *   }
   * })
   */
  upsert<T extends UserUpsertArgs>(
    args: Prisma.SelectSubset<T, UserUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserCountArgs} args - Arguments to filter Users to count.
   * @example
   * // Count the number of Users
   * const count = await prisma.user.count({
   *   where: {
   *     // ... the filter for the Users we want to count
   *   }
   * })
   **/
  count<T extends UserCountArgs>(
    args?: Prisma.Subset<T, UserCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UserCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends UserAggregateArgs>(
    args: Prisma.Subset<T, UserAggregateArgs>
  ): Prisma.PrismaPromise<GetUserAggregateType<T>>

  /**
   * Group by User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends UserGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UserGroupByArgs['orderBy'] }
      : { orderBy?: UserGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for User.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UserClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  role<T extends Prisma.RoleDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.RoleDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__RoleClient<
    | runtime.Types.Result.GetResult<
        Prisma.$RolePayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  token<T extends Prisma.User$tokenArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$tokenArgs<ExtArgs>>
  ): Prisma.Prisma__TokenClient<
    runtime.Types.Result.GetResult<
      Prisma.$TokenPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >
  studentProfile<T extends Prisma.User$studentProfileArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$studentProfileArgs<ExtArgs>>
  ): Prisma.Prisma__StudentClient<
    runtime.Types.Result.GetResult<
      Prisma.$StudentPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >
  teacherProfile<T extends Prisma.User$teacherProfileArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$teacherProfileArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >
  parentProfile<T extends Prisma.User$parentProfileArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$parentProfileArgs<ExtArgs>>
  ): Prisma.Prisma__ParentClient<
    runtime.Types.Result.GetResult<
      Prisma.$ParentPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >
  adminProfile<T extends Prisma.User$adminProfileArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$adminProfileArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<
      Prisma.$AdminPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >
  staffProfile<T extends Prisma.User$staffProfileArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$staffProfileArgs<ExtArgs>>
  ): Prisma.Prisma__StaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$StaffPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >
  schools<T extends Prisma.User$schoolsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$schoolsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SchoolUserPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  departments<T extends Prisma.User$departmentsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$departmentsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$DepartmentStaffPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  loginLogs<T extends Prisma.User$loginLogsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$loginLogsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$LoginLogPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  activityLogs<T extends Prisma.User$activityLogsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.User$activityLogsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$ActivityLogPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the User model
 */
export interface UserFieldRefs {
  readonly id: Prisma.FieldRef<'User', 'String'>
  readonly email: Prisma.FieldRef<'User', 'String'>
  readonly username: Prisma.FieldRef<'User', 'String'>
  readonly firstName: Prisma.FieldRef<'User', 'String'>
  readonly middleName: Prisma.FieldRef<'User', 'String'>
  readonly lastName: Prisma.FieldRef<'User', 'String'>
  readonly otherName: Prisma.FieldRef<'User', 'String'>
  readonly gender: Prisma.FieldRef<'User', 'Gender'>
  readonly dateOfBirth: Prisma.FieldRef<'User', 'DateTime'>
  readonly phone: Prisma.FieldRef<'User', 'String'>
  readonly otherPhone: Prisma.FieldRef<'User', 'String'>
  readonly nationality: Prisma.FieldRef<'User', 'String'>
  readonly avatar: Prisma.FieldRef<'User', 'String'>
  readonly city: Prisma.FieldRef<'User', 'String'>
  readonly barangay: Prisma.FieldRef<'User', 'String'>
  readonly cra: Prisma.FieldRef<'User', 'String'>
  readonly pobProvince: Prisma.FieldRef<'User', 'String'>
  readonly pobCity: Prisma.FieldRef<'User', 'String'>
  readonly religiousAffiliation: Prisma.FieldRef<'User', 'String'>
  readonly isVerified: Prisma.FieldRef<'User', 'Boolean'>
  readonly isActive: Prisma.FieldRef<'User', 'Boolean'>
  readonly createdAt: Prisma.FieldRef<'User', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'User', 'DateTime'>
  readonly password: Prisma.FieldRef<'User', 'String'>
  readonly lastLogin: Prisma.FieldRef<'User', 'DateTime'>
  readonly roleId: Prisma.FieldRef<'User', 'String'>
}

// Custom InputTypes
/**
 * User findUnique
 */
export type UserFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findUniqueOrThrow
 */
export type UserFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findFirst
 */
export type UserFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findFirstOrThrow
 */
export type UserFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findMany
 */
export type UserFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which Users to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Users.
   */
  skip?: number
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User create
 */
export type UserCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to create a User.
   */
  data: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
}

/**
 * User createMany
 */
export type UserCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User createManyAndReturn
 */
export type UserCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * User update
 */
export type UserUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to update a User.
   */
  data: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
  /**
   * Choose, which User to update.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User updateMany
 */
export type UserUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User updateManyAndReturn
 */
export type UserUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * User upsert
 */
export type UserUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The filter to search for the User to update in case it exists.
   */
  where: Prisma.UserWhereUniqueInput
  /**
   * In case the User found by the `where` argument doesn't exist, create a new User with this data.
   */
  create: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
  /**
   * In case the User was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
}

/**
 * User delete
 */
export type UserDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter which User to delete.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User deleteMany
 */
export type UserDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Users to delete
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to delete.
   */
  limit?: number
}

/**
 * User.token
 */
export type User$tokenArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Token
   */
  select?: Prisma.TokenSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Token
   */
  omit?: Prisma.TokenOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TokenInclude<ExtArgs> | null
  where?: Prisma.TokenWhereInput
}

/**
 * User.studentProfile
 */
export type User$studentProfileArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Student
   */
  select?: Prisma.StudentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Student
   */
  omit?: Prisma.StudentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StudentInclude<ExtArgs> | null
  where?: Prisma.StudentWhereInput
}

/**
 * User.teacherProfile
 */
export type User$teacherProfileArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  where?: Prisma.TeacherWhereInput
}

/**
 * User.parentProfile
 */
export type User$parentProfileArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Parent
   */
  select?: Prisma.ParentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Parent
   */
  omit?: Prisma.ParentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ParentInclude<ExtArgs> | null
  where?: Prisma.ParentWhereInput
}

/**
 * User.adminProfile
 */
export type User$adminProfileArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  where?: Prisma.AdminWhereInput
}

/**
 * User.staffProfile
 */
export type User$staffProfileArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Staff
   */
  select?: Prisma.StaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Staff
   */
  omit?: Prisma.StaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StaffInclude<ExtArgs> | null
  where?: Prisma.StaffWhereInput
}

/**
 * User.schools
 */
export type User$schoolsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SchoolUser
   */
  select?: Prisma.SchoolUserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SchoolUser
   */
  omit?: Prisma.SchoolUserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SchoolUserInclude<ExtArgs> | null
  where?: Prisma.SchoolUserWhereInput
  orderBy?: Prisma.SchoolUserOrderByWithRelationInput | Prisma.SchoolUserOrderByWithRelationInput[]
  cursor?: Prisma.SchoolUserWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SchoolUserScalarFieldEnum | Prisma.SchoolUserScalarFieldEnum[]
}

/**
 * User.departments
 */
export type User$departmentsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  where?: Prisma.DepartmentStaffWhereInput
  orderBy?:
    | Prisma.DepartmentStaffOrderByWithRelationInput
    | Prisma.DepartmentStaffOrderByWithRelationInput[]
  cursor?: Prisma.DepartmentStaffWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DepartmentStaffScalarFieldEnum | Prisma.DepartmentStaffScalarFieldEnum[]
}

/**
 * User.loginLogs
 */
export type User$loginLogsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the LoginLog
   */
  select?: Prisma.LoginLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LoginLog
   */
  omit?: Prisma.LoginLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LoginLogInclude<ExtArgs> | null
  where?: Prisma.LoginLogWhereInput
  orderBy?: Prisma.LoginLogOrderByWithRelationInput | Prisma.LoginLogOrderByWithRelationInput[]
  cursor?: Prisma.LoginLogWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.LoginLogScalarFieldEnum | Prisma.LoginLogScalarFieldEnum[]
}

/**
 * User.activityLogs
 */
export type User$activityLogsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  where?: Prisma.ActivityLogWhereInput
  orderBy?:
    | Prisma.ActivityLogOrderByWithRelationInput
    | Prisma.ActivityLogOrderByWithRelationInput[]
  cursor?: Prisma.ActivityLogWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ActivityLogScalarFieldEnum | Prisma.ActivityLogScalarFieldEnum[]
}

/**
 * User without action
 */
export type UserDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
}
