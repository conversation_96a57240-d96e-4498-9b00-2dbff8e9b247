/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `ActivityLog` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model ActivityLog
 *
 */
export type ActivityLogModel = runtime.Types.Result.DefaultSelection<Prisma.$ActivityLogPayload>

export type AggregateActivityLog = {
  _count: ActivityLogCountAggregateOutputType | null
  _min: ActivityLogMinAggregateOutputType | null
  _max: ActivityLogMaxAggregateOutputType | null
}

export type ActivityLogMinAggregateOutputType = {
  id: string | null
  userId: string | null
  action: string | null
  resource: string | null
  createdAt: Date | null
}

export type ActivityLogMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  action: string | null
  resource: string | null
  createdAt: Date | null
}

export type ActivityLogCountAggregateOutputType = {
  id: number
  userId: number
  action: number
  resource: number
  details: number
  createdAt: number
  _all: number
}

export type ActivityLogMinAggregateInputType = {
  id?: true
  userId?: true
  action?: true
  resource?: true
  createdAt?: true
}

export type ActivityLogMaxAggregateInputType = {
  id?: true
  userId?: true
  action?: true
  resource?: true
  createdAt?: true
}

export type ActivityLogCountAggregateInputType = {
  id?: true
  userId?: true
  action?: true
  resource?: true
  details?: true
  createdAt?: true
  _all?: true
}

export type ActivityLogAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which ActivityLog to aggregate.
   */
  where?: Prisma.ActivityLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of ActivityLogs to fetch.
   */
  orderBy?:
    | Prisma.ActivityLogOrderByWithRelationInput
    | Prisma.ActivityLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.ActivityLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` ActivityLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` ActivityLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned ActivityLogs
   **/
  _count?: true | ActivityLogCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: ActivityLogMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: ActivityLogMaxAggregateInputType
}

export type GetActivityLogAggregateType<T extends ActivityLogAggregateArgs> = {
  [P in keyof T & keyof AggregateActivityLog]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateActivityLog[P]>
    : Prisma.GetScalarType<T[P], AggregateActivityLog[P]>
}

export type ActivityLogGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.ActivityLogWhereInput
  orderBy?:
    | Prisma.ActivityLogOrderByWithAggregationInput
    | Prisma.ActivityLogOrderByWithAggregationInput[]
  by: Prisma.ActivityLogScalarFieldEnum[] | Prisma.ActivityLogScalarFieldEnum
  having?: Prisma.ActivityLogScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ActivityLogCountAggregateInputType | true
  _min?: ActivityLogMinAggregateInputType
  _max?: ActivityLogMaxAggregateInputType
}

export type ActivityLogGroupByOutputType = {
  id: string
  userId: string
  action: string
  resource: string
  details: runtime.JsonValue | null
  createdAt: Date
  _count: ActivityLogCountAggregateOutputType | null
  _min: ActivityLogMinAggregateOutputType | null
  _max: ActivityLogMaxAggregateOutputType | null
}

type GetActivityLogGroupByPayload<T extends ActivityLogGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ActivityLogGroupByOutputType, T['by']> & {
      [P in keyof T & keyof ActivityLogGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], ActivityLogGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], ActivityLogGroupByOutputType[P]>
    }
  >
>

export type ActivityLogWhereInput = {
  AND?: Prisma.ActivityLogWhereInput | Prisma.ActivityLogWhereInput[]
  OR?: Prisma.ActivityLogWhereInput[]
  NOT?: Prisma.ActivityLogWhereInput | Prisma.ActivityLogWhereInput[]
  id?: Prisma.StringFilter<'ActivityLog'> | string
  userId?: Prisma.StringFilter<'ActivityLog'> | string
  action?: Prisma.StringFilter<'ActivityLog'> | string
  resource?: Prisma.StringFilter<'ActivityLog'> | string
  details?: Prisma.JsonNullableFilter<'ActivityLog'>
  createdAt?: Prisma.DateTimeFilter<'ActivityLog'> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type ActivityLogOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  action?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  details?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type ActivityLogWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    AND?: Prisma.ActivityLogWhereInput | Prisma.ActivityLogWhereInput[]
    OR?: Prisma.ActivityLogWhereInput[]
    NOT?: Prisma.ActivityLogWhereInput | Prisma.ActivityLogWhereInput[]
    userId?: Prisma.StringFilter<'ActivityLog'> | string
    action?: Prisma.StringFilter<'ActivityLog'> | string
    resource?: Prisma.StringFilter<'ActivityLog'> | string
    details?: Prisma.JsonNullableFilter<'ActivityLog'>
    createdAt?: Prisma.DateTimeFilter<'ActivityLog'> | Date | string
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id'
>

export type ActivityLogOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  action?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  details?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.ActivityLogCountOrderByAggregateInput
  _max?: Prisma.ActivityLogMaxOrderByAggregateInput
  _min?: Prisma.ActivityLogMinOrderByAggregateInput
}

export type ActivityLogScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.ActivityLogScalarWhereWithAggregatesInput
    | Prisma.ActivityLogScalarWhereWithAggregatesInput[]
  OR?: Prisma.ActivityLogScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.ActivityLogScalarWhereWithAggregatesInput
    | Prisma.ActivityLogScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'ActivityLog'> | string
  userId?: Prisma.StringWithAggregatesFilter<'ActivityLog'> | string
  action?: Prisma.StringWithAggregatesFilter<'ActivityLog'> | string
  resource?: Prisma.StringWithAggregatesFilter<'ActivityLog'> | string
  details?: Prisma.JsonNullableWithAggregatesFilter<'ActivityLog'>
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'ActivityLog'> | Date | string
}

export type ActivityLogCreateInput = {
  id?: string
  action: string
  resource: string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutActivityLogsInput
}

export type ActivityLogUncheckedCreateInput = {
  id?: string
  userId: string
  action: string
  resource: string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type ActivityLogUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutActivityLogsNestedInput
}

export type ActivityLogUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ActivityLogCreateManyInput = {
  id?: string
  userId: string
  action: string
  resource: string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type ActivityLogUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ActivityLogUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ActivityLogCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  action?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  details?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ActivityLogMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  action?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ActivityLogMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  action?: Prisma.SortOrder
  resource?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ActivityLogListRelationFilter = {
  every?: Prisma.ActivityLogWhereInput
  some?: Prisma.ActivityLogWhereInput
  none?: Prisma.ActivityLogWhereInput
}

export type ActivityLogOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ActivityLogCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.ActivityLogCreateWithoutUserInput,
        Prisma.ActivityLogUncheckedCreateWithoutUserInput
      >
    | Prisma.ActivityLogCreateWithoutUserInput[]
    | Prisma.ActivityLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ActivityLogCreateManyUserInputEnvelope
  connect?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
}

export type ActivityLogUncheckedCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.ActivityLogCreateWithoutUserInput,
        Prisma.ActivityLogUncheckedCreateWithoutUserInput
      >
    | Prisma.ActivityLogCreateWithoutUserInput[]
    | Prisma.ActivityLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ActivityLogCreateManyUserInputEnvelope
  connect?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
}

export type ActivityLogUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.ActivityLogCreateWithoutUserInput,
        Prisma.ActivityLogUncheckedCreateWithoutUserInput
      >
    | Prisma.ActivityLogCreateWithoutUserInput[]
    | Prisma.ActivityLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.ActivityLogUpsertWithWhereUniqueWithoutUserInput
    | Prisma.ActivityLogUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ActivityLogCreateManyUserInputEnvelope
  set?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  disconnect?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  delete?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  connect?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  update?:
    | Prisma.ActivityLogUpdateWithWhereUniqueWithoutUserInput
    | Prisma.ActivityLogUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.ActivityLogUpdateManyWithWhereWithoutUserInput
    | Prisma.ActivityLogUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ActivityLogScalarWhereInput | Prisma.ActivityLogScalarWhereInput[]
}

export type ActivityLogUncheckedUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.ActivityLogCreateWithoutUserInput,
        Prisma.ActivityLogUncheckedCreateWithoutUserInput
      >
    | Prisma.ActivityLogCreateWithoutUserInput[]
    | Prisma.ActivityLogUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput
    | Prisma.ActivityLogCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.ActivityLogUpsertWithWhereUniqueWithoutUserInput
    | Prisma.ActivityLogUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ActivityLogCreateManyUserInputEnvelope
  set?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  disconnect?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  delete?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  connect?: Prisma.ActivityLogWhereUniqueInput | Prisma.ActivityLogWhereUniqueInput[]
  update?:
    | Prisma.ActivityLogUpdateWithWhereUniqueWithoutUserInput
    | Prisma.ActivityLogUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.ActivityLogUpdateManyWithWhereWithoutUserInput
    | Prisma.ActivityLogUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ActivityLogScalarWhereInput | Prisma.ActivityLogScalarWhereInput[]
}

export type ActivityLogCreateWithoutUserInput = {
  id?: string
  action: string
  resource: string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type ActivityLogUncheckedCreateWithoutUserInput = {
  id?: string
  action: string
  resource: string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type ActivityLogCreateOrConnectWithoutUserInput = {
  where: Prisma.ActivityLogWhereUniqueInput
  create: Prisma.XOR<
    Prisma.ActivityLogCreateWithoutUserInput,
    Prisma.ActivityLogUncheckedCreateWithoutUserInput
  >
}

export type ActivityLogCreateManyUserInputEnvelope = {
  data: Prisma.ActivityLogCreateManyUserInput | Prisma.ActivityLogCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type ActivityLogUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.ActivityLogWhereUniqueInput
  update: Prisma.XOR<
    Prisma.ActivityLogUpdateWithoutUserInput,
    Prisma.ActivityLogUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.ActivityLogCreateWithoutUserInput,
    Prisma.ActivityLogUncheckedCreateWithoutUserInput
  >
}

export type ActivityLogUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.ActivityLogWhereUniqueInput
  data: Prisma.XOR<
    Prisma.ActivityLogUpdateWithoutUserInput,
    Prisma.ActivityLogUncheckedUpdateWithoutUserInput
  >
}

export type ActivityLogUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.ActivityLogScalarWhereInput
  data: Prisma.XOR<
    Prisma.ActivityLogUpdateManyMutationInput,
    Prisma.ActivityLogUncheckedUpdateManyWithoutUserInput
  >
}

export type ActivityLogScalarWhereInput = {
  AND?: Prisma.ActivityLogScalarWhereInput | Prisma.ActivityLogScalarWhereInput[]
  OR?: Prisma.ActivityLogScalarWhereInput[]
  NOT?: Prisma.ActivityLogScalarWhereInput | Prisma.ActivityLogScalarWhereInput[]
  id?: Prisma.StringFilter<'ActivityLog'> | string
  userId?: Prisma.StringFilter<'ActivityLog'> | string
  action?: Prisma.StringFilter<'ActivityLog'> | string
  resource?: Prisma.StringFilter<'ActivityLog'> | string
  details?: Prisma.JsonNullableFilter<'ActivityLog'>
  createdAt?: Prisma.DateTimeFilter<'ActivityLog'> | Date | string
}

export type ActivityLogCreateManyUserInput = {
  id?: string
  action: string
  resource: string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type ActivityLogUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ActivityLogUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ActivityLogUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  resource?: Prisma.StringFieldUpdateOperationsInput | string
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ActivityLogSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    action?: boolean
    resource?: boolean
    details?: boolean
    createdAt?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['activityLog']
>

export type ActivityLogSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    action?: boolean
    resource?: boolean
    details?: boolean
    createdAt?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['activityLog']
>

export type ActivityLogSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    action?: boolean
    resource?: boolean
    details?: boolean
    createdAt?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['activityLog']
>

export type ActivityLogSelectScalar = {
  id?: boolean
  userId?: boolean
  action?: boolean
  resource?: boolean
  details?: boolean
  createdAt?: boolean
}

export type ActivityLogOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'userId' | 'action' | 'resource' | 'details' | 'createdAt',
  ExtArgs['result']['activityLog']
>
export type ActivityLogInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ActivityLogIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ActivityLogIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $ActivityLogPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'ActivityLog'
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      userId: string
      action: string
      resource: string
      details: runtime.JsonValue | null
      createdAt: Date
    },
    ExtArgs['result']['activityLog']
  >
  composites: {}
}

export type ActivityLogGetPayload<S extends boolean | null | undefined | ActivityLogDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$ActivityLogPayload, S>

export type ActivityLogCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<ActivityLogFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: ActivityLogCountAggregateInputType | true
}

export interface ActivityLogDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['ActivityLog']
    meta: { name: 'ActivityLog' }
  }
  /**
   * Find zero or one ActivityLog that matches the filter.
   * @param {ActivityLogFindUniqueArgs} args - Arguments to find a ActivityLog
   * @example
   * // Get one ActivityLog
   * const activityLog = await prisma.activityLog.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ActivityLogFindUniqueArgs>(
    args: Prisma.SelectSubset<T, ActivityLogFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one ActivityLog that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ActivityLogFindUniqueOrThrowArgs} args - Arguments to find a ActivityLog
   * @example
   * // Get one ActivityLog
   * const activityLog = await prisma.activityLog.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ActivityLogFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, ActivityLogFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first ActivityLog that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActivityLogFindFirstArgs} args - Arguments to find a ActivityLog
   * @example
   * // Get one ActivityLog
   * const activityLog = await prisma.activityLog.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ActivityLogFindFirstArgs>(
    args?: Prisma.SelectSubset<T, ActivityLogFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first ActivityLog that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActivityLogFindFirstOrThrowArgs} args - Arguments to find a ActivityLog
   * @example
   * // Get one ActivityLog
   * const activityLog = await prisma.activityLog.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ActivityLogFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, ActivityLogFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more ActivityLogs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActivityLogFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ActivityLogs
   * const activityLogs = await prisma.activityLog.findMany()
   *
   * // Get first 10 ActivityLogs
   * const activityLogs = await prisma.activityLog.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const activityLogWithIdOnly = await prisma.activityLog.findMany({ select: { id: true } })
   *
   */
  findMany<T extends ActivityLogFindManyArgs>(
    args?: Prisma.SelectSubset<T, ActivityLogFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a ActivityLog.
   * @param {ActivityLogCreateArgs} args - Arguments to create a ActivityLog.
   * @example
   * // Create one ActivityLog
   * const ActivityLog = await prisma.activityLog.create({
   *   data: {
   *     // ... data to create a ActivityLog
   *   }
   * })
   *
   */
  create<T extends ActivityLogCreateArgs>(
    args: Prisma.SelectSubset<T, ActivityLogCreateArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many ActivityLogs.
   * @param {ActivityLogCreateManyArgs} args - Arguments to create many ActivityLogs.
   * @example
   * // Create many ActivityLogs
   * const activityLog = await prisma.activityLog.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends ActivityLogCreateManyArgs>(
    args?: Prisma.SelectSubset<T, ActivityLogCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ActivityLogs and returns the data saved in the database.
   * @param {ActivityLogCreateManyAndReturnArgs} args - Arguments to create many ActivityLogs.
   * @example
   * // Create many ActivityLogs
   * const activityLog = await prisma.activityLog.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many ActivityLogs and only return the `id`
   * const activityLogWithIdOnly = await prisma.activityLog.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends ActivityLogCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, ActivityLogCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a ActivityLog.
   * @param {ActivityLogDeleteArgs} args - Arguments to delete one ActivityLog.
   * @example
   * // Delete one ActivityLog
   * const ActivityLog = await prisma.activityLog.delete({
   *   where: {
   *     // ... filter to delete one ActivityLog
   *   }
   * })
   *
   */
  delete<T extends ActivityLogDeleteArgs>(
    args: Prisma.SelectSubset<T, ActivityLogDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one ActivityLog.
   * @param {ActivityLogUpdateArgs} args - Arguments to update one ActivityLog.
   * @example
   * // Update one ActivityLog
   * const activityLog = await prisma.activityLog.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends ActivityLogUpdateArgs>(
    args: Prisma.SelectSubset<T, ActivityLogUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more ActivityLogs.
   * @param {ActivityLogDeleteManyArgs} args - Arguments to filter ActivityLogs to delete.
   * @example
   * // Delete a few ActivityLogs
   * const { count } = await prisma.activityLog.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends ActivityLogDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, ActivityLogDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ActivityLogs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActivityLogUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ActivityLogs
   * const activityLog = await prisma.activityLog.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends ActivityLogUpdateManyArgs>(
    args: Prisma.SelectSubset<T, ActivityLogUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ActivityLogs and returns the data updated in the database.
   * @param {ActivityLogUpdateManyAndReturnArgs} args - Arguments to update many ActivityLogs.
   * @example
   * // Update many ActivityLogs
   * const activityLog = await prisma.activityLog.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more ActivityLogs and only return the `id`
   * const activityLogWithIdOnly = await prisma.activityLog.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends ActivityLogUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, ActivityLogUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one ActivityLog.
   * @param {ActivityLogUpsertArgs} args - Arguments to update or create a ActivityLog.
   * @example
   * // Update or create a ActivityLog
   * const activityLog = await prisma.activityLog.upsert({
   *   create: {
   *     // ... data to create a ActivityLog
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ActivityLog we want to update
   *   }
   * })
   */
  upsert<T extends ActivityLogUpsertArgs>(
    args: Prisma.SelectSubset<T, ActivityLogUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__ActivityLogClient<
    runtime.Types.Result.GetResult<
      Prisma.$ActivityLogPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of ActivityLogs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActivityLogCountArgs} args - Arguments to filter ActivityLogs to count.
   * @example
   * // Count the number of ActivityLogs
   * const count = await prisma.activityLog.count({
   *   where: {
   *     // ... the filter for the ActivityLogs we want to count
   *   }
   * })
   **/
  count<T extends ActivityLogCountArgs>(
    args?: Prisma.Subset<T, ActivityLogCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ActivityLogCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ActivityLog.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActivityLogAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends ActivityLogAggregateArgs>(
    args: Prisma.Subset<T, ActivityLogAggregateArgs>
  ): Prisma.PrismaPromise<GetActivityLogAggregateType<T>>

  /**
   * Group by ActivityLog.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ActivityLogGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends ActivityLogGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ActivityLogGroupByArgs['orderBy'] }
      : { orderBy?: ActivityLogGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, ActivityLogGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetActivityLogGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ActivityLog model
   */
  readonly fields: ActivityLogFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for ActivityLog.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ActivityLogClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the ActivityLog model
 */
export interface ActivityLogFieldRefs {
  readonly id: Prisma.FieldRef<'ActivityLog', 'String'>
  readonly userId: Prisma.FieldRef<'ActivityLog', 'String'>
  readonly action: Prisma.FieldRef<'ActivityLog', 'String'>
  readonly resource: Prisma.FieldRef<'ActivityLog', 'String'>
  readonly details: Prisma.FieldRef<'ActivityLog', 'Json'>
  readonly createdAt: Prisma.FieldRef<'ActivityLog', 'DateTime'>
}

// Custom InputTypes
/**
 * ActivityLog findUnique
 */
export type ActivityLogFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * Filter, which ActivityLog to fetch.
   */
  where: Prisma.ActivityLogWhereUniqueInput
}

/**
 * ActivityLog findUniqueOrThrow
 */
export type ActivityLogFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * Filter, which ActivityLog to fetch.
   */
  where: Prisma.ActivityLogWhereUniqueInput
}

/**
 * ActivityLog findFirst
 */
export type ActivityLogFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * Filter, which ActivityLog to fetch.
   */
  where?: Prisma.ActivityLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of ActivityLogs to fetch.
   */
  orderBy?:
    | Prisma.ActivityLogOrderByWithRelationInput
    | Prisma.ActivityLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for ActivityLogs.
   */
  cursor?: Prisma.ActivityLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` ActivityLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` ActivityLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of ActivityLogs.
   */
  distinct?: Prisma.ActivityLogScalarFieldEnum | Prisma.ActivityLogScalarFieldEnum[]
}

/**
 * ActivityLog findFirstOrThrow
 */
export type ActivityLogFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * Filter, which ActivityLog to fetch.
   */
  where?: Prisma.ActivityLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of ActivityLogs to fetch.
   */
  orderBy?:
    | Prisma.ActivityLogOrderByWithRelationInput
    | Prisma.ActivityLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for ActivityLogs.
   */
  cursor?: Prisma.ActivityLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` ActivityLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` ActivityLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of ActivityLogs.
   */
  distinct?: Prisma.ActivityLogScalarFieldEnum | Prisma.ActivityLogScalarFieldEnum[]
}

/**
 * ActivityLog findMany
 */
export type ActivityLogFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * Filter, which ActivityLogs to fetch.
   */
  where?: Prisma.ActivityLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of ActivityLogs to fetch.
   */
  orderBy?:
    | Prisma.ActivityLogOrderByWithRelationInput
    | Prisma.ActivityLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing ActivityLogs.
   */
  cursor?: Prisma.ActivityLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` ActivityLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` ActivityLogs.
   */
  skip?: number
  distinct?: Prisma.ActivityLogScalarFieldEnum | Prisma.ActivityLogScalarFieldEnum[]
}

/**
 * ActivityLog create
 */
export type ActivityLogCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * The data needed to create a ActivityLog.
   */
  data: Prisma.XOR<Prisma.ActivityLogCreateInput, Prisma.ActivityLogUncheckedCreateInput>
}

/**
 * ActivityLog createMany
 */
export type ActivityLogCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many ActivityLogs.
   */
  data: Prisma.ActivityLogCreateManyInput | Prisma.ActivityLogCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ActivityLog createManyAndReturn
 */
export type ActivityLogCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * The data used to create many ActivityLogs.
   */
  data: Prisma.ActivityLogCreateManyInput | Prisma.ActivityLogCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ActivityLog update
 */
export type ActivityLogUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * The data needed to update a ActivityLog.
   */
  data: Prisma.XOR<Prisma.ActivityLogUpdateInput, Prisma.ActivityLogUncheckedUpdateInput>
  /**
   * Choose, which ActivityLog to update.
   */
  where: Prisma.ActivityLogWhereUniqueInput
}

/**
 * ActivityLog updateMany
 */
export type ActivityLogUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update ActivityLogs.
   */
  data: Prisma.XOR<
    Prisma.ActivityLogUpdateManyMutationInput,
    Prisma.ActivityLogUncheckedUpdateManyInput
  >
  /**
   * Filter which ActivityLogs to update
   */
  where?: Prisma.ActivityLogWhereInput
  /**
   * Limit how many ActivityLogs to update.
   */
  limit?: number
}

/**
 * ActivityLog updateManyAndReturn
 */
export type ActivityLogUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * The data used to update ActivityLogs.
   */
  data: Prisma.XOR<
    Prisma.ActivityLogUpdateManyMutationInput,
    Prisma.ActivityLogUncheckedUpdateManyInput
  >
  /**
   * Filter which ActivityLogs to update
   */
  where?: Prisma.ActivityLogWhereInput
  /**
   * Limit how many ActivityLogs to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ActivityLog upsert
 */
export type ActivityLogUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * The filter to search for the ActivityLog to update in case it exists.
   */
  where: Prisma.ActivityLogWhereUniqueInput
  /**
   * In case the ActivityLog found by the `where` argument doesn't exist, create a new ActivityLog with this data.
   */
  create: Prisma.XOR<Prisma.ActivityLogCreateInput, Prisma.ActivityLogUncheckedCreateInput>
  /**
   * In case the ActivityLog was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ActivityLogUpdateInput, Prisma.ActivityLogUncheckedUpdateInput>
}

/**
 * ActivityLog delete
 */
export type ActivityLogDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
  /**
   * Filter which ActivityLog to delete.
   */
  where: Prisma.ActivityLogWhereUniqueInput
}

/**
 * ActivityLog deleteMany
 */
export type ActivityLogDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which ActivityLogs to delete
   */
  where?: Prisma.ActivityLogWhereInput
  /**
   * Limit how many ActivityLogs to delete.
   */
  limit?: number
}

/**
 * ActivityLog without action
 */
export type ActivityLogDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the ActivityLog
   */
  select?: Prisma.ActivityLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ActivityLog
   */
  omit?: Prisma.ActivityLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ActivityLogInclude<ExtArgs> | null
}
