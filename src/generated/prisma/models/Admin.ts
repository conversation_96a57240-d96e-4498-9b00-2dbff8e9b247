/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Admin` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Admin
 *
 */
export type AdminModel = runtime.Types.Result.DefaultSelection<Prisma.$AdminPayload>

export type AggregateAdmin = {
  _count: AdminCountAggregateOutputType | null
  _min: AdminMinAggregateOutputType | null
  _max: AdminMaxAggregateOutputType | null
}

export type AdminMinAggregateOutputType = {
  id: string | null
  userId: string | null
  adminLevel: string | null
}

export type AdminMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  adminLevel: string | null
}

export type AdminCountAggregateOutputType = {
  id: number
  userId: number
  adminLevel: number
  _all: number
}

export type AdminMinAggregateInputType = {
  id?: true
  userId?: true
  adminLevel?: true
}

export type AdminMaxAggregateInputType = {
  id?: true
  userId?: true
  adminLevel?: true
}

export type AdminCountAggregateInputType = {
  id?: true
  userId?: true
  adminLevel?: true
  _all?: true
}

export type AdminAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Admin to aggregate.
   */
  where?: Prisma.AdminWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Admins to fetch.
   */
  orderBy?: Prisma.AdminOrderByWithRelationInput | Prisma.AdminOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.AdminWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Admins from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Admins.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Admins
   **/
  _count?: true | AdminCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: AdminMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: AdminMaxAggregateInputType
}

export type GetAdminAggregateType<T extends AdminAggregateArgs> = {
  [P in keyof T & keyof AggregateAdmin]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAdmin[P]>
    : Prisma.GetScalarType<T[P], AggregateAdmin[P]>
}

export type AdminGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.AdminWhereInput
  orderBy?: Prisma.AdminOrderByWithAggregationInput | Prisma.AdminOrderByWithAggregationInput[]
  by: Prisma.AdminScalarFieldEnum[] | Prisma.AdminScalarFieldEnum
  having?: Prisma.AdminScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AdminCountAggregateInputType | true
  _min?: AdminMinAggregateInputType
  _max?: AdminMaxAggregateInputType
}

export type AdminGroupByOutputType = {
  id: string
  userId: string
  adminLevel: string
  _count: AdminCountAggregateOutputType | null
  _min: AdminMinAggregateOutputType | null
  _max: AdminMaxAggregateOutputType | null
}

type GetAdminGroupByPayload<T extends AdminGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AdminGroupByOutputType, T['by']> & {
      [P in keyof T & keyof AdminGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], AdminGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], AdminGroupByOutputType[P]>
    }
  >
>

export type AdminWhereInput = {
  AND?: Prisma.AdminWhereInput | Prisma.AdminWhereInput[]
  OR?: Prisma.AdminWhereInput[]
  NOT?: Prisma.AdminWhereInput | Prisma.AdminWhereInput[]
  id?: Prisma.StringFilter<'Admin'> | string
  userId?: Prisma.StringFilter<'Admin'> | string
  adminLevel?: Prisma.StringFilter<'Admin'> | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type AdminOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  adminLevel?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type AdminWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    userId?: string
    AND?: Prisma.AdminWhereInput | Prisma.AdminWhereInput[]
    OR?: Prisma.AdminWhereInput[]
    NOT?: Prisma.AdminWhereInput | Prisma.AdminWhereInput[]
    adminLevel?: Prisma.StringFilter<'Admin'> | string
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id' | 'userId'
>

export type AdminOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  adminLevel?: Prisma.SortOrder
  _count?: Prisma.AdminCountOrderByAggregateInput
  _max?: Prisma.AdminMaxOrderByAggregateInput
  _min?: Prisma.AdminMinOrderByAggregateInput
}

export type AdminScalarWhereWithAggregatesInput = {
  AND?: Prisma.AdminScalarWhereWithAggregatesInput | Prisma.AdminScalarWhereWithAggregatesInput[]
  OR?: Prisma.AdminScalarWhereWithAggregatesInput[]
  NOT?: Prisma.AdminScalarWhereWithAggregatesInput | Prisma.AdminScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Admin'> | string
  userId?: Prisma.StringWithAggregatesFilter<'Admin'> | string
  adminLevel?: Prisma.StringWithAggregatesFilter<'Admin'> | string
}

export type AdminCreateInput = {
  id?: string
  adminLevel: string
  user: Prisma.UserCreateNestedOneWithoutAdminProfileInput
}

export type AdminUncheckedCreateInput = {
  id?: string
  userId: string
  adminLevel: string
}

export type AdminUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  adminLevel?: Prisma.StringFieldUpdateOperationsInput | string
  user?: Prisma.UserUpdateOneRequiredWithoutAdminProfileNestedInput
}

export type AdminUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  adminLevel?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AdminCreateManyInput = {
  id?: string
  userId: string
  adminLevel: string
}

export type AdminUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  adminLevel?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AdminUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  adminLevel?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AdminNullableScalarRelationFilter = {
  is?: Prisma.AdminWhereInput | null
  isNot?: Prisma.AdminWhereInput | null
}

export type AdminCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  adminLevel?: Prisma.SortOrder
}

export type AdminMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  adminLevel?: Prisma.SortOrder
}

export type AdminMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  adminLevel?: Prisma.SortOrder
}

export type AdminCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.AdminCreateWithoutUserInput,
    Prisma.AdminUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.AdminCreateOrConnectWithoutUserInput
  connect?: Prisma.AdminWhereUniqueInput
}

export type AdminUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.AdminCreateWithoutUserInput,
    Prisma.AdminUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.AdminCreateOrConnectWithoutUserInput
  connect?: Prisma.AdminWhereUniqueInput
}

export type AdminUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.AdminCreateWithoutUserInput,
    Prisma.AdminUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.AdminCreateOrConnectWithoutUserInput
  upsert?: Prisma.AdminUpsertWithoutUserInput
  disconnect?: Prisma.AdminWhereInput | boolean
  delete?: Prisma.AdminWhereInput | boolean
  connect?: Prisma.AdminWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.AdminUpdateToOneWithWhereWithoutUserInput,
      Prisma.AdminUpdateWithoutUserInput
    >,
    Prisma.AdminUncheckedUpdateWithoutUserInput
  >
}

export type AdminUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.AdminCreateWithoutUserInput,
    Prisma.AdminUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.AdminCreateOrConnectWithoutUserInput
  upsert?: Prisma.AdminUpsertWithoutUserInput
  disconnect?: Prisma.AdminWhereInput | boolean
  delete?: Prisma.AdminWhereInput | boolean
  connect?: Prisma.AdminWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.AdminUpdateToOneWithWhereWithoutUserInput,
      Prisma.AdminUpdateWithoutUserInput
    >,
    Prisma.AdminUncheckedUpdateWithoutUserInput
  >
}

export type AdminCreateWithoutUserInput = {
  id?: string
  adminLevel: string
}

export type AdminUncheckedCreateWithoutUserInput = {
  id?: string
  adminLevel: string
}

export type AdminCreateOrConnectWithoutUserInput = {
  where: Prisma.AdminWhereUniqueInput
  create: Prisma.XOR<
    Prisma.AdminCreateWithoutUserInput,
    Prisma.AdminUncheckedCreateWithoutUserInput
  >
}

export type AdminUpsertWithoutUserInput = {
  update: Prisma.XOR<
    Prisma.AdminUpdateWithoutUserInput,
    Prisma.AdminUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.AdminCreateWithoutUserInput,
    Prisma.AdminUncheckedCreateWithoutUserInput
  >
  where?: Prisma.AdminWhereInput
}

export type AdminUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.AdminWhereInput
  data: Prisma.XOR<Prisma.AdminUpdateWithoutUserInput, Prisma.AdminUncheckedUpdateWithoutUserInput>
}

export type AdminUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  adminLevel?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AdminUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  adminLevel?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AdminSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    adminLevel?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['admin']
>

export type AdminSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    adminLevel?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['admin']
>

export type AdminSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    adminLevel?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['admin']
>

export type AdminSelectScalar = {
  id?: boolean
  userId?: boolean
  adminLevel?: boolean
}

export type AdminOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<'id' | 'userId' | 'adminLevel', ExtArgs['result']['admin']>
export type AdminInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type AdminIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type AdminIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $AdminPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Admin'
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      userId: string
      adminLevel: string
    },
    ExtArgs['result']['admin']
  >
  composites: {}
}

export type AdminGetPayload<S extends boolean | null | undefined | AdminDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$AdminPayload, S>

export type AdminCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<AdminFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: AdminCountAggregateInputType | true
}

export interface AdminDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Admin']; meta: { name: 'Admin' } }
  /**
   * Find zero or one Admin that matches the filter.
   * @param {AdminFindUniqueArgs} args - Arguments to find a Admin
   * @example
   * // Get one Admin
   * const admin = await prisma.admin.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends AdminFindUniqueArgs>(
    args: Prisma.SelectSubset<T, AdminFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<
      Prisma.$AdminPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Admin that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {AdminFindUniqueOrThrowArgs} args - Arguments to find a Admin
   * @example
   * // Get one Admin
   * const admin = await prisma.admin.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends AdminFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, AdminFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<
      Prisma.$AdminPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Admin that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AdminFindFirstArgs} args - Arguments to find a Admin
   * @example
   * // Get one Admin
   * const admin = await prisma.admin.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends AdminFindFirstArgs>(
    args?: Prisma.SelectSubset<T, AdminFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<
      Prisma.$AdminPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Admin that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AdminFindFirstOrThrowArgs} args - Arguments to find a Admin
   * @example
   * // Get one Admin
   * const admin = await prisma.admin.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends AdminFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, AdminFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<
      Prisma.$AdminPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Admins that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AdminFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Admins
   * const admins = await prisma.admin.findMany()
   *
   * // Get first 10 Admins
   * const admins = await prisma.admin.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const adminWithIdOnly = await prisma.admin.findMany({ select: { id: true } })
   *
   */
  findMany<T extends AdminFindManyArgs>(
    args?: Prisma.SelectSubset<T, AdminFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, 'findMany', GlobalOmitOptions>
  >

  /**
   * Create a Admin.
   * @param {AdminCreateArgs} args - Arguments to create a Admin.
   * @example
   * // Create one Admin
   * const Admin = await prisma.admin.create({
   *   data: {
   *     // ... data to create a Admin
   *   }
   * })
   *
   */
  create<T extends AdminCreateArgs>(
    args: Prisma.SelectSubset<T, AdminCreateArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Admins.
   * @param {AdminCreateManyArgs} args - Arguments to create many Admins.
   * @example
   * // Create many Admins
   * const admin = await prisma.admin.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends AdminCreateManyArgs>(
    args?: Prisma.SelectSubset<T, AdminCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Admins and returns the data saved in the database.
   * @param {AdminCreateManyAndReturnArgs} args - Arguments to create many Admins.
   * @example
   * // Create many Admins
   * const admin = await prisma.admin.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Admins and only return the `id`
   * const adminWithIdOnly = await prisma.admin.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends AdminCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, AdminCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$AdminPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Admin.
   * @param {AdminDeleteArgs} args - Arguments to delete one Admin.
   * @example
   * // Delete one Admin
   * const Admin = await prisma.admin.delete({
   *   where: {
   *     // ... filter to delete one Admin
   *   }
   * })
   *
   */
  delete<T extends AdminDeleteArgs>(
    args: Prisma.SelectSubset<T, AdminDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Admin.
   * @param {AdminUpdateArgs} args - Arguments to update one Admin.
   * @example
   * // Update one Admin
   * const admin = await prisma.admin.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends AdminUpdateArgs>(
    args: Prisma.SelectSubset<T, AdminUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Admins.
   * @param {AdminDeleteManyArgs} args - Arguments to filter Admins to delete.
   * @example
   * // Delete a few Admins
   * const { count } = await prisma.admin.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends AdminDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, AdminDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Admins.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AdminUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Admins
   * const admin = await prisma.admin.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends AdminUpdateManyArgs>(
    args: Prisma.SelectSubset<T, AdminUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Admins and returns the data updated in the database.
   * @param {AdminUpdateManyAndReturnArgs} args - Arguments to update many Admins.
   * @example
   * // Update many Admins
   * const admin = await prisma.admin.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Admins and only return the `id`
   * const adminWithIdOnly = await prisma.admin.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends AdminUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, AdminUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$AdminPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Admin.
   * @param {AdminUpsertArgs} args - Arguments to update or create a Admin.
   * @example
   * // Update or create a Admin
   * const admin = await prisma.admin.upsert({
   *   create: {
   *     // ... data to create a Admin
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Admin we want to update
   *   }
   * })
   */
  upsert<T extends AdminUpsertArgs>(
    args: Prisma.SelectSubset<T, AdminUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__AdminClient<
    runtime.Types.Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Admins.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AdminCountArgs} args - Arguments to filter Admins to count.
   * @example
   * // Count the number of Admins
   * const count = await prisma.admin.count({
   *   where: {
   *     // ... the filter for the Admins we want to count
   *   }
   * })
   **/
  count<T extends AdminCountArgs>(
    args?: Prisma.Subset<T, AdminCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AdminCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Admin.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AdminAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends AdminAggregateArgs>(
    args: Prisma.Subset<T, AdminAggregateArgs>
  ): Prisma.PrismaPromise<GetAdminAggregateType<T>>

  /**
   * Group by Admin.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AdminGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends AdminGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: AdminGroupByArgs['orderBy'] }
      : { orderBy?: AdminGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, AdminGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetAdminGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Admin model
   */
  readonly fields: AdminFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Admin.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__AdminClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Admin model
 */
export interface AdminFieldRefs {
  readonly id: Prisma.FieldRef<'Admin', 'String'>
  readonly userId: Prisma.FieldRef<'Admin', 'String'>
  readonly adminLevel: Prisma.FieldRef<'Admin', 'String'>
}

// Custom InputTypes
/**
 * Admin findUnique
 */
export type AdminFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * Filter, which Admin to fetch.
   */
  where: Prisma.AdminWhereUniqueInput
}

/**
 * Admin findUniqueOrThrow
 */
export type AdminFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * Filter, which Admin to fetch.
   */
  where: Prisma.AdminWhereUniqueInput
}

/**
 * Admin findFirst
 */
export type AdminFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * Filter, which Admin to fetch.
   */
  where?: Prisma.AdminWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Admins to fetch.
   */
  orderBy?: Prisma.AdminOrderByWithRelationInput | Prisma.AdminOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Admins.
   */
  cursor?: Prisma.AdminWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Admins from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Admins.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Admins.
   */
  distinct?: Prisma.AdminScalarFieldEnum | Prisma.AdminScalarFieldEnum[]
}

/**
 * Admin findFirstOrThrow
 */
export type AdminFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * Filter, which Admin to fetch.
   */
  where?: Prisma.AdminWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Admins to fetch.
   */
  orderBy?: Prisma.AdminOrderByWithRelationInput | Prisma.AdminOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Admins.
   */
  cursor?: Prisma.AdminWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Admins from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Admins.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Admins.
   */
  distinct?: Prisma.AdminScalarFieldEnum | Prisma.AdminScalarFieldEnum[]
}

/**
 * Admin findMany
 */
export type AdminFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * Filter, which Admins to fetch.
   */
  where?: Prisma.AdminWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Admins to fetch.
   */
  orderBy?: Prisma.AdminOrderByWithRelationInput | Prisma.AdminOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Admins.
   */
  cursor?: Prisma.AdminWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Admins from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Admins.
   */
  skip?: number
  distinct?: Prisma.AdminScalarFieldEnum | Prisma.AdminScalarFieldEnum[]
}

/**
 * Admin create
 */
export type AdminCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * The data needed to create a Admin.
   */
  data: Prisma.XOR<Prisma.AdminCreateInput, Prisma.AdminUncheckedCreateInput>
}

/**
 * Admin createMany
 */
export type AdminCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Admins.
   */
  data: Prisma.AdminCreateManyInput | Prisma.AdminCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Admin createManyAndReturn
 */
export type AdminCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * The data used to create many Admins.
   */
  data: Prisma.AdminCreateManyInput | Prisma.AdminCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Admin update
 */
export type AdminUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * The data needed to update a Admin.
   */
  data: Prisma.XOR<Prisma.AdminUpdateInput, Prisma.AdminUncheckedUpdateInput>
  /**
   * Choose, which Admin to update.
   */
  where: Prisma.AdminWhereUniqueInput
}

/**
 * Admin updateMany
 */
export type AdminUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Admins.
   */
  data: Prisma.XOR<Prisma.AdminUpdateManyMutationInput, Prisma.AdminUncheckedUpdateManyInput>
  /**
   * Filter which Admins to update
   */
  where?: Prisma.AdminWhereInput
  /**
   * Limit how many Admins to update.
   */
  limit?: number
}

/**
 * Admin updateManyAndReturn
 */
export type AdminUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * The data used to update Admins.
   */
  data: Prisma.XOR<Prisma.AdminUpdateManyMutationInput, Prisma.AdminUncheckedUpdateManyInput>
  /**
   * Filter which Admins to update
   */
  where?: Prisma.AdminWhereInput
  /**
   * Limit how many Admins to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Admin upsert
 */
export type AdminUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * The filter to search for the Admin to update in case it exists.
   */
  where: Prisma.AdminWhereUniqueInput
  /**
   * In case the Admin found by the `where` argument doesn't exist, create a new Admin with this data.
   */
  create: Prisma.XOR<Prisma.AdminCreateInput, Prisma.AdminUncheckedCreateInput>
  /**
   * In case the Admin was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.AdminUpdateInput, Prisma.AdminUncheckedUpdateInput>
}

/**
 * Admin delete
 */
export type AdminDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
  /**
   * Filter which Admin to delete.
   */
  where: Prisma.AdminWhereUniqueInput
}

/**
 * Admin deleteMany
 */
export type AdminDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Admins to delete
   */
  where?: Prisma.AdminWhereInput
  /**
   * Limit how many Admins to delete.
   */
  limit?: number
}

/**
 * Admin without action
 */
export type AdminDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Admin
   */
  select?: Prisma.AdminSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Admin
   */
  omit?: Prisma.AdminOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AdminInclude<ExtArgs> | null
}
