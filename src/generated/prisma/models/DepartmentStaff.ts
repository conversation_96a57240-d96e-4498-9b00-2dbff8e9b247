/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `DepartmentStaff` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model DepartmentStaff
 *
 */
export type DepartmentStaffModel =
  runtime.Types.Result.DefaultSelection<Prisma.$DepartmentStaffPayload>

export type AggregateDepartmentStaff = {
  _count: DepartmentStaffCountAggregateOutputType | null
  _min: DepartmentStaffMinAggregateOutputType | null
  _max: DepartmentStaffMaxAggregateOutputType | null
}

export type DepartmentStaffMinAggregateOutputType = {
  id: string | null
  role: string | null
  createdAt: Date | null
  updatedAt: Date | null
  departmentId: string | null
  userId: string | null
}

export type DepartmentStaffMaxAggregateOutputType = {
  id: string | null
  role: string | null
  createdAt: Date | null
  updatedAt: Date | null
  departmentId: string | null
  userId: string | null
}

export type DepartmentStaffCountAggregateOutputType = {
  id: number
  role: number
  createdAt: number
  updatedAt: number
  departmentId: number
  userId: number
  _all: number
}

export type DepartmentStaffMinAggregateInputType = {
  id?: true
  role?: true
  createdAt?: true
  updatedAt?: true
  departmentId?: true
  userId?: true
}

export type DepartmentStaffMaxAggregateInputType = {
  id?: true
  role?: true
  createdAt?: true
  updatedAt?: true
  departmentId?: true
  userId?: true
}

export type DepartmentStaffCountAggregateInputType = {
  id?: true
  role?: true
  createdAt?: true
  updatedAt?: true
  departmentId?: true
  userId?: true
  _all?: true
}

export type DepartmentStaffAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which DepartmentStaff to aggregate.
   */
  where?: Prisma.DepartmentStaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of DepartmentStaffs to fetch.
   */
  orderBy?:
    | Prisma.DepartmentStaffOrderByWithRelationInput
    | Prisma.DepartmentStaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.DepartmentStaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` DepartmentStaffs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` DepartmentStaffs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned DepartmentStaffs
   **/
  _count?: true | DepartmentStaffCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: DepartmentStaffMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: DepartmentStaffMaxAggregateInputType
}

export type GetDepartmentStaffAggregateType<T extends DepartmentStaffAggregateArgs> = {
  [P in keyof T & keyof AggregateDepartmentStaff]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDepartmentStaff[P]>
    : Prisma.GetScalarType<T[P], AggregateDepartmentStaff[P]>
}

export type DepartmentStaffGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.DepartmentStaffWhereInput
  orderBy?:
    | Prisma.DepartmentStaffOrderByWithAggregationInput
    | Prisma.DepartmentStaffOrderByWithAggregationInput[]
  by: Prisma.DepartmentStaffScalarFieldEnum[] | Prisma.DepartmentStaffScalarFieldEnum
  having?: Prisma.DepartmentStaffScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: DepartmentStaffCountAggregateInputType | true
  _min?: DepartmentStaffMinAggregateInputType
  _max?: DepartmentStaffMaxAggregateInputType
}

export type DepartmentStaffGroupByOutputType = {
  id: string
  role: string
  createdAt: Date
  updatedAt: Date
  departmentId: string
  userId: string
  _count: DepartmentStaffCountAggregateOutputType | null
  _min: DepartmentStaffMinAggregateOutputType | null
  _max: DepartmentStaffMaxAggregateOutputType | null
}

type GetDepartmentStaffGroupByPayload<T extends DepartmentStaffGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<DepartmentStaffGroupByOutputType, T['by']> & {
      [P in keyof T & keyof DepartmentStaffGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], DepartmentStaffGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], DepartmentStaffGroupByOutputType[P]>
    }
  >
>

export type DepartmentStaffWhereInput = {
  AND?: Prisma.DepartmentStaffWhereInput | Prisma.DepartmentStaffWhereInput[]
  OR?: Prisma.DepartmentStaffWhereInput[]
  NOT?: Prisma.DepartmentStaffWhereInput | Prisma.DepartmentStaffWhereInput[]
  id?: Prisma.StringFilter<'DepartmentStaff'> | string
  role?: Prisma.StringFilter<'DepartmentStaff'> | string
  createdAt?: Prisma.DateTimeFilter<'DepartmentStaff'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'DepartmentStaff'> | Date | string
  departmentId?: Prisma.StringFilter<'DepartmentStaff'> | string
  userId?: Prisma.StringFilter<'DepartmentStaff'> | string
  department?: Prisma.XOR<Prisma.DepartmentScalarRelationFilter, Prisma.DepartmentWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type DepartmentStaffOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  departmentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  department?: Prisma.DepartmentOrderByWithRelationInput
  user?: Prisma.UserOrderByWithRelationInput
}

export type DepartmentStaffWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    AND?: Prisma.DepartmentStaffWhereInput | Prisma.DepartmentStaffWhereInput[]
    OR?: Prisma.DepartmentStaffWhereInput[]
    NOT?: Prisma.DepartmentStaffWhereInput | Prisma.DepartmentStaffWhereInput[]
    role?: Prisma.StringFilter<'DepartmentStaff'> | string
    createdAt?: Prisma.DateTimeFilter<'DepartmentStaff'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'DepartmentStaff'> | Date | string
    departmentId?: Prisma.StringFilter<'DepartmentStaff'> | string
    userId?: Prisma.StringFilter<'DepartmentStaff'> | string
    department?: Prisma.XOR<Prisma.DepartmentScalarRelationFilter, Prisma.DepartmentWhereInput>
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  },
  'id'
>

export type DepartmentStaffOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  departmentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.DepartmentStaffCountOrderByAggregateInput
  _max?: Prisma.DepartmentStaffMaxOrderByAggregateInput
  _min?: Prisma.DepartmentStaffMinOrderByAggregateInput
}

export type DepartmentStaffScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.DepartmentStaffScalarWhereWithAggregatesInput
    | Prisma.DepartmentStaffScalarWhereWithAggregatesInput[]
  OR?: Prisma.DepartmentStaffScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.DepartmentStaffScalarWhereWithAggregatesInput
    | Prisma.DepartmentStaffScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'DepartmentStaff'> | string
  role?: Prisma.StringWithAggregatesFilter<'DepartmentStaff'> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'DepartmentStaff'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'DepartmentStaff'> | Date | string
  departmentId?: Prisma.StringWithAggregatesFilter<'DepartmentStaff'> | string
  userId?: Prisma.StringWithAggregatesFilter<'DepartmentStaff'> | string
}

export type DepartmentStaffCreateInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  department: Prisma.DepartmentCreateNestedOneWithoutStaffsInput
  user: Prisma.UserCreateNestedOneWithoutDepartmentsInput
}

export type DepartmentStaffUncheckedCreateInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  departmentId: string
  userId: string
}

export type DepartmentStaffUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  department?: Prisma.DepartmentUpdateOneRequiredWithoutStaffsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutDepartmentsNestedInput
}

export type DepartmentStaffUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  departmentId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentStaffCreateManyInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  departmentId: string
  userId: string
}

export type DepartmentStaffUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DepartmentStaffUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  departmentId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentStaffListRelationFilter = {
  every?: Prisma.DepartmentStaffWhereInput
  some?: Prisma.DepartmentStaffWhereInput
  none?: Prisma.DepartmentStaffWhereInput
}

export type DepartmentStaffOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type DepartmentStaffCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  departmentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type DepartmentStaffMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  departmentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type DepartmentStaffMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  departmentId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type DepartmentStaffCreateNestedManyWithoutDepartmentInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutDepartmentInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput
      >
    | Prisma.DepartmentStaffCreateWithoutDepartmentInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput[]
  createMany?: Prisma.DepartmentStaffCreateManyDepartmentInputEnvelope
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
}

export type DepartmentStaffUncheckedCreateNestedManyWithoutDepartmentInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutDepartmentInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput
      >
    | Prisma.DepartmentStaffCreateWithoutDepartmentInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput[]
  createMany?: Prisma.DepartmentStaffCreateManyDepartmentInputEnvelope
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
}

export type DepartmentStaffUpdateManyWithoutDepartmentNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutDepartmentInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput
      >
    | Prisma.DepartmentStaffCreateWithoutDepartmentInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput[]
  upsert?:
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutDepartmentInput
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutDepartmentInput[]
  createMany?: Prisma.DepartmentStaffCreateManyDepartmentInputEnvelope
  set?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  disconnect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  delete?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  update?:
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutDepartmentInput
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutDepartmentInput[]
  updateMany?:
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutDepartmentInput
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutDepartmentInput[]
  deleteMany?: Prisma.DepartmentStaffScalarWhereInput | Prisma.DepartmentStaffScalarWhereInput[]
}

export type DepartmentStaffUncheckedUpdateManyWithoutDepartmentNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutDepartmentInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput
      >
    | Prisma.DepartmentStaffCreateWithoutDepartmentInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutDepartmentInput[]
  upsert?:
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutDepartmentInput
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutDepartmentInput[]
  createMany?: Prisma.DepartmentStaffCreateManyDepartmentInputEnvelope
  set?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  disconnect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  delete?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  update?:
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutDepartmentInput
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutDepartmentInput[]
  updateMany?:
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutDepartmentInput
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutDepartmentInput[]
  deleteMany?: Prisma.DepartmentStaffScalarWhereInput | Prisma.DepartmentStaffScalarWhereInput[]
}

export type DepartmentStaffCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutUserInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutUserInput
      >
    | Prisma.DepartmentStaffCreateWithoutUserInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.DepartmentStaffCreateManyUserInputEnvelope
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
}

export type DepartmentStaffUncheckedCreateNestedManyWithoutUserInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutUserInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutUserInput
      >
    | Prisma.DepartmentStaffCreateWithoutUserInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.DepartmentStaffCreateManyUserInputEnvelope
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
}

export type DepartmentStaffUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutUserInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutUserInput
      >
    | Prisma.DepartmentStaffCreateWithoutUserInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutUserInput
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.DepartmentStaffCreateManyUserInputEnvelope
  set?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  disconnect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  delete?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  update?:
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutUserInput
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutUserInput
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.DepartmentStaffScalarWhereInput | Prisma.DepartmentStaffScalarWhereInput[]
}

export type DepartmentStaffUncheckedUpdateManyWithoutUserNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.DepartmentStaffCreateWithoutUserInput,
        Prisma.DepartmentStaffUncheckedCreateWithoutUserInput
      >
    | Prisma.DepartmentStaffCreateWithoutUserInput[]
    | Prisma.DepartmentStaffUncheckedCreateWithoutUserInput[]
  connectOrCreate?:
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput
    | Prisma.DepartmentStaffCreateOrConnectWithoutUserInput[]
  upsert?:
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutUserInput
    | Prisma.DepartmentStaffUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.DepartmentStaffCreateManyUserInputEnvelope
  set?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  disconnect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  delete?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  connect?: Prisma.DepartmentStaffWhereUniqueInput | Prisma.DepartmentStaffWhereUniqueInput[]
  update?:
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutUserInput
    | Prisma.DepartmentStaffUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?:
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutUserInput
    | Prisma.DepartmentStaffUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.DepartmentStaffScalarWhereInput | Prisma.DepartmentStaffScalarWhereInput[]
}

export type DepartmentStaffCreateWithoutDepartmentInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutDepartmentsInput
}

export type DepartmentStaffUncheckedCreateWithoutDepartmentInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: string
}

export type DepartmentStaffCreateOrConnectWithoutDepartmentInput = {
  where: Prisma.DepartmentStaffWhereUniqueInput
  create: Prisma.XOR<
    Prisma.DepartmentStaffCreateWithoutDepartmentInput,
    Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput
  >
}

export type DepartmentStaffCreateManyDepartmentInputEnvelope = {
  data:
    | Prisma.DepartmentStaffCreateManyDepartmentInput
    | Prisma.DepartmentStaffCreateManyDepartmentInput[]
  skipDuplicates?: boolean
}

export type DepartmentStaffUpsertWithWhereUniqueWithoutDepartmentInput = {
  where: Prisma.DepartmentStaffWhereUniqueInput
  update: Prisma.XOR<
    Prisma.DepartmentStaffUpdateWithoutDepartmentInput,
    Prisma.DepartmentStaffUncheckedUpdateWithoutDepartmentInput
  >
  create: Prisma.XOR<
    Prisma.DepartmentStaffCreateWithoutDepartmentInput,
    Prisma.DepartmentStaffUncheckedCreateWithoutDepartmentInput
  >
}

export type DepartmentStaffUpdateWithWhereUniqueWithoutDepartmentInput = {
  where: Prisma.DepartmentStaffWhereUniqueInput
  data: Prisma.XOR<
    Prisma.DepartmentStaffUpdateWithoutDepartmentInput,
    Prisma.DepartmentStaffUncheckedUpdateWithoutDepartmentInput
  >
}

export type DepartmentStaffUpdateManyWithWhereWithoutDepartmentInput = {
  where: Prisma.DepartmentStaffScalarWhereInput
  data: Prisma.XOR<
    Prisma.DepartmentStaffUpdateManyMutationInput,
    Prisma.DepartmentStaffUncheckedUpdateManyWithoutDepartmentInput
  >
}

export type DepartmentStaffScalarWhereInput = {
  AND?: Prisma.DepartmentStaffScalarWhereInput | Prisma.DepartmentStaffScalarWhereInput[]
  OR?: Prisma.DepartmentStaffScalarWhereInput[]
  NOT?: Prisma.DepartmentStaffScalarWhereInput | Prisma.DepartmentStaffScalarWhereInput[]
  id?: Prisma.StringFilter<'DepartmentStaff'> | string
  role?: Prisma.StringFilter<'DepartmentStaff'> | string
  createdAt?: Prisma.DateTimeFilter<'DepartmentStaff'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'DepartmentStaff'> | Date | string
  departmentId?: Prisma.StringFilter<'DepartmentStaff'> | string
  userId?: Prisma.StringFilter<'DepartmentStaff'> | string
}

export type DepartmentStaffCreateWithoutUserInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  department: Prisma.DepartmentCreateNestedOneWithoutStaffsInput
}

export type DepartmentStaffUncheckedCreateWithoutUserInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  departmentId: string
}

export type DepartmentStaffCreateOrConnectWithoutUserInput = {
  where: Prisma.DepartmentStaffWhereUniqueInput
  create: Prisma.XOR<
    Prisma.DepartmentStaffCreateWithoutUserInput,
    Prisma.DepartmentStaffUncheckedCreateWithoutUserInput
  >
}

export type DepartmentStaffCreateManyUserInputEnvelope = {
  data: Prisma.DepartmentStaffCreateManyUserInput | Prisma.DepartmentStaffCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type DepartmentStaffUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.DepartmentStaffWhereUniqueInput
  update: Prisma.XOR<
    Prisma.DepartmentStaffUpdateWithoutUserInput,
    Prisma.DepartmentStaffUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.DepartmentStaffCreateWithoutUserInput,
    Prisma.DepartmentStaffUncheckedCreateWithoutUserInput
  >
}

export type DepartmentStaffUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.DepartmentStaffWhereUniqueInput
  data: Prisma.XOR<
    Prisma.DepartmentStaffUpdateWithoutUserInput,
    Prisma.DepartmentStaffUncheckedUpdateWithoutUserInput
  >
}

export type DepartmentStaffUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.DepartmentStaffScalarWhereInput
  data: Prisma.XOR<
    Prisma.DepartmentStaffUpdateManyMutationInput,
    Prisma.DepartmentStaffUncheckedUpdateManyWithoutUserInput
  >
}

export type DepartmentStaffCreateManyDepartmentInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  userId: string
}

export type DepartmentStaffUpdateWithoutDepartmentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutDepartmentsNestedInput
}

export type DepartmentStaffUncheckedUpdateWithoutDepartmentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentStaffUncheckedUpdateManyWithoutDepartmentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentStaffCreateManyUserInput = {
  id?: string
  role: string
  createdAt?: Date | string
  updatedAt?: Date | string
  departmentId: string
}

export type DepartmentStaffUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  department?: Prisma.DepartmentUpdateOneRequiredWithoutStaffsNestedInput
}

export type DepartmentStaffUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  departmentId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentStaffUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  departmentId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type DepartmentStaffSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    role?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    departmentId?: boolean
    userId?: boolean
    department?: boolean | Prisma.DepartmentDefaultArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['departmentStaff']
>

export type DepartmentStaffSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    role?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    departmentId?: boolean
    userId?: boolean
    department?: boolean | Prisma.DepartmentDefaultArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['departmentStaff']
>

export type DepartmentStaffSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    role?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    departmentId?: boolean
    userId?: boolean
    department?: boolean | Prisma.DepartmentDefaultArgs<ExtArgs>
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['departmentStaff']
>

export type DepartmentStaffSelectScalar = {
  id?: boolean
  role?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  departmentId?: boolean
  userId?: boolean
}

export type DepartmentStaffOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'role' | 'createdAt' | 'updatedAt' | 'departmentId' | 'userId',
  ExtArgs['result']['departmentStaff']
>
export type DepartmentStaffInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  department?: boolean | Prisma.DepartmentDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type DepartmentStaffIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  department?: boolean | Prisma.DepartmentDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type DepartmentStaffIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  department?: boolean | Prisma.DepartmentDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $DepartmentStaffPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'DepartmentStaff'
  objects: {
    department: Prisma.$DepartmentPayload<ExtArgs>
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      role: string
      createdAt: Date
      updatedAt: Date
      departmentId: string
      userId: string
    },
    ExtArgs['result']['departmentStaff']
  >
  composites: {}
}

export type DepartmentStaffGetPayload<
  S extends boolean | null | undefined | DepartmentStaffDefaultArgs,
> = runtime.Types.Result.GetResult<Prisma.$DepartmentStaffPayload, S>

export type DepartmentStaffCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<DepartmentStaffFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: DepartmentStaffCountAggregateInputType | true
}

export interface DepartmentStaffDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['DepartmentStaff']
    meta: { name: 'DepartmentStaff' }
  }
  /**
   * Find zero or one DepartmentStaff that matches the filter.
   * @param {DepartmentStaffFindUniqueArgs} args - Arguments to find a DepartmentStaff
   * @example
   * // Get one DepartmentStaff
   * const departmentStaff = await prisma.departmentStaff.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends DepartmentStaffFindUniqueArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one DepartmentStaff that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {DepartmentStaffFindUniqueOrThrowArgs} args - Arguments to find a DepartmentStaff
   * @example
   * // Get one DepartmentStaff
   * const departmentStaff = await prisma.departmentStaff.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends DepartmentStaffFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first DepartmentStaff that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentStaffFindFirstArgs} args - Arguments to find a DepartmentStaff
   * @example
   * // Get one DepartmentStaff
   * const departmentStaff = await prisma.departmentStaff.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends DepartmentStaffFindFirstArgs>(
    args?: Prisma.SelectSubset<T, DepartmentStaffFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first DepartmentStaff that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentStaffFindFirstOrThrowArgs} args - Arguments to find a DepartmentStaff
   * @example
   * // Get one DepartmentStaff
   * const departmentStaff = await prisma.departmentStaff.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends DepartmentStaffFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, DepartmentStaffFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more DepartmentStaffs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentStaffFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all DepartmentStaffs
   * const departmentStaffs = await prisma.departmentStaff.findMany()
   *
   * // Get first 10 DepartmentStaffs
   * const departmentStaffs = await prisma.departmentStaff.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const departmentStaffWithIdOnly = await prisma.departmentStaff.findMany({ select: { id: true } })
   *
   */
  findMany<T extends DepartmentStaffFindManyArgs>(
    args?: Prisma.SelectSubset<T, DepartmentStaffFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a DepartmentStaff.
   * @param {DepartmentStaffCreateArgs} args - Arguments to create a DepartmentStaff.
   * @example
   * // Create one DepartmentStaff
   * const DepartmentStaff = await prisma.departmentStaff.create({
   *   data: {
   *     // ... data to create a DepartmentStaff
   *   }
   * })
   *
   */
  create<T extends DepartmentStaffCreateArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffCreateArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many DepartmentStaffs.
   * @param {DepartmentStaffCreateManyArgs} args - Arguments to create many DepartmentStaffs.
   * @example
   * // Create many DepartmentStaffs
   * const departmentStaff = await prisma.departmentStaff.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends DepartmentStaffCreateManyArgs>(
    args?: Prisma.SelectSubset<T, DepartmentStaffCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many DepartmentStaffs and returns the data saved in the database.
   * @param {DepartmentStaffCreateManyAndReturnArgs} args - Arguments to create many DepartmentStaffs.
   * @example
   * // Create many DepartmentStaffs
   * const departmentStaff = await prisma.departmentStaff.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many DepartmentStaffs and only return the `id`
   * const departmentStaffWithIdOnly = await prisma.departmentStaff.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends DepartmentStaffCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, DepartmentStaffCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a DepartmentStaff.
   * @param {DepartmentStaffDeleteArgs} args - Arguments to delete one DepartmentStaff.
   * @example
   * // Delete one DepartmentStaff
   * const DepartmentStaff = await prisma.departmentStaff.delete({
   *   where: {
   *     // ... filter to delete one DepartmentStaff
   *   }
   * })
   *
   */
  delete<T extends DepartmentStaffDeleteArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one DepartmentStaff.
   * @param {DepartmentStaffUpdateArgs} args - Arguments to update one DepartmentStaff.
   * @example
   * // Update one DepartmentStaff
   * const departmentStaff = await prisma.departmentStaff.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends DepartmentStaffUpdateArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more DepartmentStaffs.
   * @param {DepartmentStaffDeleteManyArgs} args - Arguments to filter DepartmentStaffs to delete.
   * @example
   * // Delete a few DepartmentStaffs
   * const { count } = await prisma.departmentStaff.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends DepartmentStaffDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, DepartmentStaffDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more DepartmentStaffs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentStaffUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many DepartmentStaffs
   * const departmentStaff = await prisma.departmentStaff.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends DepartmentStaffUpdateManyArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more DepartmentStaffs and returns the data updated in the database.
   * @param {DepartmentStaffUpdateManyAndReturnArgs} args - Arguments to update many DepartmentStaffs.
   * @example
   * // Update many DepartmentStaffs
   * const departmentStaff = await prisma.departmentStaff.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more DepartmentStaffs and only return the `id`
   * const departmentStaffWithIdOnly = await prisma.departmentStaff.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends DepartmentStaffUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one DepartmentStaff.
   * @param {DepartmentStaffUpsertArgs} args - Arguments to update or create a DepartmentStaff.
   * @example
   * // Update or create a DepartmentStaff
   * const departmentStaff = await prisma.departmentStaff.upsert({
   *   create: {
   *     // ... data to create a DepartmentStaff
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the DepartmentStaff we want to update
   *   }
   * })
   */
  upsert<T extends DepartmentStaffUpsertArgs>(
    args: Prisma.SelectSubset<T, DepartmentStaffUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentStaffClient<
    runtime.Types.Result.GetResult<
      Prisma.$DepartmentStaffPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of DepartmentStaffs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentStaffCountArgs} args - Arguments to filter DepartmentStaffs to count.
   * @example
   * // Count the number of DepartmentStaffs
   * const count = await prisma.departmentStaff.count({
   *   where: {
   *     // ... the filter for the DepartmentStaffs we want to count
   *   }
   * })
   **/
  count<T extends DepartmentStaffCountArgs>(
    args?: Prisma.Subset<T, DepartmentStaffCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], DepartmentStaffCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a DepartmentStaff.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentStaffAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends DepartmentStaffAggregateArgs>(
    args: Prisma.Subset<T, DepartmentStaffAggregateArgs>
  ): Prisma.PrismaPromise<GetDepartmentStaffAggregateType<T>>

  /**
   * Group by DepartmentStaff.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DepartmentStaffGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends DepartmentStaffGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: DepartmentStaffGroupByArgs['orderBy'] }
      : { orderBy?: DepartmentStaffGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, DepartmentStaffGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors
    ? GetDepartmentStaffGroupByPayload<T>
    : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the DepartmentStaff model
   */
  readonly fields: DepartmentStaffFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for DepartmentStaff.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__DepartmentStaffClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  department<T extends Prisma.DepartmentDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.DepartmentDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__DepartmentClient<
    | runtime.Types.Result.GetResult<
        Prisma.$DepartmentPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the DepartmentStaff model
 */
export interface DepartmentStaffFieldRefs {
  readonly id: Prisma.FieldRef<'DepartmentStaff', 'String'>
  readonly role: Prisma.FieldRef<'DepartmentStaff', 'String'>
  readonly createdAt: Prisma.FieldRef<'DepartmentStaff', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'DepartmentStaff', 'DateTime'>
  readonly departmentId: Prisma.FieldRef<'DepartmentStaff', 'String'>
  readonly userId: Prisma.FieldRef<'DepartmentStaff', 'String'>
}

// Custom InputTypes
/**
 * DepartmentStaff findUnique
 */
export type DepartmentStaffFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * Filter, which DepartmentStaff to fetch.
   */
  where: Prisma.DepartmentStaffWhereUniqueInput
}

/**
 * DepartmentStaff findUniqueOrThrow
 */
export type DepartmentStaffFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * Filter, which DepartmentStaff to fetch.
   */
  where: Prisma.DepartmentStaffWhereUniqueInput
}

/**
 * DepartmentStaff findFirst
 */
export type DepartmentStaffFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * Filter, which DepartmentStaff to fetch.
   */
  where?: Prisma.DepartmentStaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of DepartmentStaffs to fetch.
   */
  orderBy?:
    | Prisma.DepartmentStaffOrderByWithRelationInput
    | Prisma.DepartmentStaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for DepartmentStaffs.
   */
  cursor?: Prisma.DepartmentStaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` DepartmentStaffs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` DepartmentStaffs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of DepartmentStaffs.
   */
  distinct?: Prisma.DepartmentStaffScalarFieldEnum | Prisma.DepartmentStaffScalarFieldEnum[]
}

/**
 * DepartmentStaff findFirstOrThrow
 */
export type DepartmentStaffFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * Filter, which DepartmentStaff to fetch.
   */
  where?: Prisma.DepartmentStaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of DepartmentStaffs to fetch.
   */
  orderBy?:
    | Prisma.DepartmentStaffOrderByWithRelationInput
    | Prisma.DepartmentStaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for DepartmentStaffs.
   */
  cursor?: Prisma.DepartmentStaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` DepartmentStaffs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` DepartmentStaffs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of DepartmentStaffs.
   */
  distinct?: Prisma.DepartmentStaffScalarFieldEnum | Prisma.DepartmentStaffScalarFieldEnum[]
}

/**
 * DepartmentStaff findMany
 */
export type DepartmentStaffFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * Filter, which DepartmentStaffs to fetch.
   */
  where?: Prisma.DepartmentStaffWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of DepartmentStaffs to fetch.
   */
  orderBy?:
    | Prisma.DepartmentStaffOrderByWithRelationInput
    | Prisma.DepartmentStaffOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing DepartmentStaffs.
   */
  cursor?: Prisma.DepartmentStaffWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` DepartmentStaffs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` DepartmentStaffs.
   */
  skip?: number
  distinct?: Prisma.DepartmentStaffScalarFieldEnum | Prisma.DepartmentStaffScalarFieldEnum[]
}

/**
 * DepartmentStaff create
 */
export type DepartmentStaffCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * The data needed to create a DepartmentStaff.
   */
  data: Prisma.XOR<Prisma.DepartmentStaffCreateInput, Prisma.DepartmentStaffUncheckedCreateInput>
}

/**
 * DepartmentStaff createMany
 */
export type DepartmentStaffCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many DepartmentStaffs.
   */
  data: Prisma.DepartmentStaffCreateManyInput | Prisma.DepartmentStaffCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * DepartmentStaff createManyAndReturn
 */
export type DepartmentStaffCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * The data used to create many DepartmentStaffs.
   */
  data: Prisma.DepartmentStaffCreateManyInput | Prisma.DepartmentStaffCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * DepartmentStaff update
 */
export type DepartmentStaffUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * The data needed to update a DepartmentStaff.
   */
  data: Prisma.XOR<Prisma.DepartmentStaffUpdateInput, Prisma.DepartmentStaffUncheckedUpdateInput>
  /**
   * Choose, which DepartmentStaff to update.
   */
  where: Prisma.DepartmentStaffWhereUniqueInput
}

/**
 * DepartmentStaff updateMany
 */
export type DepartmentStaffUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update DepartmentStaffs.
   */
  data: Prisma.XOR<
    Prisma.DepartmentStaffUpdateManyMutationInput,
    Prisma.DepartmentStaffUncheckedUpdateManyInput
  >
  /**
   * Filter which DepartmentStaffs to update
   */
  where?: Prisma.DepartmentStaffWhereInput
  /**
   * Limit how many DepartmentStaffs to update.
   */
  limit?: number
}

/**
 * DepartmentStaff updateManyAndReturn
 */
export type DepartmentStaffUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * The data used to update DepartmentStaffs.
   */
  data: Prisma.XOR<
    Prisma.DepartmentStaffUpdateManyMutationInput,
    Prisma.DepartmentStaffUncheckedUpdateManyInput
  >
  /**
   * Filter which DepartmentStaffs to update
   */
  where?: Prisma.DepartmentStaffWhereInput
  /**
   * Limit how many DepartmentStaffs to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * DepartmentStaff upsert
 */
export type DepartmentStaffUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * The filter to search for the DepartmentStaff to update in case it exists.
   */
  where: Prisma.DepartmentStaffWhereUniqueInput
  /**
   * In case the DepartmentStaff found by the `where` argument doesn't exist, create a new DepartmentStaff with this data.
   */
  create: Prisma.XOR<Prisma.DepartmentStaffCreateInput, Prisma.DepartmentStaffUncheckedCreateInput>
  /**
   * In case the DepartmentStaff was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.DepartmentStaffUpdateInput, Prisma.DepartmentStaffUncheckedUpdateInput>
}

/**
 * DepartmentStaff delete
 */
export type DepartmentStaffDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
  /**
   * Filter which DepartmentStaff to delete.
   */
  where: Prisma.DepartmentStaffWhereUniqueInput
}

/**
 * DepartmentStaff deleteMany
 */
export type DepartmentStaffDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which DepartmentStaffs to delete
   */
  where?: Prisma.DepartmentStaffWhereInput
  /**
   * Limit how many DepartmentStaffs to delete.
   */
  limit?: number
}

/**
 * DepartmentStaff without action
 */
export type DepartmentStaffDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the DepartmentStaff
   */
  select?: Prisma.DepartmentStaffSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DepartmentStaff
   */
  omit?: Prisma.DepartmentStaffOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DepartmentStaffInclude<ExtArgs> | null
}
