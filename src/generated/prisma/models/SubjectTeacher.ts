/* !!! This is code generated by <PERSON>risma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `SubjectTeacher` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model SubjectTeacher
 *
 */
export type SubjectTeacherModel =
  runtime.Types.Result.DefaultSelection<Prisma.$SubjectTeacherPayload>

export type AggregateSubjectTeacher = {
  _count: SubjectTeacherCountAggregateOutputType | null
  _min: SubjectTeacherMinAggregateOutputType | null
  _max: SubjectTeacherMaxAggregateOutputType | null
}

export type SubjectTeacherMinAggregateOutputType = {
  id: string | null
  createdAt: Date | null
  updatedAt: Date | null
  subjectId: string | null
  teacherId: string | null
}

export type SubjectTeacherMaxAggregateOutputType = {
  id: string | null
  createdAt: Date | null
  updatedAt: Date | null
  subjectId: string | null
  teacherId: string | null
}

export type SubjectTeacherCountAggregateOutputType = {
  id: number
  createdAt: number
  updatedAt: number
  subjectId: number
  teacherId: number
  _all: number
}

export type SubjectTeacherMinAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  subjectId?: true
  teacherId?: true
}

export type SubjectTeacherMaxAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  subjectId?: true
  teacherId?: true
}

export type SubjectTeacherCountAggregateInputType = {
  id?: true
  createdAt?: true
  updatedAt?: true
  subjectId?: true
  teacherId?: true
  _all?: true
}

export type SubjectTeacherAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SubjectTeacher to aggregate.
   */
  where?: Prisma.SubjectTeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectTeachers to fetch.
   */
  orderBy?:
    | Prisma.SubjectTeacherOrderByWithRelationInput
    | Prisma.SubjectTeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.SubjectTeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectTeachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectTeachers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned SubjectTeachers
   **/
  _count?: true | SubjectTeacherCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: SubjectTeacherMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: SubjectTeacherMaxAggregateInputType
}

export type GetSubjectTeacherAggregateType<T extends SubjectTeacherAggregateArgs> = {
  [P in keyof T & keyof AggregateSubjectTeacher]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSubjectTeacher[P]>
    : Prisma.GetScalarType<T[P], AggregateSubjectTeacher[P]>
}

export type SubjectTeacherGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SubjectTeacherWhereInput
  orderBy?:
    | Prisma.SubjectTeacherOrderByWithAggregationInput
    | Prisma.SubjectTeacherOrderByWithAggregationInput[]
  by: Prisma.SubjectTeacherScalarFieldEnum[] | Prisma.SubjectTeacherScalarFieldEnum
  having?: Prisma.SubjectTeacherScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SubjectTeacherCountAggregateInputType | true
  _min?: SubjectTeacherMinAggregateInputType
  _max?: SubjectTeacherMaxAggregateInputType
}

export type SubjectTeacherGroupByOutputType = {
  id: string
  createdAt: Date
  updatedAt: Date
  subjectId: string
  teacherId: string
  _count: SubjectTeacherCountAggregateOutputType | null
  _min: SubjectTeacherMinAggregateOutputType | null
  _max: SubjectTeacherMaxAggregateOutputType | null
}

type GetSubjectTeacherGroupByPayload<T extends SubjectTeacherGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SubjectTeacherGroupByOutputType, T['by']> & {
      [P in keyof T & keyof SubjectTeacherGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], SubjectTeacherGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], SubjectTeacherGroupByOutputType[P]>
    }
  >
>

export type SubjectTeacherWhereInput = {
  AND?: Prisma.SubjectTeacherWhereInput | Prisma.SubjectTeacherWhereInput[]
  OR?: Prisma.SubjectTeacherWhereInput[]
  NOT?: Prisma.SubjectTeacherWhereInput | Prisma.SubjectTeacherWhereInput[]
  id?: Prisma.StringFilter<'SubjectTeacher'> | string
  createdAt?: Prisma.DateTimeFilter<'SubjectTeacher'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'SubjectTeacher'> | Date | string
  subjectId?: Prisma.StringFilter<'SubjectTeacher'> | string
  teacherId?: Prisma.StringFilter<'SubjectTeacher'> | string
  subject?: Prisma.XOR<Prisma.SubjectScalarRelationFilter, Prisma.SubjectWhereInput>
  teacher?: Prisma.XOR<Prisma.TeacherScalarRelationFilter, Prisma.TeacherWhereInput>
}

export type SubjectTeacherOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  teacherId?: Prisma.SortOrder
  subject?: Prisma.SubjectOrderByWithRelationInput
  teacher?: Prisma.TeacherOrderByWithRelationInput
}

export type SubjectTeacherWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    subjectId_teacherId?: Prisma.SubjectTeacherSubjectIdTeacherIdCompoundUniqueInput
    AND?: Prisma.SubjectTeacherWhereInput | Prisma.SubjectTeacherWhereInput[]
    OR?: Prisma.SubjectTeacherWhereInput[]
    NOT?: Prisma.SubjectTeacherWhereInput | Prisma.SubjectTeacherWhereInput[]
    createdAt?: Prisma.DateTimeFilter<'SubjectTeacher'> | Date | string
    updatedAt?: Prisma.DateTimeFilter<'SubjectTeacher'> | Date | string
    subjectId?: Prisma.StringFilter<'SubjectTeacher'> | string
    teacherId?: Prisma.StringFilter<'SubjectTeacher'> | string
    subject?: Prisma.XOR<Prisma.SubjectScalarRelationFilter, Prisma.SubjectWhereInput>
    teacher?: Prisma.XOR<Prisma.TeacherScalarRelationFilter, Prisma.TeacherWhereInput>
  },
  'id' | 'subjectId_teacherId'
>

export type SubjectTeacherOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  teacherId?: Prisma.SortOrder
  _count?: Prisma.SubjectTeacherCountOrderByAggregateInput
  _max?: Prisma.SubjectTeacherMaxOrderByAggregateInput
  _min?: Prisma.SubjectTeacherMinOrderByAggregateInput
}

export type SubjectTeacherScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.SubjectTeacherScalarWhereWithAggregatesInput
    | Prisma.SubjectTeacherScalarWhereWithAggregatesInput[]
  OR?: Prisma.SubjectTeacherScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.SubjectTeacherScalarWhereWithAggregatesInput
    | Prisma.SubjectTeacherScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'SubjectTeacher'> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<'SubjectTeacher'> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<'SubjectTeacher'> | Date | string
  subjectId?: Prisma.StringWithAggregatesFilter<'SubjectTeacher'> | string
  teacherId?: Prisma.StringWithAggregatesFilter<'SubjectTeacher'> | string
}

export type SubjectTeacherCreateInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subject: Prisma.SubjectCreateNestedOneWithoutTeachersInput
  teacher: Prisma.TeacherCreateNestedOneWithoutSubjectsInput
}

export type SubjectTeacherUncheckedCreateInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
  teacherId: string
}

export type SubjectTeacherUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subject?: Prisma.SubjectUpdateOneRequiredWithoutTeachersNestedInput
  teacher?: Prisma.TeacherUpdateOneRequiredWithoutSubjectsNestedInput
}

export type SubjectTeacherUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
  teacherId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectTeacherCreateManyInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
  teacherId: string
}

export type SubjectTeacherUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SubjectTeacherUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
  teacherId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectTeacherListRelationFilter = {
  every?: Prisma.SubjectTeacherWhereInput
  some?: Prisma.SubjectTeacherWhereInput
  none?: Prisma.SubjectTeacherWhereInput
}

export type SubjectTeacherOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SubjectTeacherSubjectIdTeacherIdCompoundUniqueInput = {
  subjectId: string
  teacherId: string
}

export type SubjectTeacherCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  teacherId?: Prisma.SortOrder
}

export type SubjectTeacherMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  teacherId?: Prisma.SortOrder
}

export type SubjectTeacherMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  subjectId?: Prisma.SortOrder
  teacherId?: Prisma.SortOrder
}

export type SubjectTeacherCreateNestedManyWithoutSubjectInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutSubjectInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectTeacherCreateWithoutSubjectInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput[]
  createMany?: Prisma.SubjectTeacherCreateManySubjectInputEnvelope
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
}

export type SubjectTeacherUncheckedCreateNestedManyWithoutSubjectInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutSubjectInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectTeacherCreateWithoutSubjectInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput[]
  createMany?: Prisma.SubjectTeacherCreateManySubjectInputEnvelope
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
}

export type SubjectTeacherUpdateManyWithoutSubjectNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutSubjectInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectTeacherCreateWithoutSubjectInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput[]
  upsert?:
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutSubjectInput[]
  createMany?: Prisma.SubjectTeacherCreateManySubjectInputEnvelope
  set?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  disconnect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  delete?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  update?:
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutSubjectInput[]
  updateMany?:
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutSubjectInput
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutSubjectInput[]
  deleteMany?: Prisma.SubjectTeacherScalarWhereInput | Prisma.SubjectTeacherScalarWhereInput[]
}

export type SubjectTeacherUncheckedUpdateManyWithoutSubjectNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutSubjectInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput
      >
    | Prisma.SubjectTeacherCreateWithoutSubjectInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutSubjectInput[]
  upsert?:
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutSubjectInput[]
  createMany?: Prisma.SubjectTeacherCreateManySubjectInputEnvelope
  set?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  disconnect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  delete?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  update?:
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutSubjectInput
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutSubjectInput[]
  updateMany?:
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutSubjectInput
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutSubjectInput[]
  deleteMany?: Prisma.SubjectTeacherScalarWhereInput | Prisma.SubjectTeacherScalarWhereInput[]
}

export type SubjectTeacherCreateNestedManyWithoutTeacherInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutTeacherInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput
      >
    | Prisma.SubjectTeacherCreateWithoutTeacherInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput[]
  createMany?: Prisma.SubjectTeacherCreateManyTeacherInputEnvelope
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
}

export type SubjectTeacherUncheckedCreateNestedManyWithoutTeacherInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutTeacherInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput
      >
    | Prisma.SubjectTeacherCreateWithoutTeacherInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput[]
  createMany?: Prisma.SubjectTeacherCreateManyTeacherInputEnvelope
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
}

export type SubjectTeacherUpdateManyWithoutTeacherNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutTeacherInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput
      >
    | Prisma.SubjectTeacherCreateWithoutTeacherInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput[]
  upsert?:
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutTeacherInput
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutTeacherInput[]
  createMany?: Prisma.SubjectTeacherCreateManyTeacherInputEnvelope
  set?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  disconnect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  delete?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  update?:
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutTeacherInput
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutTeacherInput[]
  updateMany?:
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutTeacherInput
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutTeacherInput[]
  deleteMany?: Prisma.SubjectTeacherScalarWhereInput | Prisma.SubjectTeacherScalarWhereInput[]
}

export type SubjectTeacherUncheckedUpdateManyWithoutTeacherNestedInput = {
  create?:
    | Prisma.XOR<
        Prisma.SubjectTeacherCreateWithoutTeacherInput,
        Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput
      >
    | Prisma.SubjectTeacherCreateWithoutTeacherInput[]
    | Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput[]
  connectOrCreate?:
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput
    | Prisma.SubjectTeacherCreateOrConnectWithoutTeacherInput[]
  upsert?:
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutTeacherInput
    | Prisma.SubjectTeacherUpsertWithWhereUniqueWithoutTeacherInput[]
  createMany?: Prisma.SubjectTeacherCreateManyTeacherInputEnvelope
  set?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  disconnect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  delete?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  connect?: Prisma.SubjectTeacherWhereUniqueInput | Prisma.SubjectTeacherWhereUniqueInput[]
  update?:
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutTeacherInput
    | Prisma.SubjectTeacherUpdateWithWhereUniqueWithoutTeacherInput[]
  updateMany?:
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutTeacherInput
    | Prisma.SubjectTeacherUpdateManyWithWhereWithoutTeacherInput[]
  deleteMany?: Prisma.SubjectTeacherScalarWhereInput | Prisma.SubjectTeacherScalarWhereInput[]
}

export type SubjectTeacherCreateWithoutSubjectInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  teacher: Prisma.TeacherCreateNestedOneWithoutSubjectsInput
}

export type SubjectTeacherUncheckedCreateWithoutSubjectInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  teacherId: string
}

export type SubjectTeacherCreateOrConnectWithoutSubjectInput = {
  where: Prisma.SubjectTeacherWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SubjectTeacherCreateWithoutSubjectInput,
    Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput
  >
}

export type SubjectTeacherCreateManySubjectInputEnvelope = {
  data: Prisma.SubjectTeacherCreateManySubjectInput | Prisma.SubjectTeacherCreateManySubjectInput[]
  skipDuplicates?: boolean
}

export type SubjectTeacherUpsertWithWhereUniqueWithoutSubjectInput = {
  where: Prisma.SubjectTeacherWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SubjectTeacherUpdateWithoutSubjectInput,
    Prisma.SubjectTeacherUncheckedUpdateWithoutSubjectInput
  >
  create: Prisma.XOR<
    Prisma.SubjectTeacherCreateWithoutSubjectInput,
    Prisma.SubjectTeacherUncheckedCreateWithoutSubjectInput
  >
}

export type SubjectTeacherUpdateWithWhereUniqueWithoutSubjectInput = {
  where: Prisma.SubjectTeacherWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SubjectTeacherUpdateWithoutSubjectInput,
    Prisma.SubjectTeacherUncheckedUpdateWithoutSubjectInput
  >
}

export type SubjectTeacherUpdateManyWithWhereWithoutSubjectInput = {
  where: Prisma.SubjectTeacherScalarWhereInput
  data: Prisma.XOR<
    Prisma.SubjectTeacherUpdateManyMutationInput,
    Prisma.SubjectTeacherUncheckedUpdateManyWithoutSubjectInput
  >
}

export type SubjectTeacherScalarWhereInput = {
  AND?: Prisma.SubjectTeacherScalarWhereInput | Prisma.SubjectTeacherScalarWhereInput[]
  OR?: Prisma.SubjectTeacherScalarWhereInput[]
  NOT?: Prisma.SubjectTeacherScalarWhereInput | Prisma.SubjectTeacherScalarWhereInput[]
  id?: Prisma.StringFilter<'SubjectTeacher'> | string
  createdAt?: Prisma.DateTimeFilter<'SubjectTeacher'> | Date | string
  updatedAt?: Prisma.DateTimeFilter<'SubjectTeacher'> | Date | string
  subjectId?: Prisma.StringFilter<'SubjectTeacher'> | string
  teacherId?: Prisma.StringFilter<'SubjectTeacher'> | string
}

export type SubjectTeacherCreateWithoutTeacherInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subject: Prisma.SubjectCreateNestedOneWithoutTeachersInput
}

export type SubjectTeacherUncheckedCreateWithoutTeacherInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
}

export type SubjectTeacherCreateOrConnectWithoutTeacherInput = {
  where: Prisma.SubjectTeacherWhereUniqueInput
  create: Prisma.XOR<
    Prisma.SubjectTeacherCreateWithoutTeacherInput,
    Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput
  >
}

export type SubjectTeacherCreateManyTeacherInputEnvelope = {
  data: Prisma.SubjectTeacherCreateManyTeacherInput | Prisma.SubjectTeacherCreateManyTeacherInput[]
  skipDuplicates?: boolean
}

export type SubjectTeacherUpsertWithWhereUniqueWithoutTeacherInput = {
  where: Prisma.SubjectTeacherWhereUniqueInput
  update: Prisma.XOR<
    Prisma.SubjectTeacherUpdateWithoutTeacherInput,
    Prisma.SubjectTeacherUncheckedUpdateWithoutTeacherInput
  >
  create: Prisma.XOR<
    Prisma.SubjectTeacherCreateWithoutTeacherInput,
    Prisma.SubjectTeacherUncheckedCreateWithoutTeacherInput
  >
}

export type SubjectTeacherUpdateWithWhereUniqueWithoutTeacherInput = {
  where: Prisma.SubjectTeacherWhereUniqueInput
  data: Prisma.XOR<
    Prisma.SubjectTeacherUpdateWithoutTeacherInput,
    Prisma.SubjectTeacherUncheckedUpdateWithoutTeacherInput
  >
}

export type SubjectTeacherUpdateManyWithWhereWithoutTeacherInput = {
  where: Prisma.SubjectTeacherScalarWhereInput
  data: Prisma.XOR<
    Prisma.SubjectTeacherUpdateManyMutationInput,
    Prisma.SubjectTeacherUncheckedUpdateManyWithoutTeacherInput
  >
}

export type SubjectTeacherCreateManySubjectInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  teacherId: string
}

export type SubjectTeacherUpdateWithoutSubjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  teacher?: Prisma.TeacherUpdateOneRequiredWithoutSubjectsNestedInput
}

export type SubjectTeacherUncheckedUpdateWithoutSubjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  teacherId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectTeacherUncheckedUpdateManyWithoutSubjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  teacherId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectTeacherCreateManyTeacherInput = {
  id?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  subjectId: string
}

export type SubjectTeacherUpdateWithoutTeacherInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subject?: Prisma.SubjectUpdateOneRequiredWithoutTeachersNestedInput
}

export type SubjectTeacherUncheckedUpdateWithoutTeacherInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectTeacherUncheckedUpdateManyWithoutTeacherInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  subjectId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SubjectTeacherSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    subjectId?: boolean
    teacherId?: boolean
    subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
    teacher?: boolean | Prisma.TeacherDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subjectTeacher']
>

export type SubjectTeacherSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    subjectId?: boolean
    teacherId?: boolean
    subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
    teacher?: boolean | Prisma.TeacherDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subjectTeacher']
>

export type SubjectTeacherSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    subjectId?: boolean
    teacherId?: boolean
    subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
    teacher?: boolean | Prisma.TeacherDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['subjectTeacher']
>

export type SubjectTeacherSelectScalar = {
  id?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  subjectId?: boolean
  teacherId?: boolean
}

export type SubjectTeacherOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'createdAt' | 'updatedAt' | 'subjectId' | 'teacherId',
  ExtArgs['result']['subjectTeacher']
>
export type SubjectTeacherInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
  teacher?: boolean | Prisma.TeacherDefaultArgs<ExtArgs>
}
export type SubjectTeacherIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
  teacher?: boolean | Prisma.TeacherDefaultArgs<ExtArgs>
}
export type SubjectTeacherIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  subject?: boolean | Prisma.SubjectDefaultArgs<ExtArgs>
  teacher?: boolean | Prisma.TeacherDefaultArgs<ExtArgs>
}

export type $SubjectTeacherPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'SubjectTeacher'
  objects: {
    subject: Prisma.$SubjectPayload<ExtArgs>
    teacher: Prisma.$TeacherPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      createdAt: Date
      updatedAt: Date
      subjectId: string
      teacherId: string
    },
    ExtArgs['result']['subjectTeacher']
  >
  composites: {}
}

export type SubjectTeacherGetPayload<
  S extends boolean | null | undefined | SubjectTeacherDefaultArgs,
> = runtime.Types.Result.GetResult<Prisma.$SubjectTeacherPayload, S>

export type SubjectTeacherCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<SubjectTeacherFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: SubjectTeacherCountAggregateInputType | true
}

export interface SubjectTeacherDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: {
    types: Prisma.TypeMap<ExtArgs>['model']['SubjectTeacher']
    meta: { name: 'SubjectTeacher' }
  }
  /**
   * Find zero or one SubjectTeacher that matches the filter.
   * @param {SubjectTeacherFindUniqueArgs} args - Arguments to find a SubjectTeacher
   * @example
   * // Get one SubjectTeacher
   * const subjectTeacher = await prisma.subjectTeacher.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SubjectTeacherFindUniqueArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one SubjectTeacher that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SubjectTeacherFindUniqueOrThrowArgs} args - Arguments to find a SubjectTeacher
   * @example
   * // Get one SubjectTeacher
   * const subjectTeacher = await prisma.subjectTeacher.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SubjectTeacherFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SubjectTeacher that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectTeacherFindFirstArgs} args - Arguments to find a SubjectTeacher
   * @example
   * // Get one SubjectTeacher
   * const subjectTeacher = await prisma.subjectTeacher.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SubjectTeacherFindFirstArgs>(
    args?: Prisma.SelectSubset<T, SubjectTeacherFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first SubjectTeacher that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectTeacherFindFirstOrThrowArgs} args - Arguments to find a SubjectTeacher
   * @example
   * // Get one SubjectTeacher
   * const subjectTeacher = await prisma.subjectTeacher.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SubjectTeacherFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, SubjectTeacherFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more SubjectTeachers that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectTeacherFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all SubjectTeachers
   * const subjectTeachers = await prisma.subjectTeacher.findMany()
   *
   * // Get first 10 SubjectTeachers
   * const subjectTeachers = await prisma.subjectTeacher.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const subjectTeacherWithIdOnly = await prisma.subjectTeacher.findMany({ select: { id: true } })
   *
   */
  findMany<T extends SubjectTeacherFindManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectTeacherFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a SubjectTeacher.
   * @param {SubjectTeacherCreateArgs} args - Arguments to create a SubjectTeacher.
   * @example
   * // Create one SubjectTeacher
   * const SubjectTeacher = await prisma.subjectTeacher.create({
   *   data: {
   *     // ... data to create a SubjectTeacher
   *   }
   * })
   *
   */
  create<T extends SubjectTeacherCreateArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherCreateArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'create',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many SubjectTeachers.
   * @param {SubjectTeacherCreateManyArgs} args - Arguments to create many SubjectTeachers.
   * @example
   * // Create many SubjectTeachers
   * const subjectTeacher = await prisma.subjectTeacher.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends SubjectTeacherCreateManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectTeacherCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many SubjectTeachers and returns the data saved in the database.
   * @param {SubjectTeacherCreateManyAndReturnArgs} args - Arguments to create many SubjectTeachers.
   * @example
   * // Create many SubjectTeachers
   * const subjectTeacher = await prisma.subjectTeacher.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many SubjectTeachers and only return the `id`
   * const subjectTeacherWithIdOnly = await prisma.subjectTeacher.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends SubjectTeacherCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, SubjectTeacherCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a SubjectTeacher.
   * @param {SubjectTeacherDeleteArgs} args - Arguments to delete one SubjectTeacher.
   * @example
   * // Delete one SubjectTeacher
   * const SubjectTeacher = await prisma.subjectTeacher.delete({
   *   where: {
   *     // ... filter to delete one SubjectTeacher
   *   }
   * })
   *
   */
  delete<T extends SubjectTeacherDeleteArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'delete',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one SubjectTeacher.
   * @param {SubjectTeacherUpdateArgs} args - Arguments to update one SubjectTeacher.
   * @example
   * // Update one SubjectTeacher
   * const subjectTeacher = await prisma.subjectTeacher.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends SubjectTeacherUpdateArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'update',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more SubjectTeachers.
   * @param {SubjectTeacherDeleteManyArgs} args - Arguments to filter SubjectTeachers to delete.
   * @example
   * // Delete a few SubjectTeachers
   * const { count } = await prisma.subjectTeacher.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends SubjectTeacherDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, SubjectTeacherDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SubjectTeachers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectTeacherUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many SubjectTeachers
   * const subjectTeacher = await prisma.subjectTeacher.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends SubjectTeacherUpdateManyArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SubjectTeachers and returns the data updated in the database.
   * @param {SubjectTeacherUpdateManyAndReturnArgs} args - Arguments to update many SubjectTeachers.
   * @example
   * // Update many SubjectTeachers
   * const subjectTeacher = await prisma.subjectTeacher.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more SubjectTeachers and only return the `id`
   * const subjectTeacherWithIdOnly = await prisma.subjectTeacher.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends SubjectTeacherUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one SubjectTeacher.
   * @param {SubjectTeacherUpsertArgs} args - Arguments to update or create a SubjectTeacher.
   * @example
   * // Update or create a SubjectTeacher
   * const subjectTeacher = await prisma.subjectTeacher.upsert({
   *   create: {
   *     // ... data to create a SubjectTeacher
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the SubjectTeacher we want to update
   *   }
   * })
   */
  upsert<T extends SubjectTeacherUpsertArgs>(
    args: Prisma.SelectSubset<T, SubjectTeacherUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectTeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$SubjectTeacherPayload<ExtArgs>,
      T,
      'upsert',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of SubjectTeachers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectTeacherCountArgs} args - Arguments to filter SubjectTeachers to count.
   * @example
   * // Count the number of SubjectTeachers
   * const count = await prisma.subjectTeacher.count({
   *   where: {
   *     // ... the filter for the SubjectTeachers we want to count
   *   }
   * })
   **/
  count<T extends SubjectTeacherCountArgs>(
    args?: Prisma.Subset<T, SubjectTeacherCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SubjectTeacherCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a SubjectTeacher.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectTeacherAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends SubjectTeacherAggregateArgs>(
    args: Prisma.Subset<T, SubjectTeacherAggregateArgs>
  ): Prisma.PrismaPromise<GetSubjectTeacherAggregateType<T>>

  /**
   * Group by SubjectTeacher.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubjectTeacherGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends SubjectTeacherGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SubjectTeacherGroupByArgs['orderBy'] }
      : { orderBy?: SubjectTeacherGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, SubjectTeacherGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetSubjectTeacherGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SubjectTeacher model
   */
  readonly fields: SubjectTeacherFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for SubjectTeacher.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SubjectTeacherClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  subject<T extends Prisma.SubjectDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.SubjectDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__SubjectClient<
    | runtime.Types.Result.GetResult<
        Prisma.$SubjectPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  teacher<T extends Prisma.TeacherDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.TeacherDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    | runtime.Types.Result.GetResult<
        Prisma.$TeacherPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the SubjectTeacher model
 */
export interface SubjectTeacherFieldRefs {
  readonly id: Prisma.FieldRef<'SubjectTeacher', 'String'>
  readonly createdAt: Prisma.FieldRef<'SubjectTeacher', 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<'SubjectTeacher', 'DateTime'>
  readonly subjectId: Prisma.FieldRef<'SubjectTeacher', 'String'>
  readonly teacherId: Prisma.FieldRef<'SubjectTeacher', 'String'>
}

// Custom InputTypes
/**
 * SubjectTeacher findUnique
 */
export type SubjectTeacherFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * Filter, which SubjectTeacher to fetch.
   */
  where: Prisma.SubjectTeacherWhereUniqueInput
}

/**
 * SubjectTeacher findUniqueOrThrow
 */
export type SubjectTeacherFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * Filter, which SubjectTeacher to fetch.
   */
  where: Prisma.SubjectTeacherWhereUniqueInput
}

/**
 * SubjectTeacher findFirst
 */
export type SubjectTeacherFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * Filter, which SubjectTeacher to fetch.
   */
  where?: Prisma.SubjectTeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectTeachers to fetch.
   */
  orderBy?:
    | Prisma.SubjectTeacherOrderByWithRelationInput
    | Prisma.SubjectTeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SubjectTeachers.
   */
  cursor?: Prisma.SubjectTeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectTeachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectTeachers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SubjectTeachers.
   */
  distinct?: Prisma.SubjectTeacherScalarFieldEnum | Prisma.SubjectTeacherScalarFieldEnum[]
}

/**
 * SubjectTeacher findFirstOrThrow
 */
export type SubjectTeacherFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * Filter, which SubjectTeacher to fetch.
   */
  where?: Prisma.SubjectTeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectTeachers to fetch.
   */
  orderBy?:
    | Prisma.SubjectTeacherOrderByWithRelationInput
    | Prisma.SubjectTeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for SubjectTeachers.
   */
  cursor?: Prisma.SubjectTeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectTeachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectTeachers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of SubjectTeachers.
   */
  distinct?: Prisma.SubjectTeacherScalarFieldEnum | Prisma.SubjectTeacherScalarFieldEnum[]
}

/**
 * SubjectTeacher findMany
 */
export type SubjectTeacherFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * Filter, which SubjectTeachers to fetch.
   */
  where?: Prisma.SubjectTeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of SubjectTeachers to fetch.
   */
  orderBy?:
    | Prisma.SubjectTeacherOrderByWithRelationInput
    | Prisma.SubjectTeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing SubjectTeachers.
   */
  cursor?: Prisma.SubjectTeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` SubjectTeachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` SubjectTeachers.
   */
  skip?: number
  distinct?: Prisma.SubjectTeacherScalarFieldEnum | Prisma.SubjectTeacherScalarFieldEnum[]
}

/**
 * SubjectTeacher create
 */
export type SubjectTeacherCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * The data needed to create a SubjectTeacher.
   */
  data: Prisma.XOR<Prisma.SubjectTeacherCreateInput, Prisma.SubjectTeacherUncheckedCreateInput>
}

/**
 * SubjectTeacher createMany
 */
export type SubjectTeacherCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many SubjectTeachers.
   */
  data: Prisma.SubjectTeacherCreateManyInput | Prisma.SubjectTeacherCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * SubjectTeacher createManyAndReturn
 */
export type SubjectTeacherCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * The data used to create many SubjectTeachers.
   */
  data: Prisma.SubjectTeacherCreateManyInput | Prisma.SubjectTeacherCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * SubjectTeacher update
 */
export type SubjectTeacherUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * The data needed to update a SubjectTeacher.
   */
  data: Prisma.XOR<Prisma.SubjectTeacherUpdateInput, Prisma.SubjectTeacherUncheckedUpdateInput>
  /**
   * Choose, which SubjectTeacher to update.
   */
  where: Prisma.SubjectTeacherWhereUniqueInput
}

/**
 * SubjectTeacher updateMany
 */
export type SubjectTeacherUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update SubjectTeachers.
   */
  data: Prisma.XOR<
    Prisma.SubjectTeacherUpdateManyMutationInput,
    Prisma.SubjectTeacherUncheckedUpdateManyInput
  >
  /**
   * Filter which SubjectTeachers to update
   */
  where?: Prisma.SubjectTeacherWhereInput
  /**
   * Limit how many SubjectTeachers to update.
   */
  limit?: number
}

/**
 * SubjectTeacher updateManyAndReturn
 */
export type SubjectTeacherUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * The data used to update SubjectTeachers.
   */
  data: Prisma.XOR<
    Prisma.SubjectTeacherUpdateManyMutationInput,
    Prisma.SubjectTeacherUncheckedUpdateManyInput
  >
  /**
   * Filter which SubjectTeachers to update
   */
  where?: Prisma.SubjectTeacherWhereInput
  /**
   * Limit how many SubjectTeachers to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * SubjectTeacher upsert
 */
export type SubjectTeacherUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * The filter to search for the SubjectTeacher to update in case it exists.
   */
  where: Prisma.SubjectTeacherWhereUniqueInput
  /**
   * In case the SubjectTeacher found by the `where` argument doesn't exist, create a new SubjectTeacher with this data.
   */
  create: Prisma.XOR<Prisma.SubjectTeacherCreateInput, Prisma.SubjectTeacherUncheckedCreateInput>
  /**
   * In case the SubjectTeacher was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SubjectTeacherUpdateInput, Prisma.SubjectTeacherUncheckedUpdateInput>
}

/**
 * SubjectTeacher delete
 */
export type SubjectTeacherDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  /**
   * Filter which SubjectTeacher to delete.
   */
  where: Prisma.SubjectTeacherWhereUniqueInput
}

/**
 * SubjectTeacher deleteMany
 */
export type SubjectTeacherDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which SubjectTeachers to delete
   */
  where?: Prisma.SubjectTeacherWhereInput
  /**
   * Limit how many SubjectTeachers to delete.
   */
  limit?: number
}

/**
 * SubjectTeacher without action
 */
export type SubjectTeacherDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
}
