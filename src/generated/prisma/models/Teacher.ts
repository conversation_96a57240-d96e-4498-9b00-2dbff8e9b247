/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports the `Teacher` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from '@prisma/client/runtime/library'
import type * as $Enums from '../enums.js'
import type * as Prisma from '../internal/prismaNamespace.js'

/**
 * Model Teacher
 *
 */
export type TeacherModel = runtime.Types.Result.DefaultSelection<Prisma.$TeacherPayload>

export type AggregateTeacher = {
  _count: TeacherCountAggregateOutputType | null
  _avg: TeacherAvgAggregateOutputType | null
  _sum: TeacherSumAggregateOutputType | null
  _min: TeacherMinAggregateOutputType | null
  _max: TeacherMaxAggregateOutputType | null
}

export type TeacherAvgAggregateOutputType = {
  experience: number | null
}

export type TeacherSumAggregateOutputType = {
  experience: number | null
}

export type TeacherMinAggregateOutputType = {
  id: string | null
  userId: string | null
  employeeId: string | null
  qualification: string | null
  experience: number | null
  joiningDate: Date | null
}

export type TeacherMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  employeeId: string | null
  qualification: string | null
  experience: number | null
  joiningDate: Date | null
}

export type TeacherCountAggregateOutputType = {
  id: number
  userId: number
  employeeId: number
  qualification: number
  experience: number
  joiningDate: number
  _all: number
}

export type TeacherAvgAggregateInputType = {
  experience?: true
}

export type TeacherSumAggregateInputType = {
  experience?: true
}

export type TeacherMinAggregateInputType = {
  id?: true
  userId?: true
  employeeId?: true
  qualification?: true
  experience?: true
  joiningDate?: true
}

export type TeacherMaxAggregateInputType = {
  id?: true
  userId?: true
  employeeId?: true
  qualification?: true
  experience?: true
  joiningDate?: true
}

export type TeacherCountAggregateInputType = {
  id?: true
  userId?: true
  employeeId?: true
  qualification?: true
  experience?: true
  joiningDate?: true
  _all?: true
}

export type TeacherAggregateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Teacher to aggregate.
   */
  where?: Prisma.TeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Teachers to fetch.
   */
  orderBy?: Prisma.TeacherOrderByWithRelationInput | Prisma.TeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the start position
   */
  cursor?: Prisma.TeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Teachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Teachers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Count returned Teachers
   **/
  _count?: true | TeacherCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to average
   **/
  _avg?: TeacherAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to sum
   **/
  _sum?: TeacherSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the minimum value
   **/
  _min?: TeacherMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   *
   * Select which fields to find the maximum value
   **/
  _max?: TeacherMaxAggregateInputType
}

export type GetTeacherAggregateType<T extends TeacherAggregateArgs> = {
  [P in keyof T & keyof AggregateTeacher]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTeacher[P]>
    : Prisma.GetScalarType<T[P], AggregateTeacher[P]>
}

export type TeacherGroupByArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.TeacherWhereInput
  orderBy?: Prisma.TeacherOrderByWithAggregationInput | Prisma.TeacherOrderByWithAggregationInput[]
  by: Prisma.TeacherScalarFieldEnum[] | Prisma.TeacherScalarFieldEnum
  having?: Prisma.TeacherScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TeacherCountAggregateInputType | true
  _avg?: TeacherAvgAggregateInputType
  _sum?: TeacherSumAggregateInputType
  _min?: TeacherMinAggregateInputType
  _max?: TeacherMaxAggregateInputType
}

export type TeacherGroupByOutputType = {
  id: string
  userId: string
  employeeId: string
  qualification: string | null
  experience: number | null
  joiningDate: Date
  _count: TeacherCountAggregateOutputType | null
  _avg: TeacherAvgAggregateOutputType | null
  _sum: TeacherSumAggregateOutputType | null
  _min: TeacherMinAggregateOutputType | null
  _max: TeacherMaxAggregateOutputType | null
}

type GetTeacherGroupByPayload<T extends TeacherGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TeacherGroupByOutputType, T['by']> & {
      [P in keyof T & keyof TeacherGroupByOutputType]: P extends '_count'
        ? T[P] extends boolean
          ? number
          : Prisma.GetScalarType<T[P], TeacherGroupByOutputType[P]>
        : Prisma.GetScalarType<T[P], TeacherGroupByOutputType[P]>
    }
  >
>

export type TeacherWhereInput = {
  AND?: Prisma.TeacherWhereInput | Prisma.TeacherWhereInput[]
  OR?: Prisma.TeacherWhereInput[]
  NOT?: Prisma.TeacherWhereInput | Prisma.TeacherWhereInput[]
  id?: Prisma.StringFilter<'Teacher'> | string
  userId?: Prisma.StringFilter<'Teacher'> | string
  employeeId?: Prisma.StringFilter<'Teacher'> | string
  qualification?: Prisma.StringNullableFilter<'Teacher'> | string | null
  experience?: Prisma.IntNullableFilter<'Teacher'> | number | null
  joiningDate?: Prisma.DateTimeFilter<'Teacher'> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  Subjects?: Prisma.SubjectTeacherListRelationFilter
}

export type TeacherOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrderInput | Prisma.SortOrder
  experience?: Prisma.SortOrderInput | Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  Subjects?: Prisma.SubjectTeacherOrderByRelationAggregateInput
}

export type TeacherWhereUniqueInput = Prisma.AtLeast<
  {
    id?: string
    userId?: string
    employeeId?: string
    AND?: Prisma.TeacherWhereInput | Prisma.TeacherWhereInput[]
    OR?: Prisma.TeacherWhereInput[]
    NOT?: Prisma.TeacherWhereInput | Prisma.TeacherWhereInput[]
    qualification?: Prisma.StringNullableFilter<'Teacher'> | string | null
    experience?: Prisma.IntNullableFilter<'Teacher'> | number | null
    joiningDate?: Prisma.DateTimeFilter<'Teacher'> | Date | string
    user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
    Subjects?: Prisma.SubjectTeacherListRelationFilter
  },
  'id' | 'userId' | 'employeeId'
>

export type TeacherOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrderInput | Prisma.SortOrder
  experience?: Prisma.SortOrderInput | Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
  _count?: Prisma.TeacherCountOrderByAggregateInput
  _avg?: Prisma.TeacherAvgOrderByAggregateInput
  _max?: Prisma.TeacherMaxOrderByAggregateInput
  _min?: Prisma.TeacherMinOrderByAggregateInput
  _sum?: Prisma.TeacherSumOrderByAggregateInput
}

export type TeacherScalarWhereWithAggregatesInput = {
  AND?:
    | Prisma.TeacherScalarWhereWithAggregatesInput
    | Prisma.TeacherScalarWhereWithAggregatesInput[]
  OR?: Prisma.TeacherScalarWhereWithAggregatesInput[]
  NOT?:
    | Prisma.TeacherScalarWhereWithAggregatesInput
    | Prisma.TeacherScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<'Teacher'> | string
  userId?: Prisma.StringWithAggregatesFilter<'Teacher'> | string
  employeeId?: Prisma.StringWithAggregatesFilter<'Teacher'> | string
  qualification?: Prisma.StringNullableWithAggregatesFilter<'Teacher'> | string | null
  experience?: Prisma.IntNullableWithAggregatesFilter<'Teacher'> | number | null
  joiningDate?: Prisma.DateTimeWithAggregatesFilter<'Teacher'> | Date | string
}

export type TeacherCreateInput = {
  id?: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
  user: Prisma.UserCreateNestedOneWithoutTeacherProfileInput
  Subjects?: Prisma.SubjectTeacherCreateNestedManyWithoutTeacherInput
}

export type TeacherUncheckedCreateInput = {
  id?: string
  userId: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
  Subjects?: Prisma.SubjectTeacherUncheckedCreateNestedManyWithoutTeacherInput
}

export type TeacherUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutTeacherProfileNestedInput
  Subjects?: Prisma.SubjectTeacherUpdateManyWithoutTeacherNestedInput
}

export type TeacherUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  Subjects?: Prisma.SubjectTeacherUncheckedUpdateManyWithoutTeacherNestedInput
}

export type TeacherCreateManyInput = {
  id?: string
  userId: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
}

export type TeacherUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TeacherUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TeacherScalarRelationFilter = {
  is?: Prisma.TeacherWhereInput
  isNot?: Prisma.TeacherWhereInput
}

export type TeacherNullableScalarRelationFilter = {
  is?: Prisma.TeacherWhereInput | null
  isNot?: Prisma.TeacherWhereInput | null
}

export type TeacherCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
}

export type TeacherAvgOrderByAggregateInput = {
  experience?: Prisma.SortOrder
}

export type TeacherMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
}

export type TeacherMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  employeeId?: Prisma.SortOrder
  qualification?: Prisma.SortOrder
  experience?: Prisma.SortOrder
  joiningDate?: Prisma.SortOrder
}

export type TeacherSumOrderByAggregateInput = {
  experience?: Prisma.SortOrder
}

export type TeacherCreateNestedOneWithoutSubjectsInput = {
  create?: Prisma.XOR<
    Prisma.TeacherCreateWithoutSubjectsInput,
    Prisma.TeacherUncheckedCreateWithoutSubjectsInput
  >
  connectOrCreate?: Prisma.TeacherCreateOrConnectWithoutSubjectsInput
  connect?: Prisma.TeacherWhereUniqueInput
}

export type TeacherUpdateOneRequiredWithoutSubjectsNestedInput = {
  create?: Prisma.XOR<
    Prisma.TeacherCreateWithoutSubjectsInput,
    Prisma.TeacherUncheckedCreateWithoutSubjectsInput
  >
  connectOrCreate?: Prisma.TeacherCreateOrConnectWithoutSubjectsInput
  upsert?: Prisma.TeacherUpsertWithoutSubjectsInput
  connect?: Prisma.TeacherWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.TeacherUpdateToOneWithWhereWithoutSubjectsInput,
      Prisma.TeacherUpdateWithoutSubjectsInput
    >,
    Prisma.TeacherUncheckedUpdateWithoutSubjectsInput
  >
}

export type TeacherCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.TeacherCreateWithoutUserInput,
    Prisma.TeacherUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TeacherCreateOrConnectWithoutUserInput
  connect?: Prisma.TeacherWhereUniqueInput
}

export type TeacherUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<
    Prisma.TeacherCreateWithoutUserInput,
    Prisma.TeacherUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TeacherCreateOrConnectWithoutUserInput
  connect?: Prisma.TeacherWhereUniqueInput
}

export type TeacherUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.TeacherCreateWithoutUserInput,
    Prisma.TeacherUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TeacherCreateOrConnectWithoutUserInput
  upsert?: Prisma.TeacherUpsertWithoutUserInput
  disconnect?: Prisma.TeacherWhereInput | boolean
  delete?: Prisma.TeacherWhereInput | boolean
  connect?: Prisma.TeacherWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.TeacherUpdateToOneWithWhereWithoutUserInput,
      Prisma.TeacherUpdateWithoutUserInput
    >,
    Prisma.TeacherUncheckedUpdateWithoutUserInput
  >
}

export type TeacherUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<
    Prisma.TeacherCreateWithoutUserInput,
    Prisma.TeacherUncheckedCreateWithoutUserInput
  >
  connectOrCreate?: Prisma.TeacherCreateOrConnectWithoutUserInput
  upsert?: Prisma.TeacherUpsertWithoutUserInput
  disconnect?: Prisma.TeacherWhereInput | boolean
  delete?: Prisma.TeacherWhereInput | boolean
  connect?: Prisma.TeacherWhereUniqueInput
  update?: Prisma.XOR<
    Prisma.XOR<
      Prisma.TeacherUpdateToOneWithWhereWithoutUserInput,
      Prisma.TeacherUpdateWithoutUserInput
    >,
    Prisma.TeacherUncheckedUpdateWithoutUserInput
  >
}

export type NullableIntFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type TeacherCreateWithoutSubjectsInput = {
  id?: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
  user: Prisma.UserCreateNestedOneWithoutTeacherProfileInput
}

export type TeacherUncheckedCreateWithoutSubjectsInput = {
  id?: string
  userId: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
}

export type TeacherCreateOrConnectWithoutSubjectsInput = {
  where: Prisma.TeacherWhereUniqueInput
  create: Prisma.XOR<
    Prisma.TeacherCreateWithoutSubjectsInput,
    Prisma.TeacherUncheckedCreateWithoutSubjectsInput
  >
}

export type TeacherUpsertWithoutSubjectsInput = {
  update: Prisma.XOR<
    Prisma.TeacherUpdateWithoutSubjectsInput,
    Prisma.TeacherUncheckedUpdateWithoutSubjectsInput
  >
  create: Prisma.XOR<
    Prisma.TeacherCreateWithoutSubjectsInput,
    Prisma.TeacherUncheckedCreateWithoutSubjectsInput
  >
  where?: Prisma.TeacherWhereInput
}

export type TeacherUpdateToOneWithWhereWithoutSubjectsInput = {
  where?: Prisma.TeacherWhereInput
  data: Prisma.XOR<
    Prisma.TeacherUpdateWithoutSubjectsInput,
    Prisma.TeacherUncheckedUpdateWithoutSubjectsInput
  >
}

export type TeacherUpdateWithoutSubjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutTeacherProfileNestedInput
}

export type TeacherUncheckedUpdateWithoutSubjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TeacherCreateWithoutUserInput = {
  id?: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
  Subjects?: Prisma.SubjectTeacherCreateNestedManyWithoutTeacherInput
}

export type TeacherUncheckedCreateWithoutUserInput = {
  id?: string
  employeeId: string
  qualification?: string | null
  experience?: number | null
  joiningDate?: Date | string
  Subjects?: Prisma.SubjectTeacherUncheckedCreateNestedManyWithoutTeacherInput
}

export type TeacherCreateOrConnectWithoutUserInput = {
  where: Prisma.TeacherWhereUniqueInput
  create: Prisma.XOR<
    Prisma.TeacherCreateWithoutUserInput,
    Prisma.TeacherUncheckedCreateWithoutUserInput
  >
}

export type TeacherUpsertWithoutUserInput = {
  update: Prisma.XOR<
    Prisma.TeacherUpdateWithoutUserInput,
    Prisma.TeacherUncheckedUpdateWithoutUserInput
  >
  create: Prisma.XOR<
    Prisma.TeacherCreateWithoutUserInput,
    Prisma.TeacherUncheckedCreateWithoutUserInput
  >
  where?: Prisma.TeacherWhereInput
}

export type TeacherUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.TeacherWhereInput
  data: Prisma.XOR<
    Prisma.TeacherUpdateWithoutUserInput,
    Prisma.TeacherUncheckedUpdateWithoutUserInput
  >
}

export type TeacherUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  Subjects?: Prisma.SubjectTeacherUpdateManyWithoutTeacherNestedInput
}

export type TeacherUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  employeeId?: Prisma.StringFieldUpdateOperationsInput | string
  qualification?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  experience?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  joiningDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  Subjects?: Prisma.SubjectTeacherUncheckedUpdateManyWithoutTeacherNestedInput
}

/**
 * Count Type TeacherCountOutputType
 */

export type TeacherCountOutputType = {
  Subjects: number
}

export type TeacherCountOutputTypeSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  Subjects?: boolean | TeacherCountOutputTypeCountSubjectsArgs
}

/**
 * TeacherCountOutputType without action
 */
export type TeacherCountOutputTypeDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the TeacherCountOutputType
   */
  select?: Prisma.TeacherCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TeacherCountOutputType without action
 */
export type TeacherCountOutputTypeCountSubjectsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  where?: Prisma.SubjectTeacherWhereInput
}

export type TeacherSelect<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    employeeId?: boolean
    qualification?: boolean
    experience?: boolean
    joiningDate?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
    Subjects?: boolean | Prisma.Teacher$SubjectsArgs<ExtArgs>
    _count?: boolean | Prisma.TeacherCountOutputTypeDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['teacher']
>

export type TeacherSelectCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    employeeId?: boolean
    qualification?: boolean
    experience?: boolean
    joiningDate?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['teacher']
>

export type TeacherSelectUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetSelect<
  {
    id?: boolean
    userId?: boolean
    employeeId?: boolean
    qualification?: boolean
    experience?: boolean
    joiningDate?: boolean
    user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  },
  ExtArgs['result']['teacher']
>

export type TeacherSelectScalar = {
  id?: boolean
  userId?: boolean
  employeeId?: boolean
  qualification?: boolean
  experience?: boolean
  joiningDate?: boolean
}

export type TeacherOmit<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = runtime.Types.Extensions.GetOmit<
  'id' | 'userId' | 'employeeId' | 'qualification' | 'experience' | 'joiningDate',
  ExtArgs['result']['teacher']
>
export type TeacherInclude<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  Subjects?: boolean | Prisma.Teacher$SubjectsArgs<ExtArgs>
  _count?: boolean | Prisma.TeacherCountOutputTypeDefaultArgs<ExtArgs>
}
export type TeacherIncludeCreateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type TeacherIncludeUpdateManyAndReturn<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $TeacherPayload<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  name: 'Teacher'
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
    Subjects: Prisma.$SubjectTeacherPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<
    {
      id: string
      userId: string
      employeeId: string
      qualification: string | null
      experience: number | null
      joiningDate: Date
    },
    ExtArgs['result']['teacher']
  >
  composites: {}
}

export type TeacherGetPayload<S extends boolean | null | undefined | TeacherDefaultArgs> =
  runtime.Types.Result.GetResult<Prisma.$TeacherPayload, S>

export type TeacherCountArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = Omit<TeacherFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
  select?: TeacherCountAggregateInputType | true
}

export interface TeacherDelegate<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Teacher']; meta: { name: 'Teacher' } }
  /**
   * Find zero or one Teacher that matches the filter.
   * @param {TeacherFindUniqueArgs} args - Arguments to find a Teacher
   * @example
   * // Get one Teacher
   * const teacher = await prisma.teacher.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TeacherFindUniqueArgs>(
    args: Prisma.SelectSubset<T, TeacherFindUniqueArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'findUnique',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find one Teacher that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TeacherFindUniqueOrThrowArgs} args - Arguments to find a Teacher
   * @example
   * // Get one Teacher
   * const teacher = await prisma.teacher.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TeacherFindUniqueOrThrowArgs>(
    args: Prisma.SelectSubset<T, TeacherFindUniqueOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'findUniqueOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Teacher that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeacherFindFirstArgs} args - Arguments to find a Teacher
   * @example
   * // Get one Teacher
   * const teacher = await prisma.teacher.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TeacherFindFirstArgs>(
    args?: Prisma.SelectSubset<T, TeacherFindFirstArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'findFirst',
      GlobalOmitOptions
    > | null,
    null,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find the first Teacher that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeacherFindFirstOrThrowArgs} args - Arguments to find a Teacher
   * @example
   * // Get one Teacher
   * const teacher = await prisma.teacher.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TeacherFindFirstOrThrowArgs>(
    args?: Prisma.SelectSubset<T, TeacherFindFirstOrThrowArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'findFirstOrThrow',
      GlobalOmitOptions
    >,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Find zero or more Teachers that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeacherFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Teachers
   * const teachers = await prisma.teacher.findMany()
   *
   * // Get first 10 Teachers
   * const teachers = await prisma.teacher.findMany({ take: 10 })
   *
   * // Only select the `id`
   * const teacherWithIdOnly = await prisma.teacher.findMany({ select: { id: true } })
   *
   */
  findMany<T extends TeacherFindManyArgs>(
    args?: Prisma.SelectSubset<T, TeacherFindManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'findMany',
      GlobalOmitOptions
    >
  >

  /**
   * Create a Teacher.
   * @param {TeacherCreateArgs} args - Arguments to create a Teacher.
   * @example
   * // Create one Teacher
   * const Teacher = await prisma.teacher.create({
   *   data: {
   *     // ... data to create a Teacher
   *   }
   * })
   *
   */
  create<T extends TeacherCreateArgs>(
    args: Prisma.SelectSubset<T, TeacherCreateArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<Prisma.$TeacherPayload<ExtArgs>, T, 'create', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Create many Teachers.
   * @param {TeacherCreateManyArgs} args - Arguments to create many Teachers.
   * @example
   * // Create many Teachers
   * const teacher = await prisma.teacher.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   */
  createMany<T extends TeacherCreateManyArgs>(
    args?: Prisma.SelectSubset<T, TeacherCreateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Teachers and returns the data saved in the database.
   * @param {TeacherCreateManyAndReturnArgs} args - Arguments to create many Teachers.
   * @example
   * // Create many Teachers
   * const teacher = await prisma.teacher.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Create many Teachers and only return the `id`
   * const teacherWithIdOnly = await prisma.teacher.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  createManyAndReturn<T extends TeacherCreateManyAndReturnArgs>(
    args?: Prisma.SelectSubset<T, TeacherCreateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'createManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Delete a Teacher.
   * @param {TeacherDeleteArgs} args - Arguments to delete one Teacher.
   * @example
   * // Delete one Teacher
   * const Teacher = await prisma.teacher.delete({
   *   where: {
   *     // ... filter to delete one Teacher
   *   }
   * })
   *
   */
  delete<T extends TeacherDeleteArgs>(
    args: Prisma.SelectSubset<T, TeacherDeleteArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<Prisma.$TeacherPayload<ExtArgs>, T, 'delete', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Update one Teacher.
   * @param {TeacherUpdateArgs} args - Arguments to update one Teacher.
   * @example
   * // Update one Teacher
   * const teacher = await prisma.teacher.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  update<T extends TeacherUpdateArgs>(
    args: Prisma.SelectSubset<T, TeacherUpdateArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<Prisma.$TeacherPayload<ExtArgs>, T, 'update', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Delete zero or more Teachers.
   * @param {TeacherDeleteManyArgs} args - Arguments to filter Teachers to delete.
   * @example
   * // Delete a few Teachers
   * const { count } = await prisma.teacher.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   *
   */
  deleteMany<T extends TeacherDeleteManyArgs>(
    args?: Prisma.SelectSubset<T, TeacherDeleteManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Teachers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeacherUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Teachers
   * const teacher = await prisma.teacher.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   *
   */
  updateMany<T extends TeacherUpdateManyArgs>(
    args: Prisma.SelectSubset<T, TeacherUpdateManyArgs<ExtArgs>>
  ): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Teachers and returns the data updated in the database.
   * @param {TeacherUpdateManyAndReturnArgs} args - Arguments to update many Teachers.
   * @example
   * // Update many Teachers
   * const teacher = await prisma.teacher.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *
   * // Update zero or more Teachers and only return the `id`
   * const teacherWithIdOnly = await prisma.teacher.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   *
   */
  updateManyAndReturn<T extends TeacherUpdateManyAndReturnArgs>(
    args: Prisma.SelectSubset<T, TeacherUpdateManyAndReturnArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    runtime.Types.Result.GetResult<
      Prisma.$TeacherPayload<ExtArgs>,
      T,
      'updateManyAndReturn',
      GlobalOmitOptions
    >
  >

  /**
   * Create or update one Teacher.
   * @param {TeacherUpsertArgs} args - Arguments to update or create a Teacher.
   * @example
   * // Update or create a Teacher
   * const teacher = await prisma.teacher.upsert({
   *   create: {
   *     // ... data to create a Teacher
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Teacher we want to update
   *   }
   * })
   */
  upsert<T extends TeacherUpsertArgs>(
    args: Prisma.SelectSubset<T, TeacherUpsertArgs<ExtArgs>>
  ): Prisma.Prisma__TeacherClient<
    runtime.Types.Result.GetResult<Prisma.$TeacherPayload<ExtArgs>, T, 'upsert', GlobalOmitOptions>,
    never,
    ExtArgs,
    GlobalOmitOptions
  >

  /**
   * Count the number of Teachers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeacherCountArgs} args - Arguments to filter Teachers to count.
   * @example
   * // Count the number of Teachers
   * const count = await prisma.teacher.count({
   *   where: {
   *     // ... the filter for the Teachers we want to count
   *   }
   * })
   **/
  count<T extends TeacherCountArgs>(
    args?: Prisma.Subset<T, TeacherCountArgs>
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TeacherCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Teacher.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeacherAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
   **/
  aggregate<T extends TeacherAggregateArgs>(
    args: Prisma.Subset<T, TeacherAggregateArgs>
  ): Prisma.PrismaPromise<GetTeacherAggregateType<T>>

  /**
   * Group by Teacher.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TeacherGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   *
   **/
  groupBy<
    T extends TeacherGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TeacherGroupByArgs['orderBy'] }
      : { orderBy?: TeacherGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<
      Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>
    >,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
      ? `Error: "by" must not be empty.`
      : HavingValid extends Prisma.False
        ? {
            [P in HavingFields]: P extends ByFields
              ? never
              : P extends string
                ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
                : [Error, 'Field ', P, ` in "having" needs to be provided in "by"`]
          }[HavingFields]
        : 'take' extends Prisma.Keys<T>
          ? 'orderBy' extends Prisma.Keys<T>
            ? ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields]
            : 'Error: If you provide "take", you also need to provide "orderBy"'
          : 'skip' extends Prisma.Keys<T>
            ? 'orderBy' extends Prisma.Keys<T>
              ? ByValid extends Prisma.True
                ? {}
                : {
                    [P in OrderFields]: P extends ByFields
                      ? never
                      : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                  }[OrderFields]
              : 'Error: If you provide "skip", you also need to provide "orderBy"'
            : ByValid extends Prisma.True
              ? {}
              : {
                  [P in OrderFields]: P extends ByFields
                    ? never
                    : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
                }[OrderFields],
  >(
    args: Prisma.SubsetIntersection<T, TeacherGroupByArgs, OrderByArg> & InputErrors
  ): {} extends InputErrors ? GetTeacherGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Teacher model
   */
  readonly fields: TeacherFieldRefs
}

/**
 * The delegate class that acts as a "Promise-like" for Teacher.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TeacherClient<
  T,
  Null = never,
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
  GlobalOmitOptions = {},
> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: 'PrismaPromise'
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>
  ): Prisma.Prisma__UserClient<
    | runtime.Types.Result.GetResult<
        Prisma.$UserPayload<ExtArgs>,
        T,
        'findUniqueOrThrow',
        GlobalOmitOptions
      >
    | Null,
    Null,
    ExtArgs,
    GlobalOmitOptions
  >
  Subjects<T extends Prisma.Teacher$SubjectsArgs<ExtArgs> = {}>(
    args?: Prisma.Subset<T, Prisma.Teacher$SubjectsArgs<ExtArgs>>
  ): Prisma.PrismaPromise<
    | runtime.Types.Result.GetResult<
        Prisma.$SubjectTeacherPayload<ExtArgs>,
        T,
        'findMany',
        GlobalOmitOptions
      >
    | Null
  >
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}

/**
 * Fields of the Teacher model
 */
export interface TeacherFieldRefs {
  readonly id: Prisma.FieldRef<'Teacher', 'String'>
  readonly userId: Prisma.FieldRef<'Teacher', 'String'>
  readonly employeeId: Prisma.FieldRef<'Teacher', 'String'>
  readonly qualification: Prisma.FieldRef<'Teacher', 'String'>
  readonly experience: Prisma.FieldRef<'Teacher', 'Int'>
  readonly joiningDate: Prisma.FieldRef<'Teacher', 'DateTime'>
}

// Custom InputTypes
/**
 * Teacher findUnique
 */
export type TeacherFindUniqueArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * Filter, which Teacher to fetch.
   */
  where: Prisma.TeacherWhereUniqueInput
}

/**
 * Teacher findUniqueOrThrow
 */
export type TeacherFindUniqueOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * Filter, which Teacher to fetch.
   */
  where: Prisma.TeacherWhereUniqueInput
}

/**
 * Teacher findFirst
 */
export type TeacherFindFirstArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * Filter, which Teacher to fetch.
   */
  where?: Prisma.TeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Teachers to fetch.
   */
  orderBy?: Prisma.TeacherOrderByWithRelationInput | Prisma.TeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Teachers.
   */
  cursor?: Prisma.TeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Teachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Teachers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Teachers.
   */
  distinct?: Prisma.TeacherScalarFieldEnum | Prisma.TeacherScalarFieldEnum[]
}

/**
 * Teacher findFirstOrThrow
 */
export type TeacherFindFirstOrThrowArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * Filter, which Teacher to fetch.
   */
  where?: Prisma.TeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Teachers to fetch.
   */
  orderBy?: Prisma.TeacherOrderByWithRelationInput | Prisma.TeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for searching for Teachers.
   */
  cursor?: Prisma.TeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Teachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Teachers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   *
   * Filter by unique combinations of Teachers.
   */
  distinct?: Prisma.TeacherScalarFieldEnum | Prisma.TeacherScalarFieldEnum[]
}

/**
 * Teacher findMany
 */
export type TeacherFindManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * Filter, which Teachers to fetch.
   */
  where?: Prisma.TeacherWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   *
   * Determine the order of Teachers to fetch.
   */
  orderBy?: Prisma.TeacherOrderByWithRelationInput | Prisma.TeacherOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   *
   * Sets the position for listing Teachers.
   */
  cursor?: Prisma.TeacherWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Take `±n` Teachers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   *
   * Skip the first `n` Teachers.
   */
  skip?: number
  distinct?: Prisma.TeacherScalarFieldEnum | Prisma.TeacherScalarFieldEnum[]
}

/**
 * Teacher create
 */
export type TeacherCreateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * The data needed to create a Teacher.
   */
  data: Prisma.XOR<Prisma.TeacherCreateInput, Prisma.TeacherUncheckedCreateInput>
}

/**
 * Teacher createMany
 */
export type TeacherCreateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to create many Teachers.
   */
  data: Prisma.TeacherCreateManyInput | Prisma.TeacherCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Teacher createManyAndReturn
 */
export type TeacherCreateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * The data used to create many Teachers.
   */
  data: Prisma.TeacherCreateManyInput | Prisma.TeacherCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Teacher update
 */
export type TeacherUpdateArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * The data needed to update a Teacher.
   */
  data: Prisma.XOR<Prisma.TeacherUpdateInput, Prisma.TeacherUncheckedUpdateInput>
  /**
   * Choose, which Teacher to update.
   */
  where: Prisma.TeacherWhereUniqueInput
}

/**
 * Teacher updateMany
 */
export type TeacherUpdateManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * The data used to update Teachers.
   */
  data: Prisma.XOR<Prisma.TeacherUpdateManyMutationInput, Prisma.TeacherUncheckedUpdateManyInput>
  /**
   * Filter which Teachers to update
   */
  where?: Prisma.TeacherWhereInput
  /**
   * Limit how many Teachers to update.
   */
  limit?: number
}

/**
 * Teacher updateManyAndReturn
 */
export type TeacherUpdateManyAndReturnArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * The data used to update Teachers.
   */
  data: Prisma.XOR<Prisma.TeacherUpdateManyMutationInput, Prisma.TeacherUncheckedUpdateManyInput>
  /**
   * Filter which Teachers to update
   */
  where?: Prisma.TeacherWhereInput
  /**
   * Limit how many Teachers to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Teacher upsert
 */
export type TeacherUpsertArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * The filter to search for the Teacher to update in case it exists.
   */
  where: Prisma.TeacherWhereUniqueInput
  /**
   * In case the Teacher found by the `where` argument doesn't exist, create a new Teacher with this data.
   */
  create: Prisma.XOR<Prisma.TeacherCreateInput, Prisma.TeacherUncheckedCreateInput>
  /**
   * In case the Teacher was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TeacherUpdateInput, Prisma.TeacherUncheckedUpdateInput>
}

/**
 * Teacher delete
 */
export type TeacherDeleteArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
  /**
   * Filter which Teacher to delete.
   */
  where: Prisma.TeacherWhereUniqueInput
}

/**
 * Teacher deleteMany
 */
export type TeacherDeleteManyArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Filter which Teachers to delete
   */
  where?: Prisma.TeacherWhereInput
  /**
   * Limit how many Teachers to delete.
   */
  limit?: number
}

/**
 * Teacher.Subjects
 */
export type Teacher$SubjectsArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the SubjectTeacher
   */
  select?: Prisma.SubjectTeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SubjectTeacher
   */
  omit?: Prisma.SubjectTeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SubjectTeacherInclude<ExtArgs> | null
  where?: Prisma.SubjectTeacherWhereInput
  orderBy?:
    | Prisma.SubjectTeacherOrderByWithRelationInput
    | Prisma.SubjectTeacherOrderByWithRelationInput[]
  cursor?: Prisma.SubjectTeacherWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SubjectTeacherScalarFieldEnum | Prisma.SubjectTeacherScalarFieldEnum[]
}

/**
 * Teacher without action
 */
export type TeacherDefaultArgs<
  ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs,
> = {
  /**
   * Select specific fields to fetch from the Teacher
   */
  select?: Prisma.TeacherSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Teacher
   */
  omit?: Prisma.TeacherOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TeacherInclude<ExtArgs> | null
}
