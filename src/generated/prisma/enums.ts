/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck
/**
 * This file exports all enum related types from the schema.
 *
 * 🟢 You can import this file directly.
 */
export const SchoolLevel = {
  GRADE_8: 'GRADE_8',
  GRADE_12: 'GRADE_12',
  COLLEGE: 'COLLEGE',
} as const

export type SchoolLevel = (typeof SchoolLevel)[keyof typeof SchoolLevel]

export const TokenTypes = {
  VERIFICATION: 'VERIFICATION',
  EMAIL_VERIFICATION: 'EMAIL_VERIFICATION',
  TWO_FA: 'TWO_FA',
  OTP: 'OTP',
  REFRESH: 'REFRESH',
} as const

export type TokenTypes = (typeof TokenTypes)[keyof typeof TokenTypes]

export const Gender = {
  MALE: 'MALE',
  FEMALE: 'FEMALE',
  OTHER: 'OTHER',
} as const

export type Gender = (typeof Gender)[keyof typeof Gender]
