import * as dotenv from 'dotenv'
import Fastify from 'fastify'
import * as closeWithGrace from 'close-with-grace'
dotenv.config()

// function getLoggerOptions() {
//   if (process.stdout.isTTY) {
//     return {
//       level: 'info',
//       transport: {
//         target: 'pino-pretty',
//         options: {
//           translateTime: 'HH:MM:ss Z',
//           ignore: 'pid,hostname',
//         },
//       },
//     }
//   }

//   return { level: process.env.LOG_LEVEL ?? 'silent' }
// }

// Instantiate Fastify with some config
const app = Fastify({
  // logger: getLoggerOptions(),
  logger: true,
})

// Register your application as a normal plugin.
app.register(import('./app'))

// delay is the number of milliseconds for the graceful close to finish
closeWithGrace({ delay: parseInt(process.env.FASTIFY_CLOSE_GRACE_DELAY) || 500 }, async function ({
  err,
}) {
  if (err) {
    app.log.error(err)
  }
  await app.close()
} as closeWithGrace.CloseWithGraceAsyncCallback)

// Start listening.
app.listen({ port: parseInt(process.env.PORT) || 3000 }, (err: any) => {
  if (err) {
    app.log.error(err)
    process.exit(1)
  }
})
