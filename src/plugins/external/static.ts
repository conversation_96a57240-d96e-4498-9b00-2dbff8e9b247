import fp from 'fastify-plugin'
import fastifyStatic, { FastifyStaticOptions } from '@fastify/static'
import * as path from 'node:path'
import * as fs from 'node:fs'

/**
 * This plugin allows serving static files as fast as possible.
 * @see {@link https://github.com/fastify/fastify-static}
 */
export default fp<FastifyStaticOptions>(
  async (fastify) => {
    fastify.register(fastifyStatic, () => {
      const dirPath = path.join(__dirname, '../../..', fastify.config.UPLOAD_DIRNAME)
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath)
      }

      return {
        root: dirPath,
        prefix: `/${fastify.config.UPLOAD_DIRNAME}`,
      }
    })
  },
  { name: 'static' }
)
