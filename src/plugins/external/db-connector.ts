import fp from 'fastify-plugin'
import { PrismaClient } from '../../generated/prisma/client'

export default fp(
  async (fastify) => {
    const db = new PrismaClient({
      log:
        fastify.config.NODE_ENV !== 'production' ? ['query', 'info', 'warn', 'error'] : ['error'],
    })

    fastify.decorate('db', db)

    try {
      await db.$connect()
      fastify.log.info('Database connected successfully')
    } catch (error) {
      fastify.log.error('Database connection failed:', error)
      throw error
    }

    fastify.decorate('pingDb', async () => {
      try {
        fastify.log.info('Pinging database...')
        await fastify.db.$queryRaw`SELECT 1`
        return true
      } catch (error) {
        fastify.log.error(`Database ping failed: ${error}`)
        return false
      }
    })

    fastify.addHook('onClose', async () => {
      await fastify.db.$disconnect()
    })
  },
  { name: 'db-connector' }
)

// Type declaration for Fastify instance
declare module 'fastify' {
  interface FastifyInstance {
    db: PrismaClient
    pingDb: () => Promise<boolean>
  }
}
