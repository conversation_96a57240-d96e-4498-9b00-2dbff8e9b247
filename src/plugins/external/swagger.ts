import fp from 'fastify-plugin'
import fastifySwaggerUi, { FastifySwaggerUiOptions } from '@fastify/swagger-ui'
import fastifySwagger, { FastifySwaggerOptions } from '@fastify/swagger'

export default fp(
  async function (fastify) {
    /**
     * A Fastify plugin for serving Swagger (OpenAPI v2) or OpenAPI v3 schemas
     *
     * @see {@link https://github.com/fastify/fastify-swagger}
     */
    fastify.register<FastifySwaggerOptions>(fastifySwagger, {})

    /**
     * A Fastify plugin for serving Swagger UI.
     *
     * @see {@link https://github.com/fastify/fastify-swagger-ui}
     */
    fastify.register<FastifySwaggerUiOptions>(fastifySwaggerUi, {
      routePrefix: '/api/docs',
    })
  },
  { name: 'swagger' }
)
