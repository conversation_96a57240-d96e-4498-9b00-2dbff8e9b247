import env, { FastifyEnvOptions } from '@fastify/env'
import fp from 'fastify-plugin'

declare module 'fastify' {
  export interface FastifyInstance {
    config: {
      PORT: number
      NODE_ENV: string
      HOST: string
      FASTIFY_CLOSE_GRACE_DELAY: number
      LOG_LEVEL: string
      DATABASE_URL: string
      JWT_SECRET: string
      JWT_EXPIRES_IN: string
      REFRESH_TOKEN_EXPIRES_IN: string
      RATE_LIMIT_MAX: number
      RATE_LIMIT_TIMEFRAME: number
      COOKIE_SECRET: string
      COOKIE_NAME: string
      COOKIE_SECURED: boolean
      UPLOAD_DIRNAME: string
      UPLOAD_TASKS_DIRNAME: string
    }
  }
}

const schema = {
  type: 'object',
  required: [
    'PORT',
    'NODE_ENV',
    'HOST',
    'FASTIFY_CLOSE_GRACE_DELAY',
    'LOG_LEVEL',
    'DATABASE_URL',
    'JWT_SECRET',
    'JWT_EXPIRES_IN',
    'JWT_REFRESH_TOKEN_EXPIRES_IN',
    'RATE_LIMIT_MAX',
    'RATE_LIMIT_TIMEFRAME',
    'COOKIE_SECRET',
    'COOKIE_NAME',
    'COOKIE_SECURED',
  ],
  properties: {
    PORT: {
      type: 'number',
      default: 3000,
    },
    NODE_ENV: {
      type: 'string',
      enum: ['development', 'production', 'test'],
    },
    HOST: {
      type: 'string',
      default: 'localhost',
    },
    FASTIFY_CLOSE_GRACE_DELAY: {
      type: 'number',
      default: 1000,
    },
    LOG_LEVEL: {
      type: 'string',
      default: 'info',
    },

    // Database
    DATABASE_URL: {
      type: 'string',
    },

    // JWT
    JWT_SECRET: {
      type: 'string',
    },
    JWT_EXPIRES_IN: {
      type: 'string',
      default: '1h',
    },
    JWT_REFRESH_TOKEN_EXPIRES_IN: {
      type: 'string',
      default: '7d',
    },

    // Rate limit
    RATE_LIMIT_MAX: {
      type: 'number',
      default: 100,
    },
    RATE_LIMIT_TIMEFRAME: {
      type: 'number',
      default: 60000,
    },

    // Security
    COOKIE_SECRET: {
      type: 'string',
    },
    COOKIE_NAME: {
      type: 'string',
    },
    COOKIE_SECURED: {
      type: 'boolean',
      default: true,
    },

    // Files
    UPLOAD_DIRNAME: {
      type: 'string',
      minLength: 1,
      pattern: '^(?!.*\\.{2}).*$',
      default: 'uploads',
    },
    UPLOAD_TASKS_DIRNAME: {
      type: 'string',
      default: 'tasks',
    },
  },
}

/**
 * This plugins helps to check environment variables.
 *
 * @see {@link https://github.com/fastify/fastify-env}
 */
export default fp<FastifyEnvOptions>(
  async (fastify) => {
    fastify.register(env, {
      // Decorate Fastify instance with `config` key
      // Optional, default: 'config'
      confKey: 'config',

      // Schema to validate
      schema,

      // Needed to read .env in root folder
      dotenv: true,
      // or, pass config options available on dotenv module
      // dotenv: {
      //   path: `${import.meta.dirname}/.env`,
      //   debug: true
      // }

      // Source for the configuration data
      // Optional, default: process.env
      data: process.env,
    })
  },
  { name: 'env' }
)
