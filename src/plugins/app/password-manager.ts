import fp from 'fastify-plugin'
import * as bcrypt from 'bcryptjs'

export default fp(
  async (fastify) => {
    fastify.decorate('passwordManager', {
      async hash(value: string): Promise<string> {
        return await bcrypt.hash(value, 10)
      },

      async compare(value: string, hash: string): Promise<boolean> {
        return await bcrypt.compare(value, hash)
      }
    })
  },
  {
    name: 'password-manager',
  }
)

declare module 'fastify' {
  export interface FastifyInstance {
    passwordManager: {
      hash(value: string): Promise<string>
      compare(value: string, hash: string): Promise<boolean>
    }
  }
}
