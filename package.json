{"name": "sis_fastify", "version": "1.0.0", "description": "This project was bootstrapped with Fastify-CLI.", "main": "app.ts", "directories": {"test": "test"}, "scripts": {"test": "npm run build:ts && tsc -p test/tsconfig.json && c8 node --test -r ts-node/register \"test/**/*.ts\"", "start": "npm run build:ts && fastify start -l info dist/app.js", "build:ts": "tsc", "watch:ts": "tsc -w", "dev": "npm run build:ts && concurrently -k -p \"[{name}]\" -n \"TypeScript,App\" -c \"yellow.bold,cyan.bold\" \"npm:watch:ts\" \"npm:dev:start\"", "dev:start": "fastify start --ignore-watch=.ts$ -w -l info -P dist/app.js", "format": "prettier --write \"src/**/*.ts\"", "db:generate": "prisma generate --schema ./prisma", "db:push": "prisma push --schema ./prisma", "db:migrate": "prisma migrate dev  --schema ./prisma", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio --schema ./prisma", "db:seed": "tsx --env-file=.env ./scripts/db-seed/base-seeder.ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/autoload": "^6.0.0", "@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.1.0", "@fastify/env": "^5.0.2", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^10.0.0", "@fastify/multipart": "^9.0.3", "@fastify/rate-limit": "^10.3.0", "@fastify/sensible": "^6.0.0", "@fastify/session": "^11.1.0", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@fastify/under-pressure": "^9.0.3", "@prisma/client": "^6.14.0", "bcryptjs": "^3.0.2", "close-with-grace": "^2.2.0", "dotenv": "^17.2.1", "fastify": "^5.0.0", "fastify-cli": "^7.4.0", "fastify-plugin": "^5.0.0", "jsonwebtoken": "^9.0.2", "otplib": "^12.0.1", "sanitize-filename": "^1.6.3"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.1.0", "c8": "^10.1.2", "concurrently": "^9.0.0", "eslint": "^9.33.0", "fastify-tsconfig": "^3.0.0", "globals": "^16.3.0", "prettier": "^3.6.2", "prisma": "^6.14.0", "ts-node": "^10.4.0", "typescript": "~5.8.2", "typescript-eslint": "^8.39.1"}}