enum Gender {
  MALE
  FEMALE
  OTHER
}

model User {
  id                   String    @id @default(cuid())
  email                String    @unique
  username             String    @unique
  firstName            String
  middleName           String?
  lastName             String
  otherName            String?
  gender               Gender
  dateOfBirth          DateTime?
  phone                String
  otherPhone           String?
  nationality          String?
  avatar               String?
  city                 String
  barangay             String?
  cra                  String?
  pobProvince          String?
  pobCity              String?
  religiousAffiliation String?
  isVerified           Boolean   @default(false)
  isActive             Boolean   @default(true)
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  // Password and authentication
  password  String
  lastLogin DateTime?

  // Role relationship
  roleId String
  role   Role   @relation(fields: [roleId], references: [id])

  // Verification token
  token Token?

  // Additional profile data based on role
  studentProfile Student?
  teacherProfile Teacher?
  parentProfile  Parent?
  adminProfile   Admin?
  staffProfile   Staff?

  // School Info
  schools     SchoolUser[]
  departments DepartmentStaff[]

  // Activity tracking
  loginLogs    LoginLog[]
  activityLogs ActivityLog[]

  @@map("users")
}

model Admin {
  id         String @id @default(cuid())
  userId     String @unique
  adminLevel String // super_admin, school_admin, etc.
  // department String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("admins")
}

model Teacher {
  id            String   @id @default(cuid())
  userId        String   @unique
  employeeId    String   @unique
  // department    String
  qualification String?
  experience    Int? // years of experience
  joiningDate   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  Subjects SubjectTeacher[]

  @@map("teachers")
}

model Staff {
  id            String   @id @default(cuid())
  userId        String   @unique
  employeeId    String   @unique
  // department    String
  qualification String?
  experience    Int? // years of experience
  joiningDate   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("staff")
}

model Student {
  id               String   @id @default(cuid())
  userId           String   @unique
  studentId        String   @unique
  // dateOfBirth      DateTime
  // address          String?
  emergencyContact String?
  enrollmentDate   DateTime @default(now())

  schoolId String?
  school   School? @relation(fields: [schoolId], references: [id])

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("students")
}

model Parent {
  id           String  @id @default(cuid())
  userId       String  @unique
  occupation   String?
  relationship String // father, mother, guardian

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("parents")
}
