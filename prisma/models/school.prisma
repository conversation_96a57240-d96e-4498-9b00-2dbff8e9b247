enum SchoolLevel {
  GRADE_8
  GRADE_12
  COLLEGE
}

model SchoolType {
  id          Int         @id @default(autoincrement())
  name        String
  description String?
  level       SchoolLevel
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  schools School[]

  @@map("school_type")
}

model School {
  id            String   @id @default(cuid())
  name          String
  address       String
  phone         String
  email         String
  website       String
  motto         String?
  description   String?
  logo          String?
  certification String?
  isActive      Boolean  @default(true)
  verified      Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  typeId Int
  type   SchoolType @relation(fields: [typeId], references: [id])

  students     Student[]
  departments  Department[]
  schoolAdmins SchoolUser[]
  sessions     Session[]
  subjects     Subject[]
  classes      Class[]

  @@map("school")
}

model SchoolUser {
  id        String   @id @default(cuid())
  role      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  school   School @relation(fields: [schoolId], references: [id])
  schoolId String
  user     User   @relation(fields: [userId], references: [id])
  userId   String

  @@map("school_user")
}

model Department {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  school   School @relation(fields: [schoolId], references: [id])
  schoolId String

  staffs DepartmentStaff[]

  @@unique([name, schoolId])
  @@map("department")
}

model DepartmentStaff {
  id        String   @id @default(cuid())
  role      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  department   Department @relation(fields: [departmentId], references: [id])
  departmentId String
  user         User       @relation(fields: [userId], references: [id])
  userId       String

  @@map("department_staff")
}

model Session {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  school   School @relation(fields: [schoolId], references: [id])
  schoolId String

  @@unique([name, schoolId])
  @@map("session")
}
