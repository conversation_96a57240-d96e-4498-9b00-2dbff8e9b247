model Class {
  id          String   @id @default(cuid())
  name        String
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  sections Section[]
  subjects SubjectClass[]

  @@unique([name, schoolId])
  @@index([schoolId])
  @@map("class")
}

model Section {
  id          String   @id @default(cuid())
  name        String
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  classId String
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)

  @@map("section")
}

model Subject {
  id          String   @id @default(cuid())
  name        String
  code        String
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  schoolId String
  school   School @relation(fields: [schoolId], references: [id])

  classes  SubjectClass[]
  teachers SubjectTeacher[]

  @@unique([name, schoolId])
  @@unique([code, schoolId])
  @@index([schoolId])
  @@map("subject")
}

model SubjectClass {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  classId String
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)

  @@unique([subjectId, classId])
  @@index([subjectId])
  @@index([classId])
  @@map("subject_class")
}

model SubjectTeacher {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  teacherId String
  teacher   Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@unique([subjectId, teacherId])
  @@index([subjectId])
  @@index([teacherId])
  @@map("subject_teacher")
}
