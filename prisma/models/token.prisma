enum TokenTypes {
  VERIFICATION
  EMAIL_VERIFICATION
  TWO_FA
  OTP
  REFRESH
}

model Token {
  id        String     @id @default(cuid())
  token     String     @unique
  type      TokenTypes
  expiresAt DateTime
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, type])
  @@index([userId])
  @@index([type])
  @@map("token")
}
