// Logging and activity tracking
model LoginLog {
  id        String    @id @default(cuid())
  userId    String
  ipAddress String
  userAgent String?
  loginAt   DateTime  @default(now())
  logoutAt  DateTime?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("login_logs")
}

model ActivityLog {
  id        String   @id @default(cuid())
  userId    String
  action    String // created_user, updated_permission, etc.
  resource  String // users, roles, permissions
  details   Json? // additional details about the action
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("activity_logs")
}
