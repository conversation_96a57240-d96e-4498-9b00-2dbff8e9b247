// prisma/seed.js
import { PrismaClient } from '../../src/generated/prisma/client';
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // Create default permissions
  const permissions = [
    // User management
    { name: 'Create Users', resource: 'users', action: 'create', description: 'Can create new users' },
    { name: 'Read Users', resource: 'users', action: 'read', description: 'Can view user information' },
    { name: 'Update Users', resource: 'users', action: 'update', description: 'Can update user information' },
    { name: 'Delete Users', resource: 'users', action: 'delete', description: 'Can delete users' },
    
    // Role management
    { name: 'Create Roles', resource: 'roles', action: 'create', description: 'Can create new roles' },
    { name: 'Read Roles', resource: 'roles', action: 'read', description: 'Can view role information' },
    { name: 'Update Roles', resource: 'roles', action: 'update', description: 'Can update role information' },
    { name: 'Delete Roles', resource: 'roles', action: 'delete', description: 'Can delete roles' },
    
    // Permission management
    { name: 'Create Permissions', resource: 'permissions', action: 'create', description: 'Can create new permissions' },
    { name: 'Read Permissions', resource: 'permissions', action: 'read', description: 'Can view permissions' },
    { name: 'Update Permissions', resource: 'permissions', action: 'update', description: 'Can update permissions' },
    { name: 'Delete Permissions', resource: 'permissions', action: 'delete', description: 'Can delete permissions' },
    
    // Student management
    { name: 'Create Students', resource: 'students', action: 'create', description: 'Can create new students' },
    { name: 'Read Students', resource: 'students', action: 'read', description: 'Can view student information' },
    { name: 'Update Students', resource: 'students', action: 'update', description: 'Can update student information' },
    { name: 'Delete Students', resource: 'students', action: 'delete', description: 'Can delete students' },
    
    // Teacher management
    { name: 'Create Teachers', resource: 'teachers', action: 'create', description: 'Can create new teachers' },
    { name: 'Read Teachers', resource: 'teachers', action: 'read', description: 'Can view teacher information' },
    { name: 'Update Teachers', resource: 'teachers', action: 'update', description: 'Can update teacher information' },
    { name: 'Delete Teachers', resource: 'teachers', action: 'delete', description: 'Can delete teachers' },
    
    // Class management
    { name: 'Create Classes', resource: 'classes', action: 'create', description: 'Can create new classes' },
    { name: 'Read Classes', resource: 'classes', action: 'read', description: 'Can view class information' },
    { name: 'Update Classes', resource: 'classes', action: 'update', description: 'Can update class information' },
    { name: 'Delete Classes', resource: 'classes', action: 'delete', description: 'Can delete classes' },
    
    // Grade management
    { name: 'Create Grades', resource: 'grades', action: 'create', description: 'Can create grades' },
    { name: 'Read Grades', resource: 'grades', action: 'read', description: 'Can view grades' },
    { name: 'Update Grades', resource: 'grades', action: 'update', description: 'Can update grades' },
    { name: 'Delete Grades', resource: 'grades', action: 'delete', description: 'Can delete grades' },
    
    // Report management
    { name: 'Create Reports', resource: 'reports', action: 'create', description: 'Can create reports' },
    { name: 'Read Reports', resource: 'reports', action: 'read', description: 'Can view reports' },
    { name: 'Update Reports', resource: 'reports', action: 'update', description: 'Can update reports' },
    { name: 'Delete Reports', resource: 'reports', action: 'delete', description: 'Can delete reports' },
    
    // Analytics
    { name: 'View Analytics', resource: 'analytics', action: 'read', description: 'Can view system analytics' },
    
    // System settings
    { name: 'Manage Settings', resource: 'settings', action: 'update', description: 'Can manage system settings' },
  ];

  console.log('Creating permissions...');
  const createdPermissions = {};
  
  for (const permission of permissions) {
    const created = await prisma.permission.upsert({
      where: { 
        resource_action: { 
          resource: permission.resource, 
          action: permission.action 
        } 
      },
      update: {},
      create: permission
    });
    createdPermissions[`${permission.resource}_${permission.action}`] = created;
  }

  // Create default roles
  const roles = [
    {
      name: 'super_admin',
      description: 'Super Administrator with all permissions',
      permissions: Object.keys(createdPermissions) // All permissions
    },
    {
      name: 'school_admin',
      description: 'School Administrator with limited permissions',
      permissions: [
        'users_read', 'users_create', 'users_update',
        'students_read', 'students_create', 'students_update', 'students_delete',
        'teachers_read', 'teachers_create', 'teachers_update', 'teachers_delete',
        'classes_read', 'classes_create', 'classes_update', 'classes_delete',
        'grades_read', 'reports_read', 'reports_create',
        'analytics_read'
      ]
    },
    {
      name: 'teacher',
      description: 'Teacher with classroom management permissions',
      permissions: [
        'students_read', 'students_update',
        'classes_read', 'classes_update',
        'grades_read', 'grades_create', 'grades_update',
        'reports_read', 'reports_create'
      ]
    },
    {
      name: 'student',
      description: 'Student with limited view permissions',
      permissions: [
        'grades_read', 'classes_read'
      ]
    },
    {
      name: 'parent',
      description: 'Parent with view permissions for their children',
      permissions: [
        'students_read', 'grades_read', 'classes_read', 'reports_read'
      ]
    }
  ];

  console.log('Creating roles...');
  const createdRoles = {};
  
  for (const roleData of roles) {
    const role = await prisma.role.upsert({
      where: { name: roleData.name },
      update: { description: roleData.description },
      create: {
        name: roleData.name,
        description: roleData.description
      }
    });
    
    createdRoles[roleData.name] = role;

    // Clear existing permissions for this role
    await prisma.rolePermission.deleteMany({
      where: { roleId: role.id }
    });

    // Add permissions to role
    const rolePermissions = roleData.permissions.map(permKey => {
      const permission = createdPermissions[permKey];
      if (!permission) {
        console.warn(`Permission ${permKey} not found for role ${roleData.name}`);
        return null;
      }
      return {
        roleId: role.id,
        permissionId: permission.id
      };
    }).filter(Boolean);

    if (rolePermissions.length > 0) {
      await prisma.rolePermission.createMany({
        data: rolePermissions
      });
    }
  }

  // Create default super admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'superadmin',
      firstName: 'Super',
      lastName: 'Admin',
      password: hashedPassword,
      roleId: createdRoles.super_admin.id,
      adminProfile: {
        create: {
          adminLevel: 'super_admin',
          department: 'IT Administration'
        }
      }
    }
  });

  // Create sample school admin
  const schoolAdminPassword = await bcrypt.hash('school123', 12);
  
  const schoolAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'principal',
      firstName: 'Jane',
      lastName: 'Smith',
      password: schoolAdminPassword,
      roleId: createdRoles.school_admin.id,
      adminProfile: {
        create: {
          adminLevel: 'school_admin',
          department: 'Administration'
        }
      }
    }
  });

  // Create sample teacher
  const teacherPassword = await bcrypt.hash('teacher123', 12);
  
  const teacher = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'johndoe',
      firstName: 'John',
      lastName: 'Doe',
      password: teacherPassword,
      roleId: createdRoles.teacher.id,
      teacherProfile: {
        create: {
          employeeId: 'TCH001',
          department: 'Mathematics',
          qualification: 'M.Sc Mathematics',
          experience: 5
        }
      }
    }
  });

  // Create sample student
  const studentPassword = await bcrypt.hash('student123', 12);
  
  const student = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'alicejohnson',
      firstName: 'Alice',
      lastName: 'Johnson',
      password: studentPassword,
      roleId: createdRoles.student.id,
      studentProfile: {
        create: {
          studentId: 'STU001',
          dateOfBirth: new Date('2005-03-15'),
          address: '123 Main Street, City',
          emergencyContact: '+1-555-0123'
        }
      }
    }
  });

  // Create sample parent
  const parentPassword = await bcrypt.hash('parent123', 12);
  
  const parent = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'robertjohnson',
      firstName: 'Robert',
      lastName: 'Johnson',
      password: parentPassword,
      roleId: createdRoles.parent.id,
      parentProfile: {
        create: {
          occupation: 'Software Engineer',
          relationship: 'father'
        }
      }
    }
  });

  console.log('Database seeded successfully!');
  console.log('\nDefault Users Created:');
  console.log('Super Admin: <EMAIL> / admin123');
  console.log('School Admin: <EMAIL> / school123');
  console.log('Teacher: <EMAIL> / teacher123');
  console.log('Student: <EMAIL> / student123');
  console.log('Parent: <EMAIL> / parent123');
  
  console.log(`\nRoles created: ${Object.keys(createdRoles).length}`);
  console.log(`Permissions created: ${Object.keys(createdPermissions).length}`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });